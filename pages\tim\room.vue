<template>
	<view @tap="msgeditid = ''">
		<view class="content"  @tap="hideDrawer" >
			<scroll-view class="msg-list"  :class="{ listheight: currInq.status == 2 }" :style="{ height: scrollheight }"
				scroll-y="true" :scroll-with-animation="scrollAnimation" :scroll-top="scrollTop"
				:scroll-into-view="scrollToView" @scrolltoupper="loadHistory" upper-threshold="50" >
				<!-- 加载历史数据waitingUI -->
				<view class="loading" v-show="isHistoryLoading">
					<view class="spinner">
						<view class="rect1"></view>
						<view class="rect2"></view>
						<view class="rect3"></view>
						<view class="rect4"></view>
						<view class="rect5"></view>
					</view>
				</view>
				<view class="row" v-for="(item, index) in msgList" :key="index" :id="item.clientMsgID | getid">
					<!-- 用户消息 -->
					<view class="withdrawmsg" v-if="item.contentType == 2101">
						{{item.sendID == userInfo.userId?'您':toUserInfo.user}}撤回了一条消息
					</view>
					<block v-else>
						<!-- 自己发出的消息 -->
						<view class="my" v-if="item.sendID == userInfo.userId" @longtap="msgeditid = item.clientMsgID">
							<!-- 左-消息 -->
							<view class="left" >
								<!-- <view class="msg_edit" v-if="msgeditid == item.clientMsgID">
									<view class="edit" v-if="item.contentType==101"
										@tap='setboard(item.textElem.content)'>
										<view class="icon">
											<image src="../../static/img/copy.png" mode="widthFix"></image>
										</view>
										<text>复制</text>
									</view>
									<view class="edit" @tap="withdraw(item, index)" v-if="item.status == 2">
										<view class="icon">
											<image src="../../static/img/withdraw.png" mode="widthFix"></image>
										</view>
										<text>撤回</text>
									</view>
									<view class="edit" @tap="voiceToText(item, index)"
										v-if="item.contentType == 103||(item.contentType == 110&&item.customElem.description=='sound' )">

										<text>转文字</text>
									</view>
								</view> -->
								<view class="username">
									<view class="time">{{ timeFliter(item.sendTime) }}</view>
								</view>

								<view class="fail" @click="resent(item)" v-if="item.status == 3">
									<image src="../../static/img/msg_fail.png" mode="widthFix"></image>
								</view>
								<view class="loading" v-if="item.status==1">
									发送中
								</view>
								<PopoverPopup :buttons="myButtons" :data="item">										
											<!-- 文字消息 -->
										<view v-if="item.contentType == 101" class="bubble" ><text-element
												:payload="item"></text-element></view>
										<!-- 语音消息 -->
										<view
											v-if="item.contentType == 103||(item.contentType == 110&&item.customElem.description=='sound')"
											class="bubble voice" @tap="playVoice(item)"
											:class="playMsgid == item.clientMsgID ? 'play' : ''">
											<view class="icon other-voice"></view>
											<view class="length">{{ item | voiceTime }}</view>
										</view>
										<!-- 语音消息转文字 -->
										<view class="voice-text"
											v-if="(item.contentType == 103||(item.contentType == 110&&item.customElem.description=='sound') && item.voiceToText )">
											{{item.voiceToText || ''}}
										</view>
										<!-- 图片消息 -->
										<view
											v-if="item.contentType == 102||(item.contentType == 110&&item.customElem.description=='img')"
											class="bubble img">
											<image :src="item|getUrl" mode="" @tap="showPic(item)" mode="widthFix">
											</image>
										</view>
										<!-- 自定义消息 -->
										<!-- 健康档案 -->
										<view class="bubble custom" style="background-color: #f8f8f8;"
											v-if="item.contentType == 110&&item.customElem.description=='health_file'">
											<image class="health" v-if="isHealth_File(item)" src="/static/img/health.png"
												mode="" @tap="toHealth(item)"></image>
										</view>
								</PopoverPopup>
								
							</view>
							<!-- 右-头像 -->
							<view class="right">
								<image :src="userInfo.img"></image>
							</view>
						</view>

						<!-- 别人发出的消息 -->
						<view class="other" v-else @longtap="msgeditid = item.clientMsgID">
							<!-- 左-头像 -->
							<view class="left">
								<image :src="toUserInfo.img"></image>
							</view>
							<!-- 右-用户名称-时间-消息 -->
							<view class="right">
								<!-- <view class="msg_edit" v-if="msgeditid == item.clientMsgID">
									<view class="edit" v-if="item.contentType==101"
										@tap='setboard(item.textElem.content)'>
										<view class="icon">
											<image src="../../static/img/copy.png" mode="widthFix"></image>
										</view>
										<text>复制</text>
									</view>
									<view class="edit" @tap="voiceToText(item, index)"
										v-if="item.contentType == 103||(item.contentType == 110&&item.customElem.description=='sound' )">

										<text>转文字</text>
									</view>
								</view> -->
								<view class="username">
									<view class="name">{{ toUserInfo.user }}</view>
									<view class="time">{{ timeFliter(item.sendTime) }}</view>
								</view>
								<PopoverPopup :buttons="otherButtons" :data="item" xPosition="left">
										<!-- 文字消息 -->
										<view v-if="item.contentType == 101" class="bubble"><text-element
												:payload="item"></text-element></view>
										<!-- 语音消息 -->
										<view
											v-if="item.contentType == 103||(item.contentType == 110&&item.customElem.description=='sound')"
											class="bubble voice" @tap="playVoice(item)"
											:class="playMsgid == item.clientMsgID ? 'play' : ''">
											<view class="icon other-voice"></view>
											<view class="length">{{ item | voiceTime }}</view>
										</view>
										<!-- 语音消息转文字 -->
										<view class="voice-text"
											v-if="(item.contentType == 103||(item.contentType == 110&&item.customElem.description=='sound') && item.voiceToText )">
											{{item.voiceToText || ''}}
										</view>
										<!-- 图片消息 -->
										<view
											v-if="item.contentType == 102||(item.contentType == 110&&item.customElem.description=='img')"
											class="bubble img">
											<image :src="item|getUrl" mode="" @tap="showPic(item)" mode="widthFix">
											</image>
										</view>
										<!-- 健康档案消息 -->
										<view v-if="item.contentType == 110&&item.customElem.description=='health_file'"
											class="bubble custom" style="background-color: #f8f8f8;">
											<image class="health" v-if="isHealth_File(item)" src="/static/img/health.png"
												mode="" @tap="toHealth(item)"></image>
										</view>
								</PopoverPopup>
							</view>
						</view>
					</block>
				</view>
			</scroll-view>
		</view>
		<!-- 抽屉栏 -->
		<view class="popup-layer" :class="popupLayerClass" @touchmove.stop.prevent="discard">
			<!-- 表情 -->
			<swiper class="emoji-swiper" :class="{ hidden: hideEmoji }" indicator-dots="true" duration="150">
				<swiper-item v-for="(page, pid) in emojiNamepage" :key="pid">
					<view v-for="(em, eid) in page" :key="eid" @tap="addEmoji(em)">
						<image mode="widthFix" :src="emojiUrl + emojiMap[em]"></image>
					</view>
				</swiper-item>
			</swiper>
			<!-- 更多功能 相册-拍照 -->
			<view class="more-layer" :class="{ hidden: hideMore }">
				<view class="list">
					<view class="box" @tap="chooseImage">
						<view class="imagesText">
							<view class="img_box">
								<view>
									<image src="../../static/img/chat-icon/icon2.png" mode="widthFix"
										class="imagesSize"></image>
								</view>
							</view>
							<text>打开图库</text>
						</view>
					</view>
					<view class="box" @tap="camera">
						<view class="imagesText">
							<view class="img_box">
								<view>
									<image src="../../static/img/chat-icon/icon3.png" mode="widthFix"
										class="imagesSize"></image>
								</view>
							</view>
							<text>打开相机</text>
						</view>
					</view>
					<view class="box" @tap="toHealth('create')" v-if="this.toUserInfo.userId != 'xxjk_serv'">
						<view class="imagesText">
							<view class="img_box">
								<view>
									<image src="../../static/img/chat-icon/icon6.png" mode="widthFix"
										class="imagesSize"></image>
								</view>
							</view>
							<text>发送档案</text>
						</view>
					</view>
					<!-- <view class="box" @tap="quickMsg">
						<view class="imagesText">
							<view class="img_box">
								<view>
									<image src="../../static/img/chat-icon/icon1.png" mode="widthFix"
										class="imagesSize"></image>
								</view>
							</view>
							<text>快捷消息</text>
						</view>
					</view> -->
				</view>
			</view>
			<!-- 快捷消息 -->
			<view class="quickMsg" :class="{ hidden: hideQuick }">
				<scroll-view scroll-y="true" class="msg_list">
					<view class="msg_item" v-for="n in quickList" @tap="sendQuick(n)">
						<view class="text">{{ n.text }}</view>
						<view class="operation">
							<view class="box edit">
								<image src="../../static/img/editor.png" mode="widthFix" @tap.stop="goedit"></image>
							</view>
							<view class="box del">
								<image src="../../static/img/delete.png" mode="widthFix"></image>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
		<!-- 底部输入栏 -->

		<view v-show="currInq.status == 1 || currInq.family_status == 1 || toUserId == 'xxjk_serv'"  class="input-box"
			:class="popupLayerClass" @touchmove.stop.prevent="discard">
			<!-- H5下不能录音，输入栏布局改动一下 -->
			<!-- #ifndef H5 -->
			<view class="voice">
				<view class="icon" :class="isVoice ? 'jianpan' : 'yuyin'" @tap="switchVoice"></view>
			</view>
			<!-- #endif -->
			<!-- #ifdef H5 -->
			<view class="more" @tap="showMore">
				<view class="icon add"></view>
			</view>
			<!-- #endif -->
			<view class="textbox">
				<view class="voice-mode" :class="[isVoice ? '' : 'hidden', recording ? 'recording' : '']"
					@touchstart="voiceBegin" @touchmove.stop.prevent="voiceIng" @touchend="voiceEnd"
					@touchcancel="voiceCancel">
					{{ voiceTis }}
				</view>
				<view class="text-mode" :class="isVoice ? 'hidden' : ''">
					<view class="box">
						<textarea class="textareaAuto" auto-height="true" fixed="true" cursor-spacing="20"
							v-model.trim="textMsg" @keyboardheightchange="keyboardheightchange" @input="monitorLength"
							maxlength="500" />
					</view>
					<view class="em" @tap="chooseEmoji">
						<view class="icon biaoqing"></view>
					</view>
				</view>
			</view>
			<!-- #ifndef H5 -->
			<view class="more" @tap="showMore">
				<view class="icon add"></view>
			</view>
			<!-- #endif -->
			<view class="send" :class="isVoice ? 'hidden' : ''" @tap="sendText">
				<view class="btn">发送</view>
			</view>
		</view>
		<view class="endtip" v-if="toUserInfo.isdel">
			<view class="tip">温馨提示，此用户账号已被注销或停用</view>
		</view>
		<view
			v-else-if="currInq.status == 2 && currInq.ischarge == 3 && (currInq.family_status == 2 || currInq.family_status == 0)"
			class="endtip">
			<view class="tip">温馨提示，问诊已取消，如需继续联系该医生，请再次购买</view>
			<view class="flex-end-center">
				<navigator :url="'../doctors/doctorsDetails?id=' + currInq.doctid"><button type="default"
						class="btn">再次购买</button></navigator>
			</view>
		</view>
		<view v-else-if="currInq.status == 2 && (currInq.family_status == 2 || currInq.family_status == 0)"
			class="endtip">
			<view class="tip">温馨提示，问诊时间已结束，如需继续联系该医生，请再次购买</view>
			<view class="flex-end-center">
				<navigator :url="'../doctors/doctorsDetails?id=' + currInq.doctid"><button type="default"
						class="btn">再次购买</button></navigator>
			</view>
		</view>
		<!-- 录音UI效果 -->
		<view class="record" :class="recording ? '' : 'hidden'">
			<view class="ing" :class="willStop ? 'hidden' : ''">
				<view class="icon luyin2"></view>
			</view>
			<view class="cancel" :class="willStop ? '' : 'hidden'">
				<view class="icon chehui"></view>
			</view>
			<view class="tis" :class="willStop ? 'change' : ''">{{ recordTis }}</view>
		</view>
		<!-- <PSM v-if="showpsm" :tipwords="tipwords" :storagename="storagename"></PSM> -->
		<view v-if="currInq.family_status == 1 && showovertime" class="family_endtime">
			私人医生服务将在{{ currInq.family_enddate }}结束</view>
	</view>
</template>
<script>
	import {
		mapState
	} from 'vuex';
	import moment from 'moment';
	import {
		emojiName,
		emojiMap,
		emojiUrl
	} from '../../common/commen.js';
	import TextElement from './text-element.vue';
	import PSM from '../../components/prompt-save-msg/prompt-save-msg.vue';
import PopoverPopup from '../../components/popover-popup/popover-popup.vue';
	export default {
		components: {
			TextElement,
			PSM,
			PopoverPopup
		},
		data() {
			return {
				scrollheight: 'auto',
				//TIM变量
				conversationActive: null,
				toUserId: '',
				toUserInfo: null,
				userInfo: null,
				nextReqMessageID: '',
				count: 15,
				isCompleted: '',
				msgList: [],
				TIM: null,
				currInq: null, //当前问诊
				//文字消息
				textMsg: '',
				//消息列表
				isHistoryLoading: false,
				scrollAnimation: false,
				scrollTop: 0,
				scrollToView: '',

				msgImgList: [],
				myuid: 0,
				recorderr: false,
				// recorddisabled: false,

				//录音相关参数
				// #ifndef H5
				//H5不能录音
				RECORDER: uni.getRecorderManager(),
				// #endif
				isVoice: false,
				voiceTis: '按住 说话',
				recordTis: '手指上滑 取消发送',
				recording: false,
				willStop: false,
				initPoint: {
					identifier: 0,
					Y: 0
				},
				recordBeginTime: 0,
				recordEndTime: 0,
				recordLength: 0,

				//播放语音相关参数
				AUDIO: uni.createInnerAudioContext(),
				// 录音部分参数
				recordOptions: {
					duration: 60000, // 录音的时长，单位 ms，最大值 600000（10 分钟）
					sampleRate: 44100, // 采样率
					numberOfChannels: 1, // 录音通道数
					encodeBitRate: 192000, // 编码码率
					format: 'mp3' // 音频格式，选择此格式创建的音频消息，可以在即时通信 IM 全平台（Android、iOS、微信小程序和Web）互通
				},
				playMsgid: null,
				VoiceTimer: null,
				// 抽屉参数
				popupLayerClass: '',
				// more参数
				hideMore: true,
				// 快捷消息
				hideQuick: true,
				//表情定义
				hideEmoji: true,
				emojiList: this.$commen.emojiList,
				emojiUrl: emojiUrl,
				emojiMap: emojiMap,
				emojiName: emojiName,
				emojiNamepage: this.$commen.getemojilist(),
				quickList: [{
						text: '请问我上次的病情什么时候可以出结果？'
					},
					{
						text: '医生，您好，请问上次复诊的结果出来了吗？'
					},
					{
						text: '请问诊所几点上班下班？'
					},
					{
						text: '请问我上次的病情什么时候可以出结果？'
					},
					{
						text: '医生，您好，请问上次复诊的结果出来了吗？'
					},
					{
						text: '请问诊所几点上班下班？'
					}
				],
				// 提示语
				tipwords: '您的消息记录只能在数据库保存七天，请将重要数据保存于病例或本地！！！',
				// 存储名称
				storagename: 'stopPsm',
				showpsm: true,
				lastMsg: '',
				operationID: '',
				msgeditid: '',
				showovertime: false,
				conversationID: '',
				lastMinSeq: 0,
				session: {
					id: 0, // 问诊id
					status: 1 // 当前咨询状态 0--医生未接诊，1--医生接诊中，2--医生接诊结束
				},
				myButtons: Object.freeze([
					{
						icon: '../../static/img/copy.png',
						label: '复制',
						action: (data) => {
									this.setboard(data.textElem.content)
						},
						isShow: (data) => {
									return data.contentType==101 && this.msgeditid === data.clientMsgID
						}
					},
					{
						icon: '../../static/img/withdraw.png',
						label: `撤回`,
						action: (data) => {
							this.withdraw(data, this.msgList.indexOf(data));
						},
						isShow: (data) => {
							return data.status == 2 && data.sendID == this.userInfo.userId
						}
					},
					{
						label: '转文字',
						action: (data) => {
							this.voiceToText(data, this.msgList.indexOf(data));
						},
						isShow: (data) => {
							return (data.contentType == 103 || (data.contentType == 110 && data.customElem.description == 'sound')) && this.msgeditid === data.clientMsgID
						}
					}
				]),
				otherButtons: Object.freeze([
					{
						label: '复制',
						icon: '../../static/img/copy.png',
						action: (data) => {
									this.setboard(data.textElem.content)
						},
						isShow: (data) => {
									return data.contentType==101 && this.msgeditid === data.clientMsgID
						}
					},
					{
						label: '转文字',
						action: (data) => {
							this.voiceToText(data, this.msgList.indexOf(data));
						},
						isShow: (data) => {
							return (data.contentType == 103 || (data.contentType == 110 && data.customElem.description == 'sound')) && this.msgeditid === data.clientMsgID
						}
					}
				])
			};
		},
		onNavigationBarButtonTap(e) {
			if (e.index == 0) {
				uni.navigateTo({
					url: './set'
				});
			}
		},
		mounted() {
			this.srolltobottom()
		},
		computed: {
			...mapState({
				currentMessageList: state => state.currentMessageList
			})
		},
		watch: {
			msgList(newVal, oldVal) {
				this.msgImgList = [];
				this.msgList.forEach(n => {
					if (n.contentType == 102) {
						this.msgImgList.push(n.pictureElem.sourcePicture.url);
					}
					if (n.contentType == 110 && n.customElem.description == 'img') {
						this.msgImgList.push(JSON.parse(n.customElem.data).url);
					}
				});
			},
			showovertime() {
				let that = this;
				let time = setTimeout(function() {
					that.showovertime = false;
					clearTimeout(time);
				}, 4000);
			}
		},
		filters: {
			getUrl(data) {
				let url
				if (data.contentType == 102) {
					url = data.pictureElem.sourcePicture.url;
				} else if (data.contentType == 110) {
					url = JSON.parse(data.customElem.data).url
				}
				return url
			},
			voiceTime(data) {
				let time
				if (data.contentType == 103) {
					time = data.soundElem.duration
				} else if (data.contentType == 110) {
					time = JSON.parse(data.customElem.data).duration
				}
				if (time < 10) {
					return '00:0' + time;
				} else if (time < 60) {
					return '00:' + time;
				} else {
					return "0" + parseInt(time / 60) + ':' + ((time % 60) < 10 ? '0' + (time % 60) : time)
				}
			},
			getid(clientMsgID) {
				return 'id_' + clientMsgID.substring(0, 6);
			}
		},
		created() {
			let showpop = uni.getStorageSync(this.storagename);
			if (showpop) {
				this.showpsm = false;
			}
			this.operationID = this.$store.state.operationID;
			this.showovertime = true;
		},
		onLoad(option) {
			this.operationID = this.$store.state.operationID
			this.conversationID = option.conversationID
			this.session.id = option.id
			this.session.status = option.status
			let globaluserInfo = this.$store.state.userInfo;
			let obj = {
				user: globaluserInfo.patname,
				userId: globaluserInfo.imname,
				img: globaluserInfo.face,
				userSig: ''
			};
			this.userInfo = obj;
			this.toUserId = this.$store.state.toUserId;

			this.conversationActive = this.$store.state.conversationActive;
			// this.TIM = this.$TIM;
			//获取聊天对象的用户信息
			// console.log(option)

			let tmp = JSON.parse(decodeURIComponent(option.toUserInfo));
			// console.log(tmp)
			this.currInq = tmp;
			let obj2 = {
				user: tmp.doctname,
				userId: tmp.username == 'xxjk_serv' ? 'xxjk_serv' : tmp.imname,
				isdel: tmp.isdel
				// img: tmp.face ? this.$baseUrl + tmp.face : '/static/img/default.png'
			};
			if (tmp.face) {
				obj2.img = this.$baseUrl + tmp.face;
			} else {
				if (tmp.gender == 0) {
					obj2.img = '/static/img/defaultgirl.png';
				} else if (tmp.gender == 1) {
					obj2.img = '/static/img/defaultman.png';
				}
			}
			if (obj2.userId == 'xxjk_serv') {
				uni.setNavigationBarTitle({
					title: '小欣客服'
				});
			} else {
				uni.setNavigationBarTitle({
					title: obj2.user
				});
			}
			this.toUserInfo = obj2;
			// 获取会话信息
			// 获取消息列表
			this.getMsgList(tmp);
			this.$IMSDK.subscribe(this.$IMSDK.IMEvents.OnRecvNewMessages, (data) => {
				// data 新消息
				for (let message of data.data) {
					if (message.sendID == this.toUserId || message.recvID == this.toUserId) {
						this.msgList.push(message)
						this.srolltobottom()
					}
				}
			});
			this.$IMSDK.subscribe(this.$IMSDK.IMEvents.OnKickedOffine, () => {
				console.log('被踢下线：', data)
				this.willStop = true;
				this.RECORDER.stop();
			})
			// 收到撤回消息
			this.$IMSDK.subscribe(this.$IMSDK.IMEvents.OnNewRecvMessageRevoked, (data) => {
				console.log(data)
				let currentIndex = (this.msgList || []).findIndex((msg) => msg.clientMsgID === data.data
					.clientMsgID);
				if (currentIndex >= 0) {
					let message = this.msgList[currentIndex]
					message.contentType = 2101
					console.log(message)
					this.$set(this.msgList, currentIndex, message)
				}

			});
			//语音自然播放结束
			this.AUDIO.onEnded(res => {
				this.playMsgid = null;
			});
			//录音开始事件
			this.RECORDER.onStart(e => {
				if (this.willStop) {
					return;
				}
				this.recordBegin(e);
			});
			//录音结束事件
			this.RECORDER.onStop(e => {
				this.recordEnd(e);
			});
		},
		onShow() {
			let that = this
			console.log(this.conversationID)
			this.$IMSDK.asyncApi('markConversationMessageAsRead', Date.now().toString(), this.conversationID)
				.then((
					data
				) => {
					// 调用成功
					console.log('markConversationMessageAsRead', data)
				})
				.catch(({
					errCode,
					errMsg
				}) => {
					// 调用失败
					console.log('markConversationMessageAsRead', errCode, errMsg)
				});
			uni.getStorage({
				key: 'healthData',
				success: res => {
					// console.log(res)
					uni.removeStorage({
						key: 'healthData'
					});
					this.sendMsg(res.data, 'custom');
				}
			});
			this.scrollTop = this.msgList.length * 1000;
		},
		onUnload() {
			//退出页面 将所有的会话内的消息设置为已读
			console.log(this.toUserId)
			this.$IMSDK.asyncApi('markConversationMessageAsRead', Date.now().toString(), this.conversationID)
				.then(({
					data
				}) => {
					// 调用成功
					console.log(data)
				})
				.catch(({
					errCode,
					errMsg
				}) => {
					// 调用失败
				});
			if (this.playMsgid) {
				this.AUDIO.stop();
				this.playMsgid = null;
			}
		},
		methods: {
			//复制文字到剪切板
			setboard(text) {
				uni.setClipboardData({
					data: text,
					success: function() {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						});
					}
				});
			},
			//时间过滤
			timeFliter(timer) {
				let timeData = new Date(timer);
				let str = this.$commen.dateTimeFliter(timeData);
				return str;
			},

			//触发滑动到顶部(加载历史信息记录)
			loadHistory(e) {
				let _this = this
				// 更多消息列表
				if (this.isHistoryLoading) {
					return;
				}
				this.isHistoryLoading = true; //参数作为进入请求标识，防止重复请求
				this.scrollAnimation = false; //关闭滑动动画

				this.$IMSDK.asyncApi('getAdvancedHistoryMessageList', Date.now().toString(), {
						lastMinSeq: this.lastMinSeq,
						count: 20,
						startClientMsgID: this.msgList[0].clientMsgID,
						conversationID: this.conversationID,
					})
					.then(({
						data
					}) => {
						// 调用成功
						this.lastMinSeq = data.lastMinSeq
						let getList = data.messageList;
						if (getList.length == 0) {
							_this.isHistoryLoading = false; //参数作为进入请求标识，防止重复请求
							_this.scrollAnimation = true;
							return;
						}
						let scrollToId = "id_" + getList[getList.length - 1].clientMsgID.substring(0,
							6)
						Array.prototype.push.apply(getList, _this.msgList);
						_this.msgList = getList;
						_this.isHistoryLoading = false;
						_this.$nextTick(function() {
							_this.scrollToView = scrollToId;
							_this.scrollAnimation = true; //恢复滚动动画
						});
					})
					.catch((err) => {
						// 调用失败
						uni.showToast({
							title: '获取消息列表失败',
							icon: 'none'
						})
					});
				//这段代码很重要，不然每次加载历史数据都会跳到顶部
			},
			// 加载初始页面消息
			getMsgList(data) {
				let that = this
				this.$IMSDK.asyncApi('getAdvancedHistoryMessageList', Date.now().toString(), {
						lastMinSeq: 0,
						count: 20,
						startClientMsgID: '',
						conversationID: this.conversationID,
					})
					.then(({
						data
					}) => {
						let list = data.messageList
						this.lastMinSeq = data.lastMinSeq
						for (let item of list) {
							if (item.status == 1) {
								item.status = 3
							}
						}
						this.msgList = list
					})
					.catch(({
						errCode,
						errMsg
					}) => {
						// 调用失败
						uni.showToast({
							title: '获取消息列表失败',
							icon: 'none'
						})
					});
				this.srolltobottom()
				this.$forceUpdate();
			},
			//处理图片尺寸，如果不处理宽高，新进入页面加载图片时候会闪
			setPicSize(content) {
				// 让图片最长边等于设置的最大长度，短边等比例缩小，图片控件真实改变，区别于aspectFit方式。
				let maxW = uni.upx2px(350); //350是定义消息图片最大宽度
				let maxH = uni.upx2px(350); //350是定义消息图片最大高度
				if (content.w > maxW || content.h > maxH) {
					let scale = content.w / content.h;
					content.w = scale > 1 ? maxW : maxH * scale;
					content.h = scale > 1 ? maxW / scale : maxH;
				}
				return content;
			},
			// 打开抽屉
			openDrawer() {
				this.popupLayerClass = 'showLayer';
				let that = this
				uni.getSystemInfo({
					success: res => {
						that.scrollheight = (res.windowHeight - uni.upx2px(100) - (res.windowWidth / 100 *
							42)) + 'px'
					}
				});
				this.$nextTick(function() {
					//进入页面滚动到底部
					this.scrollTop = this.scrollTop + 1;
					this.scrollAnimation = true;
				});
			},
			// 隐藏抽屉
			hideDrawer() {
				this.scrollheight = 'auto';
				this.popupLayerClass = '';
				this.showovertime = true;
				setTimeout(() => {
					this.hideMore = true;
					this.hideEmoji = true;
					this.hideQuick = true;
				}, 150);
			},
			// 选择图片发送
			chooseImage() {
				if (uni.getSystemInfoSync().platform == 'andriod') {
					getApp().requestAndroidPermission('android.permission.READ_EXTERNAL_STORAGE', '相册读取权限')
				}
				this.getImage('album');
			},
			//拍照发送
			camera() {
				if (uni.getSystemInfoSync().platform == 'ios') {
					getApp().requestIosPermission('camera', '摄像头权限')
				} else {
					getApp().requestAndroidPermission('android.permission.CAMERA', '摄像头权限')
				}

				this.getImage('camera');
			},
			//选照片 or 拍照

			getImage(type) {
				this.hideDrawer();
				let that = this
				uni.chooseImage({
					count: 1,
					sourceType: [type],
					sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
					success: (res) => {
						let imageinfo = res
						uni.getImageInfo({
							src: res.tempFilePaths[0],
							success: function(image) {
								let now = new Date().getTime();
								// 命名规则 发送账号id+时间戳+六位随机数+图片格式
								let name =
									that.userInfo.userId +
									'_' +
									now +
									'_' +
									Math.random()
									.toString()
									.slice(-6) +
									'.' +
									res.tempFilePaths[0].split('.').slice(-1)[0];
								console.log(imageinfo)
								uniCloud.uploadFile({
										cloudPath: name,
										filePath: res.tempFilePaths[0]
									})
									.then(res => {
										console.log(res)
										let uploadinfo = res
										if (that.$store.state.yunstore.yunstore != undefined) {
											let changTld = that.$store.state.yunstore.yunstore;
											uploadinfo.fileID = uploadinfo.fileID.replace(
												changTld, that.$store
												.state.yunstore.yunstorebk)
										}
										uploadinfo.type = imageinfo.tempFilePaths[0].split('.')
											.slice(-1)[0]
										uploadinfo.size = imageinfo.tempFiles[0].size
										uploadinfo.width = image.width
										uploadinfo.height = image.height
										that.sendMsg(uploadinfo, 'img');
									})
									.catch(err => {
										console.log(err);
										uni.showToast({
											title: '图片文件上传失败，请重新上传',
											icon: 'none'
										})
									});
							}
						});
					},
					fail(err) {
						if (type == "album" && err.errCode == 1) {
							uni.showModal({
								title: '权限设置错误',
								content: '您的权限设置有误，可能会影响到功能的正常使用，请前往设置相册权限，如果权限设置完成仍无法正常使用，请重启app',
								confirmText: '前往设置权限',
								success(res) {
									if (res.confirm) {
										if (uni.getSystemInfoSync().platform == 'ios') {
											var UIApplication = plus.ios.import("UIApplication");
											var application2 = UIApplication.sharedApplication();
											var NSURL2 = plus.ios.import("NSURL");
											// var setting2 = NSURL2.URLWithString("prefs:root=LOCATION_SERVICES");		
											var setting2 = NSURL2.URLWithString("app-settings:");
											application2.openURL(setting2);

											plus.ios.deleteObject(setting2);
											plus.ios.deleteObject(NSURL2);
											plus.ios.deleteObject(application2);
										} else {
											// console.log(plus.device.vendor);
											var Intent = plus.android.importClass(
												"android.content.Intent");
											var Settings = plus.android.importClass(
												"android.provider.Settings");
											var Uri = plus.android.importClass("android.net.Uri");
											var mainActivity = plus.android.runtimeMainActivity();
											var intent = new Intent();
											intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
											var uri = Uri.fromParts("package", mainActivity
												.getPackageName(), null);
											intent.setData(uri);
											mainActivity.startActivity(intent);
										}
									}
								}
							})
						}
					}
				});
			},
			// 选择表情
			chooseEmoji() {
				this.hideMore = true;
				if (this.hideEmoji) {
					this.hideEmoji = false;
					this.openDrawer();
				} else {
					this.hideDrawer();
				}
			},

			//更多功能(点击+弹出)
			showMore() {
				this.isVoice = false;
				this.hideEmoji = true;
				if (this.hideMore) {
					this.hideMore = false;
					this.openDrawer();
				} else {
					this.hideDrawer();
				}
			},
			//添加表情
			addEmoji(em) {
				if (this.textMsg.length < 496) {
					this.textMsg += em;
				} else {
					uni.showToast({
						title: '消息只能输入500字！',
						icon: 'none'
					});
				}
			},
			// 发送文字消息
			sendText() {
				let that = this;
				this.hideDrawer(); //隐藏抽屉
				if (!this.textMsg) {
					uni.showToast({
						title: '发送内容不能为空',
						icon: 'none',
						mask: true
					});
					this.textMsg = '.'; //只换行不输入内容无法清空，所以要先赋值
					setTimeout(function() {
						that.textMsg = '';
					}, 100);
					return;
				}
				let msg = {
					text: this.textMsg
				};
				// this.textMsg = ''; //清空输入框
				this.sendMsg(msg, 'text');
				setTimeout(function() {
					that.textMsg = '';
				}, 100);
			},
			// 发送消息
			sendMsg(content, type, description) {
				if (type == "text") {
					console.log(content)
					this.$IMSDK.asyncApi('createTextMessage', Date.now().toString(), content.text)
						.then((
							data
						) => {
							// 调用成功
							this.IMsend(data, type)
						})
				} else if (type == "img") {
					let now = new Date().getTime();
					let uuidname =
						this.userInfo.userId +
						'_' +
						now +
						'_' +
						Math.random()
						.toString()
						.slice(-6) +
						'image_file';
					let sourceInfo = {
						//原图片信息
						uuid: uuidname,
						type: content.type,
						size: content.size,
						width: content.width,
						height: content.height,
						url: content.fileID
					}
					sourceInfo = JSON.stringify(sourceInfo)
					console.log(sourceInfo)
					// 用自定义消息发图片与语音
					this.$IMSDK.asyncApi('createCustomMessage', Date.now().toString(), {
							data: sourceInfo,
							extension: sourceInfo,
							description: 'img',
						})
						.then((
							data
						) => {
							// 调用成功
							this.IMsend(data, 'custom')
						})
					// this.$IMSDK.asyncApi(
					// 		'createImageMessageByURL',
					// 		Date.now().toString(), {
					// 			sourcePicture: sourceInfo,
					// 			bigPicture: sourceInfo,
					// 			snapshotPicture: sourceInfo
					// 		}
					// 	)
					// 	.then((
					// 		data
					// 	) => {
					// 		// 调用成功
					// 		this.IMsend(data, type)
					// 	})

				} else if (type == "voice") {
					let time = parseInt(content.length.split(':')[0]) * 60 + parseInt(content.length.split(':')[1]);
					let now = new Date().getTime();
					let uuidname =
						this.userInfo.userId +
						'_' +
						now +
						'_' +
						Math.random()
						.toString()
						.slice(-6) +
						'sound_file';
					let soundInfo = {
						uuid: uuidname,
						soundPath: '',
						sourceUrl: content.file,
						dataSize: 1000,
						duration: time,
						soundType: 'mp3'
					};
					soundInfo = JSON.stringify(soundInfo)
					this.$IMSDK.asyncApi('createCustomMessage', Date.now().toString(), {
							data: soundInfo,
							extension: soundInfo,
							description: 'sound',
						})
						.then((
							data
						) => {
							// 调用成功
							this.IMsend(data, 'custom')
						})
					// this.$IMSDK.asyncApi('createSoundMessageByURL', Date.now().toString(), soundInfo)
					// 	.then((
					// 		data
					// 	) => {
					// 		// 调用成功
					// 		this.IMsend(data, type)
					// 	})
				} else if (type == "custom") {
					let msgtype = 'health_file'
					if (description) {
						msgtype = description
					}
					this.$IMSDK.asyncApi('createCustomMessage', Date.now().toString(), {
							data: content,
							extension: content,
							description: msgtype,
						})
						.then((
							data
						) => {
							// 调用成功
							this.IMsend(data, type)
						})
				}
			},
			// 预览图片
			showPic(data) {
				let url
				if (data.contentType == 102) {
					url = data.pictureElem.sourcePicture.url
				} else if (data.contentType == 110) {
					url = JSON.parse(data.customElem.data).url
				}
				uni.previewImage({
					indicator: 'none',
					current: url,
					urls: this.msgImgList
				});
			},
			// 播放语音
			playVoice(data) {
				if (this.playMsgid) {
					this.AUDIO.stop();
					this.playMsgid = null;
				} else {
					this.playMsgid = data.clientMsgID;
					if (data.contentType == 103) {
						this.AUDIO.src = data.soundElem.sourceUrl;
					} else if (data.contentType == 110) {
						this.AUDIO.src = JSON.parse(data.customElem.data).sourceUrl
					}

					this.$nextTick(function() {
						this.AUDIO.play();
					});
				}
			},
			// 语音转文字-需要使用第三方API付费-患者端略
			async voiceToText(data, index) {
				const url = JSON.parse(data.customElem.data).sourceUrl
				console.log('voiceToText', data, index)
				let result = uni.getStorageSync(url);
				console.warn('测试转文字', result);
				if (!result) {
					this.$api
						.post('api/patient/speechtotext/detail', {
							url,
							type: 1,
							id: this.session.id
						}).then(res => {
							if (res.code == '0000') {
								result = res.data.text
								uni.setStorageSync(url, result);
								this.$set(data, 'voiceToText', result);
							} else {
								result = ''
							}
						})

				} else {
					console.warn('测试转文字', result);
					this.$set(data, 'voiceToText', result);
				}
			},
			// 录音开始
			async voiceBegin(e) {
				let that = this
				let result
				if (uni.getSystemInfoSync().platform == 'ios') {
					result = await getApp().requestIosPermission('record', '麦克风权限')
					if (result == **********) {
						that.RECORDER.start()
						that.RECORDER.stop()
					}
				} else {
					result = await getApp().requestAndroidPermission('android.permission.RECORD_AUDIO', '麦克风权限')
				}
				console.log(result)
				if (result) {
					if (this.recording) {
						uni.showToast({
							title: "使用太频繁，请稍等一下~",
							icon: 'none'
						})
						return
					}
					that.recording = true;
					if (that.recorderr) {
						that.recorderr = false;
						that.willStop = false;
						that.recording = false;
						return;
					}
					if (that.playMsgid) {
						that.AUDIO.stop();
						that.playMsgid = null;
					}
					if (e.touches.length > 1) {
						return;
					}
					that.initPoint.Y = e.touches[0].clientY;
					that.initPoint.identifier = e.touches[0].identifier;
					that.RECORDER.start(that.recordOptions); //录音开始,
					console.log('that.RECORDER.start')
				}
			},
			//录音开始UI效果
			recordBegin(e) {
				this.voiceTis = '松开 结束';
				this.recordLength = 0;
				this.recordBeginTime = new Date().getTime();
			},
			// 录音被打断
			voiceCancel() {
				this.voiceTis = '按住 说话';
				this.recordTis = '手指上滑 取消发送';
				this.willStop = true; //不发送录音
				this.RECORDER.stop(); //录音结束
				this.recorderr = true;
				// this.recorddisabled = false;
			},
			// 录音中(判断是否触发上滑取消发送)
			voiceIng(e) {
				if (!this.recording) {
					return;
				}
				let touche = e.touches[0];
				//上滑一个导航栏的高度触发上滑取消发送
				if (this.initPoint.Y - touche.clientY >= uni.upx2px(100)) {
					this.willStop = true;
					this.recordTis = '松开手指 取消发送';
				} else {
					this.willStop = false;
					this.recordTis = '手指上滑 取消发送';
				}
			},
			// 结束录音
			voiceEnd(e) {
				let that = this;
				if (!this.recording) {} else {
					this.recorderr = true;
					this.voiceTis = '按住 说话';
					this.recordTis = '手指上滑 取消发送';
					this.RECORDER.stop();
				}
			},
			//录音结束(回调文件)
			recordEnd(e) {
				this.voiceTis = '按住 说话';
				this.recordTis = '手指上滑 取消发送';
				this.recordEndTime = new Date().getTime();
				this.recordLength = parseInt((this.recordEndTime - this.recordBeginTime) / 1000);
				this.recordBeginTime = 0;
				this.recordEndTime = 0;
				if (!this.willStop) {
					if (this.recordLength >= 1) {
						uniCloud
							.uploadFile({
								cloudPath: e.tempFilePath,
								filePath: e.tempFilePath
							})
							.then(res => {
								let changTld = this.$store.state.yunstore.yunstore;
								let msg = {
									file: res.fileID,
									length: 0
								};
								if (this.$store.state.yunstore.yunstore != undefined) {
									let changTld = this.$store.state.yunstore.yunstore;
									msg.file = res.fileID.replace(changTld, this.$store.state.yunstore.yunstorebk);
								}
								let min = parseInt(this.recordLength / 60);
								let sec = this.recordLength % 60;
								min = min < 10 ? '0' + min : min;
								sec = sec < 10 ? '0' + sec : sec;
								msg.length = min + ':' + sec;
								this.sendMsg(msg, 'voice');
								this.recording = false;
								this.recorderr = false;
							})
							.catch(err => {
								console.log(err);
								uni.showToast({
									title: '语音文件上传失败，请重新上传',
									icon: 'non'
								})
								this.recording = false
								this.recorderr = false
							});
					} else {
						let that = this;
						that.recording = false;
						that.recorderr = false;
						uni.showToast({
							title: '说话时间太短，请录制时长为一秒以上，最长一分钟的语音',
							icon: 'none',
							mask: true,
							duration: 2000
						});
					}
				} else {
					console.log('取消发送录音');
					this.recording = false;
					this.recorderr = false;
				}
				this.willStop = false;
			},
			// 切换语音/文字输入
			switchVoice() {
				this.hideDrawer();
				this.isVoice = this.isVoice ? false : true;
			},
			discard() {
				return;
			},
			toHealth(val) {
				let tmp;
				if (val == 'create') {
					tmp = '?userInfo=create';
				} else {
					tmp = '?userInfo=' + val.customElem.data
				}
				uni.navigateTo({
					url: '/pages/inquiry/health' + tmp
				});
			},
			quickMsg() {
				this.hideMore = true;
				this.hideEmoji = true;
				this.hideQuick = false;
			},
			sendQuick(msg) {
				this.sendMsg(msg, 'text');
				this.hideDrawer();
			},
			isHealth_File(data) {
				if (data.customElem.description == 'health_file') {
					return true;
				} else {
					return false;
				}
			},
			keyboardheightchange(event) {
				if (event.target.height == 0) {
					if (!(this.hideMore && this.hideEmoji)) {
						this.hideEmoji = true;
						this.hideMore = true;
						this.popupLayerClass = '';
					}
				}
			},
			goedit() {},
			withdraw(msg, index) {
				var d1 = new Date(msg.sendTime); //已知时间戳
				var d2 = new Date(); //当前时间戳
				let minute = parseInt(d2 - d1) / 1000 / 60; //算出相差的分钟
				if (minute > 2) {
					uni.showToast({
						title: '您只能撤回两分钟内发送的消息！',
						icon: 'none'
					});
					return
				}
				let that = this
				let withdrawmsg = JSON.parse(JSON.stringify(msg))
				withdrawmsg.contentType = 2101
				that.$IMSDK.asyncApi('revokeMessage', Date.now().toString(), {
						conversationID: this.conversationID,
						clientMsgID: msg.clientMsgID,
					})
					.then(({
						data
					}) => {
						// 调用成功
						that.$set(that.msgList, index, withdrawmsg)
					})
					.catch(({
						errCode,
						errMsg
					}) => {
						// 调用失败
						uni.showToast({
							title: "撤回消息失败",
							icon: 'none'
						})
					});
			},
			monitorLength(e) {
				if (e.target.value.length >= 500) {
					uni.showToast({
						title: '消息只能输入500字！',
						icon: 'none'
					});
				}
			},
			// 重发消息
			resent(msg) {
				let that = this;
				console.log(msg);
				uni.getNetworkType({
					success: function(res) {
						if (res.networkType == 'none') {
							uni.showToast({
								title: '您的网络已断开，请检查网络后重新发送',
								icon: 'none'
							});
						} else {
							uni.showModal({
								title: '重新发送',
								content: '是否重新发送本条消息？',
								confirmText: '重新发送',
								success(res) {
									if (res.confirm) {
										let index = that.msgList.findIndex(item => item.clientMsgID ===
											msg.clientMsgID)
										that.msgList.splice(index, 1)
										that.$IMSDK.asyncApi('deleteMessageFromLocalStorage', Date
												.now().toString(), {
													conversationID: that.conversationID,
													clientMsgID: msg.clientMsgID,
												})
											.then(({
												data
											}) => {
												// 调用成功
												if (msg.contentType == 101) {
													that.sendMsg({
														text: msg.textElem.content
													}, 'text')
												} else if (msg.contentType == 102) {
													let imginfo = msg.pictureElem.sourcePicture
													that.sendMsg({
															fileID: imginfo.url,
															type: imginfo.type,
															size: imginfo.size
														},
														'img'
													);
												} else if (msg.contentType == 103) {
													let voiceinfo = msg.soundElem
													let time = voiceinfo.duration
													let length
													if (time < 10) {
														length = '00:0' + time;
													} else if (time < 60) {
														length = '00:' + time;
													} else {
														length = "0" + parseInt(time / 60) + ':' +
															((time %
																	60) < 10 ? '0' + (time % 60) :
																time)
													}
													that.sendMsg({
															file: voiceinfo.sourceUrl,
															length: length
														},
														'voice'
													);
												} else if (msg.contentType == 110) {
													that.sendMsg(msg.customElem.data,
														'custom', msg.customElem.description)
												}
											})
											.catch(({
												errCode,
												errMsg
											}) => {
												// 调用失败
												uni.showToast({
													title: '消息发送失败',
													icon: 'none'
												})
											});

									}
								}
							})
						}
					}
				});

			},
			IMsend(message, type) {
				console.log(message)
				let that = this
				let offlinePush = {
					"title": "欣九康健康版",
					"desc": "您有一条新消息",
					"iOSPushSound": "+1",
					"iOSBadgeCount": true,
					"ex": ""
				}
				if (message) {
					this.msgList.push(message);
					console.log('创建消息', message)
					this.srolltobottom()
				} else {
					uni.showToast({
						title: '消息发送失败',
						icon: 'none'
					})
					return
				}
				if (type == 'voice' || type == 'img') {
					console.log(message)
					this.$IMSDK.asyncApi('sendMessageNotOss', Date.now().toString(), {
							recvID: this.toUserId,
							groupID: "",
							message: message,
							offlinePushInfo: offlinePush
						})
						.then((
							data
						) => {
							console.log(data)
							// 调用成功
							let currentIndex = (this.msgList || []).findIndex((msg) => msg.clientMsgID === message
								.clientMsgID);
							message.status = 2
							message.sessionType = 1
							message.recvID = this.toUserId
							console.log('发送成功', message)
							if (currentIndex >= 0) {
								this.$set(this.msgList, currentIndex, message)
								// this.msgList[currentIndex] = message
							}
						})
						.catch(({
							errCode,
							errMsg
						}) => {
							// 调用失败
							console.log(errCode, errMsg)
							let currentIndex = (this.msgList || []).findIndex((msg) => msg.clientMsgID === message
								.clientMsgID);
							message.status = 3
							if (currentIndex >= 0) {
								this.$set(this.msgList, currentIndex, message)
								// this.msgList[currentIndex] = message
							}
						});
				} else {
					this.$IMSDK.asyncApi('sendMessage', Date.now().toString(), {
							recvID: this.toUserId,
							groupID: "",
							message: message,
							offlinePushInfo: offlinePush
						})
						.then((
							data
						) => {
							// 调用成功
							let currentIndex = (this.msgList || []).findIndex((msg) => msg.clientMsgID === message
								.clientMsgID);
							message.status = 2
							message.sessionType = 1
							message.recvID = this.toUserId
							console.log('发送成功', message)
							if (currentIndex >= 0) {
								this.$set(this.msgList, currentIndex, message)
								// this.msgList[currentIndex] = message
							}
						})
						.catch(({
							errCode,
							errMsg
						}) => {
							// 调用失败
							console.log('发送失败', errCode, errMsg)
							let currentIndex = (this.msgList || []).findIndex((msg) => msg.clientMsgID === message
								.clientMsgID);
							console.log(currentIndex)
							message.status = 3
							if (currentIndex >= 0) {
								this.$set(this.msgList, currentIndex, message)
								// this.msgList[currentIndex] = message
							}
						});
				}
			},
			srolltobottom() {
				this.$nextTick(function() {
					this.scrollAnimation = true;
					//进入页面滚动到底部
					this.$nextTick(function() {
						this.scrollTop = this.msgList.length * 1000;
					});
				});
			}
		}
	};
</script>
<style lang="scss">
	@import '@/static/HM-chat/css/style.scss';

	.health {
		width: 50px;
		height: 60px;
	}

	.btn {
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		margin-right: $uni-spacing-row-lg;
	}

	.endtip {
		position: fixed;
		z-index: 20;
		bottom: -2upx;
		opacity: 1;
		background-color: #ffffff;
		width: 100%;
		box-sizing: border-box;
		min-height: 120rpx;
		padding: $uni-spacing-col-lg $uni-spacing-row-base;

		.tip {
			font-size: $uni-font-size-base;
			color: $uni-text-color-grey;
		}
	}

	.box {
		overflow: hidden;
	}

	.imagesText {
		display: flex;
		flex-direction: column;
		// font-size: 26upx;
		text-align: center;

		.img_box {
			width: 80upx;
			height: 80upx;
			margin: 0 auto 10rpx;
			display: flex;
			justify-content: center;
			align-items: center;

			.imagesSize {
				transform: scale(0.08);
				transform-origin: center center;
				display: block;
				height: auto;
			}
		}

		text {
			font-size: 24rpx;
		}
	}

	.textareaAuto {
		max-height: 80px;
	}

	.family_endtime {
		position: fixed;
		z-index: 999;
		text-align: center;
		width: 100%;
		line-height: 50rpx;
		background-color: #fff;
		font-size: 26rpx;
		color: $jian-bg-color;
	}
</style>