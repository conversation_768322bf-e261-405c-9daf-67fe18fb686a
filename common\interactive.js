/**
 * 本文件用于处理h5和App端的交互相关逻辑，这个文件没有上传的生产服
 */

export const interactive = (type, wv) => {
 switch (type) {
  case 'systemInfo':
   systemInfo(wv);
   break;
  default:

   break;
 }
}

function systemInfo(wv) {
 // 获取系统信息（同步方式确保实时性）
 const systemInfo = uni.getSystemInfoSync();

 // 构造安全区域数据
 const safeAreaData = {
  statusBar: systemInfo.statusBarHeight,   // 状态栏高度
 };
 console.log('进入了');

 // 通过evalJS调用H5全局回调函数
 wv.evalJS(
  `window.__systemInfo(${JSON.stringify(safeAreaData)})`
 );
}