<template>
	<view>
		<!-- 菜单 -->
		<view class="top-warp">
			<app-tabs v-model="tabIndex" :tabs="tabs" @change="tabChange"></app-tabs>
		</view>

		<!-- top="xxx"下拉布局往下偏移,防止被悬浮菜单遮住 -->
		<mescroll-body ref="mescrollRef" @init="mescrollInit" top="60" @down="downCallback" :up="upOption"
			@up="upCallback" @emptyclick="emptyClick">
			<!-- 数据列表 -->
			<view v-for="n in data">
				<prescription v-if="n.ordertype==2" :data="n" @tabChange="tabChange" @pay="pay"></prescription>
				<inquiry v-if="n.ordertype==0" :inquiry="n" @tabChange="tabChange" @pay="pay"></inquiry>
				<family v-if="n.ordertype==5" :inquiry="n" @tabChange="tabChange" @pay="pay"></family>
				<reservation v-if="n.ordertype==1||n.ordertype==4" :inquiry="n" @tabChange="tabChange" @pay="pay"></reservation>
				<videoCall v-if="n.ordertype==6" :inquiry="n" @tabChange="tabChange" @pay="pay"></videoCall>
			</view>
		</mescroll-body>
		<paytypeselector v-if="payTypeShow" @payTypeConfirm="payTypeConfirm" @payTypeCancel="payTypeCancel">
		</paytypeselector>
	</view>
</template>

<script>
	var payChannels = null;
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import AppTabs from "@/components/other/app-tabs.vue";
	import Registration from "@/pages/order/registration.vue";
	import Treatment from "@/pages/order/treatment.vue";
	import Prescription from "@/pages/order/prescription.vue";
	import Inquiry from "@/pages/order/inquiry.vue";
	import Family from "@/pages/order/family.vue";
	import Reservation from "@/pages/order/reservation.vue";
	import videoCall from "@/pages/order/videoCall.vue";
	import Paytypeselector from '@/pages/order/paytypeselector.vue';
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		components: {
			AppTabs,
			Registration,
			Treatment,
			Prescription,
			Inquiry,
			Family,
			Reservation,
			Paytypeselector,
			videoCall
		},
		onLoad(options) {

			// console.log(options.tabIndex)
			this.tabIndex = Number(options.tabIndex)
		},
		data() {
			return {
				payTypeShow: false, //是否显示支付方式选择器
				orderid: "", //待付款的订单号
				upOption: {
					page: {
						num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
						size: 7 // 每页数据的数量
					},
					noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
					// empty:{
					// 	tip: '~ 搜索无数据 ~', // 提示
					// 	btnText: '去看看'
					// }
				},
				data: [], //列表数据
				tabs: ['全部', '待付款', '待发货', '待收货', '待评价'],
				tabIndex: 0, // tab下标
				orderid:''
			}
		},
		methods: {
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				// console.log(this.tabIndex)
				this.$api.get('api/patient/order/getlist', {
					params: {
						pager: page.num, //当前页
						type: this.tabIndex // 不填则查询全部，0 全部 1 待付款 2 待发货 3 待收货 4 问诊咨询中
					}
				}).then(res => {
					//设置列表数据
					this.mescroll.endSuccess(res.length);
					if (page.num == 1) this.data = []; //如果是第一页需手动制空列表
					this.data = this.data.concat(res); //追加新数据
				}).catch(err => {
					this.mescroll.endErr();
				})
			},
			//点击空布局按钮的回调
			emptyClick() {
				uni.showToast({
					title: '点击了按钮,具体逻辑自行实现'
				})
			},

			// 切换菜单
			tabChange() {
				this.data = [] // 先置空列表,显示加载进度
				this.mescroll.resetUpScroll() // 再刷新列表数据
			},
			payTypeCancel() {
				this.payTypeShow = false;
			},
			payTypeConfirm(val) {
				let that = this
				this.payTypeShow = false;
				this.$api
					.post('api/patient/order/payold', {
						ordernumber: this.orderid,
						pay_type: val
					})
					.then(res => {
						console.log('payold',res)
						if (res.success) {
							let provider
							if (val == 0) {
								provider = 'alipay'
							} else if (val == 2) {
								provider = 'wxpay'
							}
							//							var EnvUtils = plus.android.importClass("com.alipay.sdk.app.EnvUtils");
							//							EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);
							uni.requestPayment({
								provider: provider,
								orderInfo: res.data.PayStr,
								success(res) {
									uni.showToast({
										title: '支付成功！',
										duration: 2000,
										success() {
											uni.$emit('updateInquiry');
											that.mescroll.triggerDownScroll();
										}
									});
								},
								fail(err) {
									uni.showModal({
										title: '支付异常',
										content: "支付失败，请重新支付",
										showCancel: false,
										success() {

										}
									})
								}
							})
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none"
							})
							setTimeout(function() {
								that.mescroll.resetUpScroll()
							}, 1500);
						}
					})
					.catch(err => {
						console.log(err);
					});
			},
			pay(orderid) { //支付订单
				// console.log(orderid)
				this.orderid = orderid;
				this.payTypeShow = true;
			},
		}
	}
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;

		.top-warp {
			z-index: 9990;
			position: fixed;
			top: --window-top;
			/* css变量 */
			left: 0;
			width: 100%;
			height: 120upx;
			// background-color: white;
		}

		.top-warp .tip {
			font-size: 28upx;
			height: 60upx;
			line-height: 60upx;
			text-align: center;
		}
	}
</style>