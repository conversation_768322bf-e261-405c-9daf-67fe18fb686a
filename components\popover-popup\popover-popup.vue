<template>
  <view :class="'popup-container-' + xPosition"   @longpress="handleLongPress" >
    <slot></slot>
    <view
      v-if="showPopup"
      :class="['popup', position]"
    >
      <view
        v-for="(button, index) in buttons"
        :key="index"
        v-if="button.isShow(data)"
        class="popup-button"
        @tap="handleButtonClick(button)"
      >
        <view class="edit" >
					<view class="icon" v-if="button.icon">
						<image :src="button.icon" mode="widthFix"></image>
					</view>
					<text>{{ button.label }}</text>
				</view>
      </view>
    </view>
    <view
      v-if="showPopup"
      class="overlay"
      @tap="closePopup"
    ></view>
  </view>
</template>

<script>
const position = {
  lt: 'lt', // 左上
  rt: 'rt', // 右上
  rb: 'rb', // 右下
  lb: 'lb' // 左下
}
export default {
  props: {
    buttons: {
      type: Array,
      required: true,
      default: () => [],
   },
    data: {
      type: Object,
      required: true,
    },
    xPosition: {
      type: String,
      default: 'right', // 默认位置为右侧
    }
  },
  data() {
    return {
      showPopup: false,
      yPosition: 'top',
    };
  },
  computed: {
	  position()  {
		  if (this.yPosition === 'bottom' && this.xPosition === 'left') {
			  return 'lb'
		  } else if (this.yPosition === 'bottom' && this.xPosition === 'right') {
			  return 'rb'
		  } else if (this.yPosition === 'top' && this.xPosition === 'left') {
			  return 'lt'
		  } else if (this.yPosition === 'top' && this.xPosition === 'right') {
			  return 'rt'
		  }
	  }
  },
  methods: {
    handleLongPress(event) {            
      if (!event.touches || event.touches.length === 0) {
        console.warn('触摸事件无效');
        return;
      }
      this.showPopup = true;
      const query = uni.createSelectorQuery().in(this);
      query.select(`.popup-container-${this.xPosition}`).boundingClientRect((rect) => {
        const {top} = rect
        if (top < 100) {
          this.yPosition = 'bottom'
        } else {
          this.yPosition = 'top'
        }
      }).exec()
    },
    closePopup() {
      this.showPopup = false;
    },

    handleButtonClick(button) {
      button.action(this.data)
      this.closePopup()
    }
    
 },
};
</script>

<style scoped lang="scss">
	@import '@/static/HM-chat/css/style.scss';
.popup-container-left {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.popup-container-right {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.popup {
  display: flex;
  height: min-content;
  z-index: 999999;
  background-color: rgba($color: #000000, $alpha: 0.7);
	border-radius: 20rpx;
}

.popup.lt {
  position: absolute;
  bottom: 110%;
  left: 0;
}

.popup.rt {
   position: absolute;
   right: 0;
	bottom: 110%;
}

.popup.rb {
	position: absolute;
	right: 0;
	top: 105%;
}
.popup.lb {
	position: absolute;
	left: 0;
	top: 105%;
}




.popup-button {
  padding: 10rpx;
  text-align: center;
  border-bottom: 1rpx solid #eee;
  color: #fff;
}

.popup-button:last-child {
  border-bottom: none;
}

.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: transparent;
  z-index: 999;
}

.edit {
	width: 100rpx;
	height: 100rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	margin-right: 10rpx;
	.icon{
		width: 30rpx;
		image{
			width: 100%;
		}
	}
	text{
		font-size: 24rpx;
		color: #fff;
	}
}
</style>