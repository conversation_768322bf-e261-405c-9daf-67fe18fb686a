<template>
	<view>
		<uni-list class="tabs">
			<navigator hover-class="none" :url="'./edit-phone?tel=' + tel"><uni-list-item title="手机号" note="设置、更换手机号码" :rightText="tel"></uni-list-item></navigator>
			<navigator hover-class="none" url="../login/forget"><uni-list-item title="密码" note="设置、修改密码"></uni-list-item></navigator>
			<navigator hover-class="none" url="../recipientAddress/authentication"><uni-list-item title="实名认证" note="实名认证信息"></uni-list-item></navigator>
		</uni-list>
		<uni-list class="tabs">
			<view v-for="item in oauth">
				<uni-list-item v-if="item.type == 1" title="微信绑定" :note="item.openid"></uni-list-item>
				<uni-list-item v-if="item.type == 4" title="AppleID绑定" :note="item.openid"></uni-list-item>
				<uni-list-item v-if="item.type == 5" title="微信小程序绑定" :note="item.openid"></uni-list-item>
			</view>
		</uni-list>
		<uni-list class="tabs">
			<navigator v-if="cancellationType == 1" hover-class="none" url="./cancellation/result"><uni-list-item title="注销账号" note=""></uni-list-item></navigator>
			<navigator v-else hover-class="none" url="./cancellation/cancellation"><uni-list-item title="注销账号" note=""></uni-list-item></navigator>
		</uni-list>
	</view>
</template>

<script>
import uniList from '@/components/uni-list/uni-list.vue';
import uniListItem from '@/components/uni-list-item/uni-list-item.vue';
import { mapState } from 'vuex';
export default {
	components: { uniList, uniListItem },
	data() {
		return {
			isios: false,
			oauth: [],
			cancellationType: ''
		};
	},
	onLoad() {
		if (uni.getSystemInfoSync().platform == 'ios') {
			this.isios = true;
		}
		this.oauth = this.$store.state.userInfo.oauth;
		this.$api.post('api/patient/auth/checkregister').then(res => {
			console.log(res)
			this.cancellationType = res.cancellationType;
		});
	},
	computed: {
		...mapState({
			tel: function(state) {
				if (state.userInfo.telephone != undefined) {
					return state.userInfo.telephone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
				} else {
					return '';
				}
			}
		})
	},
	methods: {}
};
</script>

<style lang="scss">
page {
	background-color: $uni-bg-color-grey;
	.tabs {
		margin-bottom: $uni-spacing-col-lg;
	}
}
</style>
