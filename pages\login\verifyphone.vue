<template>
	<view class="register">
		<view class="content">
			<!-- 头部logo -->
			<view class="header">
				<image :src="logoImage"></image>
			</view>
			<!-- 主体 -->
			<view class="main">
				<view class="textActive" v-if="btnFlag == true">请输入手机号和验证码！</view>
				<wInput v-model="phoneData" type="text" maxlength="11" placeholder="手机号"></wInput>
				<wInput v-model="verCode" type="number" maxlength="6" placeholder="验证码" isShowCode ref="runCode"
					@setCode="getVerCodeAndVerifyPhone()"></wInput>
				<view class="textActive" v-show="isPhoneUsed==1">此手机号已绑定过其他账号，提交后老账号将绑定您的第三方登录账号</view>
			</view>
			<view class="main" v-show="isPhoneUsed==2">
				<view class="textActive" v-if="btnFlag == false">初次登录，请设置账号和密码</view>
				<wInput v-model="userName" type="text" placeholder="用户名"></wInput>
				<wInput v-model="passData" type="password" placeholder="登录密码" isShowPass></wInput>
			</view>
			<wButton class="mainTop" text="提 交" :rotate="isRotate" @click.native="startReg()"></wButton>
			<!-- <wInput v-model="paytype" type="text"></wInput>
					<wInput v-model="paytypeAll" type="text"></wInput>
					<wInput v-model="paytypetime" type="text"></wInput> -->
			<!-- 底部信息 -->
			<view class="agreement">
				<text @tap="isShowAgree" class="cuIcon"
					:class="showAgree ? 'cuIcon-radiobox' : 'cuIcon-round'">同意</text>
				<!-- 协议地址 -->
				<navigator url="./protocol" open-type="navigate">《协议》</navigator>
			</view>
		</view>
	</view>
</template>

<script>
	var _this;
	import wInput from '../../components/watch-login/watch-input.vue'; //input
	import wButton from '../../components/watch-login/watch-button.vue'; //button

	export default {
		data() {
			return {
				//logo图片 base64
				logoImage: '/static/img/logo.png',
				phoneData: '', // 用户/电话
				passData: '', //密码
				verCode: '', //验证码
				userName: '', //用户名
				showAgree: true, //协议是否选择 
				isRotate: false, //是否加载旋转
				isPhoneUsed: 0, //0：初始状态，1：已注册过账号，2：未注册过账号。用户填的手机号是否已注册过，如注册过账号，这里直接绑定旧账号，如未注册过则需要完善用户名和密码信息
				loginData: null,
				btnFlag: true
			};
		},
		components: {
			wInput,
			wButton
		},
		mounted() {
			_this = this;
		},
		onLoad(option) {
			_this = this;
			let tmp = JSON.parse(decodeURIComponent(option.logindata));
			this.loginData = tmp;
			// 	uni.setStorageSync('sigTimer', parm)
			// this.paytype = JSON.stringify(uni.getStorageSync('sigTimer') || '第一行')
			// this.paytypeAll = JSON.stringify(uni.getStorageSync('sigTime') || '第二行')
			// this.paytypetime = JSON.stringify(uni.getStorageSync('sigTim') || '第三行')
		},
		methods: {
			isShowAgree() {
				//是否选择协议
				_this.showAgree = !_this.showAgree;
			},
			getVerCodeAndVerifyPhone() {
				//获取验证码
				if (!(/^1[3|4|5|7|8][0-9]{9}$/.test(this.phoneData))) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '手机号码格式不正确或为空!'
					});
					return false;
				}
				console.log('获取验证码');
				this.$refs.runCode.$emit('runCode'); //触发倒计时（一般用于请求成功验证码后调用）
				setTimeout(() => {
					_this.$refs.runCode.$emit('runCode', 0);
				}, 60000);
				this.$api
					.get('api/patient/getsmscode', {
						params: {
							telephone: this.phoneData
						}
					})
					.then(res => {})
					.catch(err => {});
				this.$api
					.get('api/v2/oauth/bind_mobile', {
						params: {
							role: 0,
							mobile: this.phoneData
						}
					})
					.then(res => {
						if (res.errno == 0) {
							_this.isPhoneUsed = 1;
							_this.btnFlag = true
						} else if (res.errno == 10) {
							_this.isPhoneUsed = 2;
							_this.btnFlag = false
						} else {
							uni.showToast({
								icon: 'none',
								position: 'bottom',
								title: res.msg
							});
						}
					})
					.catch(err => {});
			},
			startReg() {
				console.log('绑定注册');
				//注册
				if (this.isRotate) {
					//判断是否加载中，避免重复点击请求
					return false;
				}
				if (this.showAgree == false) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '请先同意《协议》'
					});
					return false;
				}
				if (!(/^1[3|4|5|7|8][0-9]{9}$/.test(this.phoneData))) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '手机号码格式不正确或为空!'
					});
					return false;
				}
				if (!(/^\d{6}$/.test(this.verCode))) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '验证码6位，只包括数字'
					});
					return false;
				}
				if (this.isPhoneUsed == 2) {
					if (!(/^[a-zA-Z][a-zA-Z0-9_@]{4,15}$/.test(this.userName))) {
						uni.showToast({
							icon: 'none',
							position: 'bottom',
							title: '账号必须字母开头，允许5-16字节，允许字母数字下划线组合!'
						});
						return false;
					}
					if (!(/^.*(?=.{6,})(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*? ]).*$/.test(this.passData))) {
						uni.showToast({
							icon: 'none',
							position: 'bottom',
							title: '密码最少6位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符!'
						});
						return false;
					}
				}
				console.log(this.isPhoneUsed, 6688)
				let url, parm;
				if (this.isPhoneUsed == 1) {
					url = 'api/v2/oauth/bind_mobile';
					parm = {
						'role': 0,
						'type': this.loginData.type,
						'openid': this.loginData.openId ? this.loginData.openId : this.loginData.openid,
						'client_id': this.loginData.client_id,
						'smscode': this.verCode,
						'mobile': this.phoneData
					};
				} else if (this.isPhoneUsed == 2) {
					url = 'api/v2/oauth/complete';
					parm = {
						'role': 0,
						'type': this.loginData.type,
						'openid': this.loginData.openId ? this.loginData.openId : this.loginData.openid,
						'client_id': this.loginData.client_id,
						'smscode': this.verCode,
						'mobile': this.phoneData,
						'name': this.userName,
						'password': this.passData
					};
				} else {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '请先获取手机短信验证码'
					});
					return false;
				}
				_this.isRotate = true;
				setTimeout(function() {
					_this.isRotate = false;
				}, 3000);
				console.log(url, 'url');
				console.log(parm, 'parm');
				this.$api.post(url, parm).then(res => {
					if (res.errno == 0) {
						getApp().globalData.token = "Bearer " + res.data.token;
						// #ifdef APP-PLUS
						try {
							uni.setStorageSync('token', "Bearer " + res.data.token); //存入缓存
						} catch (e) {
							console.log(e, 9966)
							// error $emit
						}
						// #endif
						getApp().fileInfo();
						uni.showToast({
							icon: 'success',
							position: 'bottom',
							title: res.msg
						});
						// uni.switchTab({
						//     url: '/pages/index/index'
						// });
					} else {
						uni.showToast({
							icon: 'none',
							position: 'bottom',
							title: res.msg
						});
						console.log(res);
					}
				}).catch(err => {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '异常错误，请稍后重试'
					});
					_this.isRotate = false;
					console.log(err);
				})
			}
		}
	};
</script>

<style>
	@import url('../../components/watch-login/css/icon.css');
	@import url('./css/main.css');
</style>