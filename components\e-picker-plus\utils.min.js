"use strict";
Object.defineProperty(exports, "__esModule", {
	value: !0
});
var modeReg = [{
		mode: "Y",
		reg: new RegExp(/^\d{4}$/),
		start: "2000",
		end: (new Date).getFullYear() + ""
	}, {
		mode: "YM",
		reg: new RegExp(/^\d{4}-\d{2}$/),
		start: "2000-01",
		end: (new Date).getFullYear() + "-12"
	}, {
		mode: "YMD",
		reg: new RegExp(/^\d{4}-\d{2}-\d{2}$/),
		start: "2000-01-01",
		end: (new Date).getFullYear() + "-12-31"
	}, {
		mode: "YMDh",
		reg: new RegExp(/^\d{4}-\d{2}-\d{2}\s{1}\d{2}$/),
		start: "2000-01-01 00",
		end: (new Date).getFullYear() + "-12-31 23"
	}, {
		mode: "YMDhm",
		reg: new RegExp(/^\d{4}-\d{2}-\d{2}\s{1}\d{2}:\d{2}$/),
		start: "2000-01-01 00:00",
		end: (new Date).getFullYear() + "-12-31 23:59"
	}, {
		mode: "YMDhms",
		reg: new RegExp(/^\d{4}-\d{2}-\d{2}\s{1}\d{2}:\d{2}:\d{2}$/),
		start: "2000-01-01 00:00:00",
		end: (new Date).getFullYear() + "-12-31 23:59:59"
	}, {
		mode: "h",
		reg: new RegExp(/^\d{2}$/),
		start: "00",
		end: "23"
	}, {
		mode: "hm",
		reg: new RegExp(/^\d{2}:\d{2}$/),
		start: "00:00",
		end: "23:59"
	}, {
		mode: "hms",
		reg: new RegExp(/^\d{2}:\d{2}:\d{2}$/),
		start: "00:00:00",
		end: "23:59:59"
	}],
	fmt = function(e) {
		return e > 9 ? e : "0" + e
	},
	isLeapYear = function(e) {
		return e % 4 == 0 && e % 100 != 0 || e % 100 == 0 && e % 400 == 0
	},
	defaultKV = [{
		key: "Y",
		title: "年"
	}, {
		key: "M",
		title: "月"
	}, {
		key: "D",
		title: "日"
	}, {
		key: "h",
		title: "时"
	}, {
		key: "m",
		title: "分"
	}, {
		key: "s",
		title: "秒"
	}],
	utils = {
		hasMode: function(e) {
			return modeReg.find(function(t) {
				return t.mode == e
			})
		},
		getRange: function(e, t) {
			return modeReg.find(function(t) {
				return t.mode === e
			})[t]
		},
		getLocalTime: function(e) {
			var t = new Date;
			switch (e) {
				case "Y":
					return "" + t.getFullYear();
				case "YM":
					return t.getFullYear() + "-" + fmt(t.getMonth() + 1);
				case "YMD":
					return t.getFullYear() + "-" + fmt(t.getMonth() + 1) + "-" + fmt(t.getDate());
				case "YMDh":
					return t.getFullYear() + "-" + fmt(t.getMonth() + 1) + "-" + fmt(t.getDate()) + " " + fmt(t.getHours());
				case "YMDhm":
					return t.getFullYear() + "-" + fmt(t.getMonth() + 1) + "-" + fmt(t.getDate()) + " " + fmt(t.getHours()) + ":" +
						fmt(t.getMinutes());
				case "YMDhms":
					return t.getFullYear() + "-" + fmt(t.getMonth() + 1) + "-" + fmt(t.getDate()) + " " + fmt(t.getHours()) + ":" +
						fmt(t.getMinutes()) + ":" + fmt(t.getSeconds());
				case "h":
				case "hm":
				case "hms":
					return fmt(t.getHours()) + ":" + fmt(t.getMinutes()) + ":" + fmt(t.getSeconds())
			}
		},
		getDays: function(e, t) {
			return [31, isLeapYear(e) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][Number(t) - 1]
		},
		getColumn: function(e, t) {
			for (var r = [], n = t; n <= e; n++) r.push(fmt(n) + "");
			return r
		},
		time2Timestamp: function(e) {
			return new Date(e).getTime()
		},
		dateTime2Obj: function(e) {
			var t = e.length,
				r = {};
			return t < 10 && 4 != t && 7 != t ? (t >= 2 && (r.h = e.substring(0, 2)), t >= 5 && (r.m = e.substring(3, 5)), 8 ==
				t && (r.s = e.substring(6, 8))) : (t >= 4 && (r.Y = e.substring(0, 4)), t >= 7 && (r.M = e.substring(5, 7)), t >=
				10 && (r.D = e.substring(8, 10)), t >= 13 && (r.h = e.substring(11, 13)), t >= 16 && (r.m = e.substring(14, 16)),
				19 == t && (r.s = e.substring(17, 19))), r
		},
		value2Obj: function(e, t, r) {
			for (var n = {}, u = 0; u < t.length; u++) n[t[u]] = r[t[u]][e[u]];
			return n
		},
		obj2Value: function(e, t) {
			var r = [];
			for (var n in t) r.push(Number(t[n]) - Number(e[n]));
			return r
		},
		obj2Arr: function(e) {
			var t = [];
			for (var r in e) ! function(r) {
				t.push(e[r] + defaultKV.find(function(e) {
					return e.key == r
				}).title)
			}(r);
			return t
		},
		obj2DateTime: function(e, t) {
			var r = "";
			for (var n in e) ! function(t) {
				r += e[t] + defaultKV.find(function(e) {
					return e.key == t
				}).title
			}(n);
			var u = r.replace("年", "-").replace("月", "-").replace("日", " ").replace("时", ":").replace("分", ":").replace("秒", "");
			return t.endsWith("s") || (u = u.substring(0, u.length - 1)), u
		}
	};
exports.default = utils;
