<template>
	<view class="psm" v-if="!hidden">
		<view class="popup">
			<view class="title">
				友情提示
			</view>
			<view class="tip">
				{{tipwords}}
			</view>
			<view class="check">
				<view class="checkbox" @click="checkclick">
					<image src="/static/img/doctors_img/dui.png" mode="widthFix" v-if="checks" ></image>
					<image src="/static/img/doctors_img/uncheck.png" mode="widthFix" v-else></image>
					<text>不再提示</text>
				</view>
				<button type="default" @click="set">我知道了</button>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		name: " prompt-save-msg",
		props: {
			tipwords: String,
			storagename:String
		},
		data() {
			return {
				checks: false,
				hidden:false
			};
		},
		methods: {
			checkclick() {
				this.checks = !this.checks
			},
			set(){
				console.log(this.checks)
				if(this.checks){
					uni.setStorageSync(this.storagename,true)
				}
				this.hidden = true
			}
		},
	}
</script>

<style lang="scss">
	.psm {
		position: fixed;
		width: 100%;
		height: 100%;
		background-color: rgba($color: #000000, $alpha: .3);
		z-index: 999;

		.popup {
			width: 70%;
			padding: 26rpx;
			background-color: #fff;
			border-radius: 10rpx;
			position: absolute;
			left: 15%;
			top: 30%;

			.title {
				font-size: 40rpx;
				font-weight: bold;
				text-align: center;
			}

			.tip {
				margin-top: 20rpx;
				font-weight: 30rpx;
			}

			.check {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-top: 20rpx;
				.checkbox{
					display: flex;
					align-items: center;
					image{
						width: 40rpx;
						margin-right: 10rpx;
					}
				}
				button {
					background-color: $jian-bg-color;
					color: #fff;
					border: none;
					margin-top: 20rpx;
					width: 200rpx;
					font-weight: 30rpx;
					height: 70rpx;
					line-height: 70rpx;
					margin: 0;
				}

			}
		}
	}
</style>
