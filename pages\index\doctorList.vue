<template>
	<view class="list">
		<view class="title space-between">
			<view class="space-between">
				<text class="rectangle"></text>
				<text style="color: #333333;font-size: 16px;">推荐医生</text>
			</view>
			<navigator style="color: #999999;font-size: 13px;" url="../doctors/doctorsList?type=1">查看更多>></navigator>
		</view>
		<view class="uni-padding-wrap">
			<view class="page-section swiper">
				<view class="page-section-spacing">
					<swiper class="swiper" next-margin="80rpx" :autoplay="autoplay" :interval="interval" :duration="duration">
						<swiper-item v-for="n in doctorList" @tap="toDetails(n.id)">
							<view class="swiper-item">
								<image class="avatar" v-if="!n.face && n.gender == 0" src="../../static/img/defaultgirl.png" mode=""></image>
								<image class="avatar" v-if="!n.face && n.gender == 1" src="../../static/img/defaultman.png" mode=""></image>
								<image class="avatar" v-if="n.face" :src="$baseUrl + n.face" mode=""></image>
								<view class="desc">
									<view>{{ n.realname }} <text v-if="n.departmentname">({{ n.departmentname }})</text></view>
									<view id="cliname" >
										<text >{{ n.cliname }}</text>
									</view>
									<view class="brief" v-if="n.brief">{{ n.brief }}</view>
									<view class="bottom">
										<text class="price">￥{{ n.inquiryfee }}</text>
										<text style="font-size: 14px;">服务数{{ n.number }}</text>
									</view>
								</view>
							</view>
						</swiper-item>
					</swiper>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			doctorList: [],
			indicatorDots: true,
			autoplay: false,
			interval: 2000,
			duration: 500
		};
	},
	created() {
		this.getdoctorlist();
	},
	methods: {
		getdoctorlist() {
			let that = this;
			this.$api.get('api/patient/recommenddoctor', { params: { pager: 1 } }).then(res => {
				that.doctorList = res;
			});
		},
		toDetails(id) {
			uni.navigateTo({
				url: '/pages/doctors/doctorsDetails?id=' + id
			});
		}
	}
};
</script>

<style lang="scss">
.title {
	height: 60px;
}
.space-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
	color: $uni-text-color;
}
.rectangle {
	display: inline-block;
	width: 4px;
	height: 18px;
	background-color: #16cc9f;
	margin-right: 20rpx;
}
.list {
	padding: 0px $uni-spacing-row-sm;
}
.price{
	color: $jian-bg-color;
	font-size: 20px;
	margin-right: 15px;
}
.swiper-item {
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
	margin: $uni-spacing-col-sm;
	height: 130px;
	margin-right: $uni-spacing-row-base;
	margin-bottom: $uni-spacing-col-sm;
	-webkit-box-shadow: rgba(0, 0, 0, 0.14) 0px 0px 2px;
	-moz-box-shadow: rgba(0, 0, 0, 0.14) 0px 0px 2px;
	box-shadow: rgba(0, 0, 0, 0.14) 0px 0px 2px;
	border-radius: $uni-border-radius-base;
}
.avatar {
	width: 85px;
	height: 95px;
	margin: 8.5px;
	border-radius: 2px;
}
.desc {
	width: calc(100% - 90px);
	position: relative;
	height: 95px;
	margin: 8.5px;
}
.bottom {
}
#cliname {
	width: 100%;
	height: 18px;
	color: #999999;
	line-height: 18px;
	font-size: 14px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.brief {
	font-size: 14px;
	color: $uni-text-color;
	line-height: 20px;
	text-align: left;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}
</style>
