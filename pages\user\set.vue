<template>
	<view>
		<uni-list class="tabs">
			<navigator hover-class="none" url="./account"><uni-list-item title="账户与安全"></uni-list-item></navigator>
		</uni-list>
		<uni-list class="tabs">
			<view><uni-list-item title="检查更新" @click="updateApp()" :note="version"></uni-list-item></view>
			<navigator class="usertest" hover-class="none" url="../login/protocol"><uni-list-item title="用户协议"
					note="用户服务协议"></uni-list-item></navigator>
			<navigator hover-class="none" url="../login/privacy"><uni-list-item title="隐私政策"
					note="隐私保护政策"></uni-list-item></navigator>
			<navigator hover-class="none" url="./permission">
				<uni-list-item title="权限设置" note="设置手机权限"></uni-list-item>
			</navigator>
			<view><uni-list-item title="清理缓存" :rightText="fileSizeString" @click="clearCache"></uni-list-item></view>
			<view><uni-list-item title="清理聊天缓存" @click="clearim"></uni-list-item></view>
			<navigator hover-class="none" url="./AboutUs"><uni-list-item title="关于我们"></uni-list-item></navigator>
		</uni-list>
		<uni-list class="tabs">
			<view><uni-list-item @click="logout()" title="退出当前账号"></uni-list-item></view>
			<view><uni-list-item v-show="!isios" @click="quit()" title="退出应用"></uni-list-item></view>
		</uni-list>
	</view>
</template>

<script>
	import uniList from '@/components/uni-list/uni-list.vue';
	import uniListItem from '@/components/uni-list-item/uni-list-item.vue';
	import {
		mapState
	} from 'vuex';
	import APPUpdate, {
		getCurrentNo
	} from '@/common/appUpdate.js';
	export default {
		components: {
			uniList,
			uniListItem
		},
		data() {
			return {
				// #ifdef APP-PLUS
				version: plus.runtime.version,
				// #endif
				fileSizeString: '',
				isios: false,
				logouttimes: 0
			};
		},
		onLoad() {
			this.formatSize();
			if (uni.getSystemInfoSync().platform == 'ios') {
				this.isios = true;
			}
			// #ifdef APP-PLUS
			getCurrentNo(res => {
				// 进页面获取当前APP版本号（用于页面显示）
				this.version = res.versionName;
			});
			// #endif
		},
		computed: {
			...mapState({
				tel: function(state) {
					if (state.userInfo.telephone != undefined) {
						return state.userInfo.telephone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
					} else {
						return '';
					}
				}
			})
		},
		methods: {
			clearstrong() {
				// #ifdef APP-PLUS
				uni.clearStorageSync();
				// #endif
			},
			// #ifdef APP-PLUS
			clearCache() {
				//清理缓存
				let that = this;
				let os = plus.os.name;
				if (os == 'Android') {
					let main = plus.android.runtimeMainActivity();
					let sdRoot = main.getCacheDir();
					let files = plus.android.invoke(sdRoot, 'listFiles');
					let len = files.length;
					for (let i = 0; i < len; i++) {
						let filePath = '' + files[i]; // 没有找到合适的方法获取路径，这样写可以转成文件路径
						plus.io.resolveLocalFileSystemURL(
							filePath,
							function(entry) {
								if (entry.isDirectory) {
									entry.removeRecursively(
										function(entry) {
											//递归删除其下的所有文件及子目录
											uni.showToast({
												title: '缓存清理完成',
												duration: 2000
											});
											that.formatSize(); // 重新计算缓存
										},
										function(e) {
											console.log(e.message);
										}
									);
								} else {
									entry.remove();
								}
							},
							function(e) {
								console.log('文件路径读取失败');
							}
						);
					}
				} else {
					// ios暂时未找到清理缓存的方法，以下是官方提供的方法，但是无效，会报错
					plus.cache.clear(function() {
						uni.showToast({
							title: '缓存清理完成',
							duration: 2000
						});
						that.formatSize();
					});
				}
			},
			// #endif
			formatSize() {
				let that = this;
				plus.cache.calculate(function(size) {
					let sizeCache = parseInt(size);
					if (sizeCache == 0) {
						that.fileSizeString = '0B';
					} else if (sizeCache < 1024) {
						that.fileSizeString = sizeCache + 'B';
					} else if (sizeCache < 1048576) {
						that.fileSizeString = (sizeCache / 1024).toFixed(2) + 'KB';
					} else if (sizeCache < 1073741824) {
						that.fileSizeString = (sizeCache / 1048576).toFixed(2) + 'MB';
					} else {
						that.fileSizeString = (sizeCache / 1073741824).toFixed(2) + 'GB';
					}
				});
			},
			logout() {
				let that = this;
				uni.showModal({
					title: '温馨提示',
					content: `是否退出当前账号`,
					success: function(res) {
						if (res.confirm) {
							uni.showLoading({
								mask: true
							});
							that.openIMlogout()

						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			openIMlogout() {
				let that = this
				this.$IMSDK.asyncApi('logout', Date.now().toString())
				  .then(() => {
				    // 退出登录成功
						that.$api.post('api/patient/user/logout').then(res => {
						if(res.success){
							that.$store.commit('reset');
							getApp().globalData.token = null;
							// #ifdef APP-PLUS
							uni.clearStorageSync();
							// #endif
							uni.reLaunch({
								url: '../login/login'
							});
					} else {
							uni.showToast({
								title:'登出失败',
								content:res.msg
							})
						}
					})
				  })
				  .catch(({ errCode, errMsg }) => {
				    // 调用失败
						if (that.logouttimes < 2) {
							that.logouttimes = that.logouttimes + 1
							uni.showToast({
								title: '登出失败，请稍后重试',
								icon: 'none'
							});
						} else {
							if (that.isios) {
								uni.showModal({
									title: '登出失败',
									content: '您已多次登出失败，建议手动重启应用后重试登出或关闭应用',
									showCancel: false,
									success(res) {}
								})
							} else {
								uni.showModal({
									title: '登出失败',
									content: '您已多次登出失败，是否强制关闭应用并登出？',
									success(res) {
										if (res.confirm) {
											getApp().globalData.token = null;
											// #ifdef APP-PLUS
											uni.clearStorageSync();
											// #endif
											plus.runtime.quit()
										}
									}
								})
							}
						}
				});
			},
			quit() {
				uni.showModal({
					title: '温馨提示',
					content: `是否退出应用`,
					success: function(res) {
						if (res.confirm) {
							// getApp().globalData.token = null;
							// #ifdef APP-PLUS
							// uni.clearStorageSync();
							plus.runtime.quit();
							// #endif
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			updateApp() {
				APPUpdate(true);
			},
			// 清理聊天记录
			clearim() {
				let that = this;
				uni.showModal({
					title: '清理聊天记录',
					content: '聊天记录清除后无法重新获取，是否确认清除？',
					success: res => {
						if (res.confirm) {
							that.$IMSDK.asyncApi('deleteAllMsgFromLocalAndSvr', Date.now().toString())
							  .then(({ data }) => {
								// 调用成功
									uni.showToast({
										title: '清理完成',
										icon: 'success'
								})
							  })
							  .catch(({ errCode, errMsg }) => {
								// 调用失败
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;

		.tabs {
			margin-bottom: $uni-spacing-col-lg;
		}
	}
</style>