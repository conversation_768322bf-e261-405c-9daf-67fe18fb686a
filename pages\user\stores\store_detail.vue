<template>
	<view>
		<view class="store_info">
			<view class="title">
				门店账户
			</view>
			<view class="avatar" @click="todetail()">
				<image v-if="storeInfo.clindFace" :src="$hisBaseUrl+storeInfo.clindFace" mode=""></image>
				<image v-else src="../../../static/img/hospital.jpg" mode=""></image>
				
			</view>
			<view class="name" @click="todetail()">
				{{storeInfo.clindName}}
			</view>
			<!-- 	<view class="id">
				账户id：************
			</view> -->
			<view class="account_info">
				<view class="balance">
					<view class="tt">
						我的余额
					</view>
					<view class="value">
						￥{{storeInfo.memberBalance}}
					</view>
					<view class="btn" @click="toaccount">
						查看账单
					</view>
				</view>
				<view class="balance">
					<view class="tt">
						红包卡券
					</view>
					<view class="value">
						{{storeInfo.couponsCount}}张
					</view>
					<view class="btn" @click="toticket">
						查看券包
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				storeInfo: {}
			};
		},
		onLoad(option) {
			let data = JSON.parse(decodeURIComponent(option.data))
			console.log(data)
			this.$api.get("api/patient/vip/getDetails", {
				params: {
					id: data.clindId
				}
			}).then(res => {
				if (res.success) {
					data.memberBalance = res.data.memberBalance
					data.couponsCount = res.data.couponsCount
					this.storeInfo = data
				} else {
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}
			})
		},
		onNavigationBarButtonTap(e) {
			if (e.index == 0) {
				uni.navigateTo({
					url: '/pages/user/stores/rechargeBook?id=' + this.storeInfo.clindId
				});
			}
		},
		methods: {
			toaccount() {
				uni.navigateTo({
					url: '/pages/user/stores/accountBook?id=' + this.storeInfo.clindId
				})
			},
			toticket() {
				uni.navigateTo({
					url: '/pages/user/stores/tickets?id=' + this.storeInfo.clindId
				})
			},
			todetail(){
				uni.navigateTo({
					url: '/pages/hospitals/hospitalDetail/hospitalDetail?id=' + this.storeInfo.clindId
				})
			}
		}
	}
</script>

<style lang="scss">
	.store_info {
		margin-top: 50rpx;
		text-align: center;
		font-size: 28rpx;

		.title {
			font-size: 36rpx;
			font-weight: bold;
		}

		.avatar {
			width: 150rpx;
			height: 150rpx;
			overflow: hidden;
			border-radius: 50%;
			margin: 20rpx auto;
			image {
				width:150rpx;
				height: 150rpx;
			}
		}

		.name {
			margin-top: 15rpx;
			font-size: 28rpx;
			font-weight: bold;
		}

		.id {
			margin-top: 15rpx;
		}

		.account_info {
			display: flex;
			justify-content: space-around;
			margin-top: 20rpx;

			.balance {
				width: 40%;

				.tt {
					font-weight: bold;
					font-size: 30rpx;
				}

				.value {
					color: $jian-bg-color;
					font-size: 30rpx;
					margin-top: 20rpx;
				}

				.btn {
					margin: 20rpx auto 0;
					width: 200rpx;
					line-height: 60rpx;
					background-color: $jian-bg-color;
					border-radius: 30rpx;
					color: #fff;
				}
			}
		}
	}
</style>