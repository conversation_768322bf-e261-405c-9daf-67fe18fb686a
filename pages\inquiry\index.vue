<template>
	<view>
		<view class="nav">
			<view class="tab">
				<view class="tab-item" :class="{'chosed':listId==1}" @click="listId = 1">
					咨询问诊
				</view>
				<view class="tab-item" :class="{'chosed':listId==2}" @click="listId = 2">
					视频咨询
				</view>
			</view>
		</view>
		<inquiryList v-show="listId==1"></inquiryList>
		<communicationList v-show="listId==2"></communicationList>
	</view>
</template>

<script>
	import inquiryList from './inquiryList.vue'
	import communicationList from './communicationList.vue'
	let that;
	export default {
		components:{
			communicationList,
			inquiryList
		},
		data() {
			return {
				listId:1
			};
		},
		
		
		
		methods: {
			

		},
	};
</script>

<style lang="scss">
	page{
		width: 100% !important;
		background-color: $uni-bg-color-grey;
		// padding: $uni-spacing-col-lg 0px;
		.nav{
			background-color: $uni-bg-color-grey;
			.tab{
				position: fixed;
				display: flex;
				z-index: 999;
				justify-content: space-around;
				align-items: center;
				width: 100%;
				height: 100rpx;
				background-color: $uni-bg-color-grey;
				padding-top: var(--status-bar-height);	
				.tab-item{
					font-weight: bold;
					color: $uni-text-color-grey;
				}
				.chosed{
					color: $jian-bg-color;
					&::after{
						content: '';
						width: 100rpx;
						height: 6rpx;
						background-color: $jian-bg-color;
						display: block;
						border-radius: 3rpx;
						margin: 10rpx auto 0;
					}
				}
			}
		}
	}
</style>