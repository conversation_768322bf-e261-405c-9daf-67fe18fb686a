<template>
	<view class="app-tabs" :class="{'tabs-fixed': fixed}">
		<view class="tabs-item">
			<view class="tab-item" v-for="(tab, i) in appoint_date" :class="{'active': value===i}" :key="i" @click="tabClick(i)">
				<!-- {{getTabName(tab)}} -->
				<view>{{tab[1]}}</view>
				<view class="date">{{tab[0]}}</view>
				<view v-if='!parseInt(tab[2])>0'>无号</view>
				<view v-else>有号</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			tabs: Array,
			value: { // 当前显示的下标 (使用v-model语法糖: 1.props需为value; 2.需回调input事件)
				type: [String,Number],
				default(){
					return 0
				}
			},
			fixed: Boolean // 是否悬浮,默认false
		},
		data() {
			return {
				appoint_date: []
			}
		},
		computed: {
			lineLift() {
				return 100/this.tabs.length*(this.value + 1) - 100/(this.tabs.length*2) + '%'
			}
		},
		mounted() {
			let that = this
			this.tabs.forEach(function(value) {
				let appointMsg = value.split('^')
				appointMsg[0] = appointMsg[0].substr(appointMsg[0].length-2,2)
				that.appoint_date.push(appointMsg)
			})
			console.log(this.appoint_date)
		},
		methods: {
			getTabName(tab){
				return typeof tab === "object" ? tab.name : tab
			},
			tabClick(i){
				if(this.value!=i){
					this.$emit("input",i);
					this.$emit("change",i);
				}
			},
			init(arr){
			　　this.tabs=arr;
				console.log(this.tabs)
			
			},
			tabsplit(e){
				
			},
		},
		
	}
	
</script>

<style lang="scss">
	.app-tabs{
		position: relative;
		// height: 60rpx; 
		line-height: 60rpx;
		font-size: 30rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #eee;
	}
	.app-tabs .tabs-item{
		display: flex;
		text-align: center;
	}
	.app-tabs .tabs-item .tab-item{
		flex: 1;
		.date{
			line-height: 60rpx;
			width: 60rpx;
			font-size: 32rpx;
			font-weight: bold;
			margin: 0 auto;
		}
	}
	.app-tabs .tabs-item .active{
		color: $jian-bg-color;
		.date{
			background-color: $jian-bg-color;
			color: #fff;
			border-radius: 50%;
		}
	}
	.app-tabs .tabs-line{
		position: absolute;
		bottom: 0;
		width: 40rpx;
		height: 4rpx;
		transform: translateX(-50%);
		border-radius: 4rpx;
		transition: left .3s;
		background: red;
	}
	
	/*悬浮*/
	.app-tabs.tabs-fixed{
		z-index: 9999;
		position: fixed;
		top: var(--window-top);
		left: 0;
		width: 100%;
	}
</style>
