import Vue from 'vue'
import Vuex from 'vuex'

Vue.use(Vuex)
const store = new Vuex.Store({
    state: {
		isTimLogin: false,
		isSDKReady: false ,// TIM SDK 是否 ready
		
		conversationActive:{},	//聊天进行中的会话
		toUserId:'',			//聊天对象id
		conversationList:[],		//会话列表
		currentMessageList:[],			//消息列表
		
		userInfo:{} ,//用户信息
		addressBook:{} ,//通讯录
		hospitalsBook:{} ,//医院通讯录
		unreadtotal:0, //站内未读消息数
		operationID:'', //openIM的operationID
		yunstore:{},
		helper:{},
		unipushinit:false,
		apptabstatus:0 //1前台 2后台
	},
    mutations: {
		//更新登录状态
		toggleisTimLogin(state, isTimLogin) {
		  state.isTimLogin = typeof isTimLogin === 'undefined' ? !state.isTimLogin : isTimLogin
		},
		//更新TIMSDK状态
		toggleIsSDKReady(state, isSDKReady) {
		  state.isSDKReady = typeof isSDKReady === 'undefined' ? !state.isSDKReady : isSDKReady
		},
		//退出登录重置状态
		reset(state) {
			state.isTimLogin = false
			state.isSDKReady = false
		},
		//选择好友聊天--创建会话/拼接会话id
		createConversationActive(state,toUserId){
			state.conversationActive.conversationID = 'C2C'+toUserId
			state.toUserId = toUserId
			state.currentMessageList = []
		},
		//选择已有会话聊天--更新选中会话详情
		updateConversationActive(state,conversationItem){
			state.conversationActive = Object.assign({}, conversationItem.conversation);
			state.toUserId = conversationItem.user.userId
			state.currentMessageList = []
		},
		//更新会话列表
		updateConversationList(state,newConversationList){
			state.conversationList = newConversationList
		},
		/**
		 * 将消息插入当前会话列表
		 * 调用时机：收/发消息事件触发时
		 * @param {Object} state
		 * @param {Message[]|Message} data
		 * @returns
		 */
		pushCurrentMessageList(state, data) {		
		  // 还没当前会话，则跳过
		  if (Array.isArray(data)) {
				data=data.map(n =>{
					n.ID=n.ID.replace(/@+/g,"")
					return n
				})
		    // 筛选出当前会话的消息
		    const result = data.filter(item => item.conversationID === state.conversationActive.conversationID)
				// console.log("mmmm"+result)
				state.currentMessageList = [...state.currentMessageList, ...result]
		  } else if (data.conversationID === state.conversationActive.conversationID) {
				data.ID=data.ID.replace(/@+/g,"")
		    state.currentMessageList = [...state.currentMessageList, data]
		  }
		},
		/**
		 * 滑到顶部请求更多的历史消息
		 * */
		unshiftCurrentMessageList(state,data){
			if(data){
				state.currentMessageList = [...data,...state.currentMessageList]
			}
		},
		updateuserInfo(state,data){
			state.userInfo = Object.assign({},state.userInfo, data);
		},
		updateAddressBook(state,data){
			state.addressBook = data;
		},
		updateHospitalsBook(state,data){
			state.hospitalsBook = data;
		},
		updateUnreadtotal(state,data){ //修改站内未读消息
			state.unreadtotal = data;
		},
		updateOperationID(state,data){ //修改站内未读消息
			state.operationID = data;
		},
		updateyunstore(state,data){
			state.yunstore = data
		},
		updatehelper(state,data){
			state.helper = data
		},
		updateUnipushstatus(state,data){
			state.unipushinit = data
		},
		updateapptabstatus(state,data){
			state.apptabstatus = data
		}
	},
	
    actions: {
		
	}
})
export default store