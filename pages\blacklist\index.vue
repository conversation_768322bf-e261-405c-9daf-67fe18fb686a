<template>
<view>
	<mescroll-body ref="mescrollRef" @init="mescrollInit" :down="downOption" @down="downCallback" @up="upCallback">
		<view  class="list flex-start"  v-for="n in dataList">
			<image class="avatar" v-if="!n.face" src="/static/img/default.png" mode=""></image>
			<image class="avatar" v-if="n.face" :src="$baseUrl+n.face" mode=""></image>
			<view class="right">
				<view class="flex-between wrap">
					<view class="name">{{n.realname}}</view>
					<view class="gender">{{n.time}}</view>
				</view>
			</view>
		</view>
	</mescroll-body>
</view>
</template>

<script>
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
				dataList: []
			}
		},
		methods: {
			/*下拉刷新的回调 */
			downCallback() {
				this.dataList=[]
				this.mescroll.resetUpScroll()
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				let that = this
				this.$api.get("api/patient/blacklist/getlist",{params:{pager: page.num}} )
				.then(res=>{
					that.mescroll.endSuccess(res.length);
					//设置列表数据
					that.dataList=this.dataList.concat(res);
				})
				.catch(res =>{
					that.mescroll.endErr()
				})
			}
		}
	}
</script>

<style lang="scss">
	.list {
		border-bottom: 1px solid $uni-border-color;
		padding: $uni-spacing-col-sm*2 $uni-spacing-row-sm;
		.avatar {
			width: $uni-img-size-lg;
			height: $uni-img-size-lg;
			border-radius: $uni-border-radius-circle;
			margin-right: $uni-spacing-row-base;
		}
		.name {
			color: $uni-text-color;
			font-size: $uni-font-size-base;
			margin-right: $uni-spacing-row-sm;
		}
		.gender{
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
		}
		.item {
			color: $uni-text-color-grey;
			font-size: $uni-font-size-sm;
		}
		.wrap{
			flex-wrap: wrap;
		}
	}
</style>
