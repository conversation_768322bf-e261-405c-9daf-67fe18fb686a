<template>
	<view class="base" v-show="i === index">
		<mescroll-uni class="friend" ref="mescrollRef2" height="87%" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption" :up="upOption">
			<navigator :url="'./hospitalDetail/hospitalDetail?id='+n.id" class="list flex-start" v-for="n in history">
				<image v-if="n.face" class="avatar" :src="$hisBaseUrl+n.face" mode=""></image>
				<image v-else class="avatar" src="../../static/img/defaultman.png" mode=""></image>
				<view class="right">
					<view class="name">{{n.name}}</view>
					<view class="name">评分：{{ n.score }}</view>
					<view class="item">
						<text>{{ n.address }}</text>					
					</view>
					<view class="item">
						<text>查看时间：{{ n.visitTime }}</text>					
					</view>
				</view>
			</navigator>
		</mescroll-uni>
	</view>
</template>

<script>
import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
import MescrollUni from '@/components/mescroll-uni/mescroll-uni.vue';
import { mapState } from 'vuex';
export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MescrollUni
	},
	props:{
		i: Number, // 每个tab页的专属下标
		index: { // 当前tab的下标
			type: Number,
			default(){
				return 0
			}
		}
	},
	data() {
		return {
			// 下拉刷新的配置(可选)
			downOption: {
				auto: true,
				empty: {
					tip: '~ 空空如也 ~', // 提示					
				}
			},
			// 上拉加载的配置(可选)
			upOption: {
				auto: false,
				noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					tip: '~ 空空如也 ~', // 提示					
				}
			},
			history:[],
			// #ifdef MP-WEIXIN
			$hisBaseUrl:''
			// #endif
		};
	},
	computed: {
	},
	created(){
		this.history = uni.getStorageSync('clindHistory')
		console.log(this.history)
		// #ifdef MP-WEIXIN
		this.$hisBaseUrl = getApp().globalData.$hisBaseUrl
		// #endif
	},
	methods: {
		/*下拉刷新的回调*/
		downCallback() {
			this.mescroll.endSuccess(0);
		},
		/*上拉加载的回调*/
		upCallback(page) {
			// 与 mescroll-body 的处理方式一致 >
			this.mescroll.endSuccess(0);
		}
	}
};
</script>

<style lang="scss">
.list {
	align-items: center;
	border-bottom: 1px solid $uni-border-color;
	padding: $uni-spacing-col-sm $uni-spacing-row-sm;
	.avatar {
		width: $uni-img-size-lg;
		height: $uni-img-size-lg;
		border-radius: $uni-border-radius-circle;
		margin-right: $uni-spacing-row-base;
	}
	.name {
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		margin-right: $uni-spacing-row-sm;
	}
	.gender{
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
	}
	.item {
		color: $uni-text-color-grey;
		font-size: $uni-font-size-sm;
	}
}
</style>
