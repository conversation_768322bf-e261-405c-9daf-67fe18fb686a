<template>
	<view id="order" class="order">
		<view class="registration">
			<view class="space-between title">
				<view class="flex-start left">
					<image class="tab" src="/static/img/order_img/prescription.png" mode=""></image>
					<text class="typetext" v-if="data.ordertype==1">挂号订单</text>
					<text class="typetext" v-else>开方订单</text>
				</view>
				<text class="status" v-if="data.status==0">待付款</text>
				<text class="status" v-if="data.status==1">待发货</text>
				<text class="status" v-if="data.status==2">待收货</text>
				<text class="status" v-if="data.status==3&&data.has_evaluation==null">待评价</text>
				<text class="status" v-if="data.status==3&&data.has_evaluation!=null">已评价</text>
			</view>
			<view>
				<view class="flex-start" >
					<text class="label">创建订单时间:</text>
					<text class="ctx">{{data.addDate}}</text>
				</view>
				<view class="flex-start">
					<text class="label">医生姓名:</text>
					<text class="ctx">{{data.doctname}}</text>
				</view>
				<view class="flex-start" v-if="data.cliname">
					<text class="label">医院名:</text>
					<text class="ctx">{{data.cliname}}</text>
				</view>
				<view class="flex-start" v-if="data.department">
					<text class="label">科室:</text>
					<text class="ctx">{{data.department}}</text>
				</view>
				<view class="flex-start" v-if="data.cliaddress">
					<text class="label">诊所地址:</text>
					<text class="ctx">{{data.cliaddress}}</text>
				</view>
				<view class="flex-start" v-if="data.diagnose">
					<text class="label">诊断:</text>
					<text class="ctx">{{data.diagnose}}</text>
				</view>
				
				<view class="flex-end-center">
					<navigator v-if="data.ordertype!=1" :url="'./details?orderid='+data.orderid" class="pay btn">查看详情</navigator>
					<text v-if="data.status==0" class="pay btn" @tap=pay(data.orderid)>付款</text>
					<text v-if="data.status==3&&data.has_evaluation==null" class="evaluation btn" @tap="edit()">评价</text>
				</view>
			</view>
		</view>
		<ygc-comment ref="ygcComment"
		        :placeholder="'发布评论'" 
		        @pubComment="evaluation"></ygc-comment>
	</view>
</template>

<script>
export default {
	props:{
		data:Object
	},
	data() {
		return {};
	},
	methods:{
		edit(){
			 this.$refs.ygcComment.toggleMask("show")
		},
		pay(val){
			this.$emit('pay',val)
		},
		evaluation(val){ //评价
			this.$refs.ygcComment.toggleMask()
			this.$api.post('api/patient/evaluation/evaluate',{
				orderid:this.data.id,
				evaluation:val.content,
				score:val.score
			}).then(res =>{
				// console.log(res)
				this.$emit('tabChange');
				uni.showToast({
				    title: res.msg,
				    duration: 2000
				});
			}).catch( err =>{
				
			})
		},
	}
};
</script>

<style lang="scss" scoped>
.order {
	margin: $uni-spacing-col-lg $uni-spacing-row-lg 0px;
	background-color: $uni-bg-color;
		border-radius: $uni-border-radius-lg;
	.registration {
		.title{
			border-bottom:  1px solid $uni-border-color;
		}
		margin:0px $uni-spacing-row-lg ;
		padding: $uni-spacing-col-lg 0px;
		.left {
			align-items: center;
			image {
				width: $uni-img-size-base;
				height: $uni-img-size-base;
			}
			.typetext {
				color: $uni-text-color;
				font-size: $uni-font-size-base;
				font-weight: bold;
			}
		}
		.status {
			color: #ec4040;
			font-size: $uni-font-size-base;
			font-weight: bold;
		}
		.label {
			display: inline-block;
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
			width: 100px;
		}
		.ctx {
			color: $uni-text-color;
			font-size: $uni-font-size-base;
			display: inline-block;
			width: calc(100% - 100px);
		}
	}
	.pay{
		display: inline-block;
		color: #EC4040;
		border: 1px solid #EC4040;
		font-size: $uni-font-size-base;
		margin-top: $uni-spacing-col-sm;
		border-radius: $uni-border-radius-lg*2;
		padding:$uni-spacing-col-sm/2 $uni-spacing-row-sm;
	}
	.btn{
		margin-left: $uni-spacing-row-lg;
	}
	.evaluation{
		display: inline-block;
		color: #EC4040;
		margin-top: $uni-spacing-col-sm;
		border: 1px solid #EC4040;
		font-size: $uni-font-size-base;
		border-radius: $uni-border-radius-lg*2;
		padding:$uni-spacing-col-sm/2 $uni-spacing-row-sm;
	}
}
</style>
