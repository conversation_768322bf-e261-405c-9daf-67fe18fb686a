<template>
	<view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" :down="downOption" @down="downCallback" @up="upCallback">
			<view class="placeholder"></view>
			<view class="inquiry flex-start" v-for="n in inqList" @click="tochatView(n)">
				<view class="left">
					<image class="avatar" v-if="n.face" :src="$baseUrl+n.face" mode=""></image>
					<image class="avatar" v-if="!n.face&&n.gender==0" src="../../static/img/defaultgirl.png" mode="">
					</image>
					<image class="avatar" v-if="!n.face&&n.gender==1" src="../../static/img/defaultman.png" mode="">
					</image>
					<text class="unreadCount" v-show="n.unreadCount">{{ n.unreadCount }}</text>
				</view>
				<view class="rigth">
					<view class="space-between item1">
						<view class="flex-start">
							<text class="name">{{ n.doctname }}</text>
							<text v-if="n.department">{{ n.department }}</text>
						</view>
					</view>
					<view class="item1 space-between">
						<text class="cliname">{{ n.cliname }}</text>
					</view>
					<view class="flex-start item2" v-if="n.id!=0">
						<view class="title">
							图文咨询：
						</view>
						<view class="to">
							<text v-if="n.status == 0 && n.ischarge == 1">待接诊</text>
							<text v-if="n.status == 1 && n.ischarge == 1">服务中</text>
							<text v-if="n.status == 2 && n.ischarge == 1">已结束</text>
							<text v-if="n.ischarge == 2">退款中</text>
							<text v-if="n.ischarge == 3">已退款</text>
						</view>
						<view class="time">
							<text v-if="n.status == 1&&n.ischarge == 1">接诊时间：{{ n.startdate }}</text>
							<text v-if="n.status == 2&&n.ischarge == 1">结束时间：{{n.enddate}}</text>
						</view>
						<view v-if="n.status == 0 && n.isBefore && n.ischarge == 1" class="refund"
							@click.stop="refund(n.ordernum,1)" type="default">咨询问诊退款</view>
					</view>
					<view class="flex-start item2" v-if="n.family_id!=0">
						<view class="title">
							私人医生：
						</view>
						<view class="to">
							<text v-if="n.family_status == 0 && n.family_ischarge == 1">待接诊</text>
							<text v-if="n.family_status == 1 && n.family_ischarge == 1">服务中</text>
							<text v-if="n.family_status == 2 && n.family_ischarge == 1">已结束</text>
							<text v-if="n.family_ischarge == 2">退款中</text>
							<text v-if="n.family_ischarge == 3">已退款</text>
						</view>
						<view class="time">
							<text
								v-if="n.family_status==1 && n.family_ischarge == 1">接诊时间：{{ n.family_startdate }}</text>
							<text v-if="n.family_status==2 && n.family_ischarge == 1">结束时间：{{ n.family_enddate }}</text>
						</view>
						<view v-if="n.family_status == 0 && n.family_isBefore && n.family_ischarge == 1" class="refund"
							@click.stop="refund(n.family_ordernum,2)" type="default">私人医生退款</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	import {
		mapState
	} from 'vuex';
	import moment from 'moment';
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
				inqList: [],
				// #ifdef MP-WEIXIN
				$baseUrl: '',
				// #endif
				operationID: ''
			};
		},
		computed: {
			...mapState({
				isTimLogin: state => state.isTimLogin,
				isSDKReady: state => state.isSDKReady,
				conversationList: state => state.conversationList
			})
		},
		watch: {
			conversationList(val) {
				if (val.length > 0) {
					this.mergelist(val);
				}
			}
		},
		created() {
			let that = this
			this.operationID = this.$store.state.operationID
			that.$IMSDK.subscribe(
				that.$IMSDK.IMEvents.OnConversationChanged,
				({
					data
				}) => {
					// data 会话信息
					this.mergelist(data)
					
				}
			);
		},
		onLoad() {
			let that = this;
			uni.$on('updateInquiry', function() {
				that.inqList = [];
				that.mescroll.resetUpScroll();
			});
			// #ifdef MP-WEIXIN
			this.$baseUrl = getApp().globalData.$baseUrl
			// #endif
		},
		methods: {
			/*下拉刷新的回调 */
			downCallback() {
				this.inqList = [];
				this.mescroll.resetUpScroll();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				console.log(111111111)
				this.$api
					.get('api/patient/inquiry/getlist', {
						params: {
							pager: page.num
						}
					})
					.then(res => {
						this.mescroll.endSuccess(res.length);
						let arr = res.map(n => {
							n.startdate = moment(n.startdate).format('MM/DD HH:mm');
							n.enddate = moment(n.enddate).format('MM/DD HH:mm');
							n.isBefore = (n.payDate != null) ? moment().isAfter(moment(n.payDate).add(12,
								'h')) : false;
							n.family_isBefore = (n.family_payDate != null) ? moment().isAfter(moment(n
								.family_payDate).add(12,
								'h')) : false;
							n.adddate = moment(n.adddate).format('MM/DD HH:mm');
							if (n.family_startdate) {
								n.family_startdate = moment(n.family_startdate).format('MM/DD HH:mm');
							}
							if (n.family_enddate) {
								n.family_enddate = moment(n.family_enddate).format('MM/DD HH:mm');
							}
							return n;
						});
						console.log(arr);
						this.inqList = this.inqList.concat(arr);
						this.getConversationList();
					})
					.catch(err => {
						this.mescroll.endErr();
					});
			},
			//获取消息列表
			getConversationList() {
				this.$IMSDK.asyncApi('getAllConversationList', Date.now().toString())
					.then(({
						data
					}) => {
						// 调用成功
						let conversationList = data
						console.log('conversationList', conversationList)
						if (conversationList.length) {
							this.$store.commit('updateConversationList', conversationList);
						}
					})
					.catch(({
						errCode,
						errMsg
					}) => {
						// 调用失败
						console.log(errCode, errMsg)
					});
			},
			//获取未读消息数
			mergelist(conversationList) {
				let unreadtotal = 0;
				this.inqList = this.inqList.map(item => {
					conversationList.forEach(item1 => {
						if (item1.userID == item.imname) {
							item.unreadCount = item1.unreadCount;
							if (item1.latestMsg) {
								item.lastMsgId = JSON.parse(item1.latestMsg).clientMsgID
							}
						}
					});
					return item;
				});
				getApp().getunread()
			},
			tochatView(inq) {
				console.log(inq)
				if (!inq.imname) {
					uni.showToast({
						title: '该用户账户异常，无法开启对话',
						icon: 'none'
					})
					return
				}
				let that = this
				if (inq.status == 0 && inq.family_status == 0) {
					uni.showToast({
						icon: 'none',
						title: '等待医生接诊!'
					});
					return;
				}
				//选择用户聊天
				this.$IMSDK.asyncApi('getOneConversation', Date.now().toString(), {
						sourceID: inq.imname,
						sessionType: 1
					})
					.then((data) => {
						// 调用成功
						that.$store.commit('createConversationActive', inq.imname);
						uni.navigateTo({
							url: '../tim/room?conversationID=' + data.data.conversationID + '&toUserInfo=' +
								encodeURIComponent(JSON.stringify(inq)) + '&status=' + inq.status + '&id=' + inq.id
						});
					})
					.catch((err) => {
						// 调用失败
						console.log(errCode)
						if (err.errCode == 1004) {
							uni.showToast({
								icon: 'none',
								title: '对方账号异常，无法开启会话！'
							});
						} else {
							uni.showModal({
								title: '服务异常',
								content: '检测到APP状态异常，可能影响正常使用，是否重启应用？',
								confirmText: '重启应用',
								success(res) {
									if (res.confirm) {
										plus.runtime.restart()
									}
								}
							})
						}
					});
			},
			refund(val, type) {
				//退费
				let that = this
				console.log(val);
				let content

				if (type == 1) {
					content = '该医生长时间未接诊您的咨询问诊服务，是否申请咨询问诊服务退费？'
				} else if (type == 2) {
					content = '该医生长时间未接诊您的私人医生服务，是否申请私人医生服务退费？'
				}
				uni.showModal({
					title: '温馨提示',
					content: content,
					success(result) {
						if (result.confirm) {
							that.$api
								.post('api/patient/order/refund', {
									ordernumber: val.trim()
								})
								.then(res => {
									console.log(res);
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
									if (res.success) {
										that.inqList = [];
										that.mescroll.resetUpScroll();
									}
								});
						}
					}
				});
			},
		}
	}
</script>

<style lang="scss">
	page {
		.placeholder{
			padding-top: var(--status-bar-height);	
			height: 110rpx;
		}
		.inquiry {
			// min-height:$jian-list-height-sm ;
			background-color: $uni-bg-color;
			padding: $uni-spacing-col-base;
			margin: $uni-spacing-col-sm $uni-spacing-row-lg;
			margin-bottom: $uni-spacing-col-lg;
			border-radius: $uni-border-radius-base;
			.left {
				margin-top: $uni-spacing-col-sm;
				position: relative;
				width: 120rpx;
				height: 100rpx;
	
				.unreadCount {
					position: absolute;
					right: -10px;
					top: -8px;
					width: 40rpx;
					height: 40rpx;
					border-radius: 50%;
					background: #f06c7a;
					color: #fff;
					line-height: 40rpx;
					text-align: center;
					font-size: 24rpx;
				}
	
				.avatar {
					position: absolute;
					rigth: 0px;
					top: 0px;
					width: 100%;
					height: 100%;
					border-radius: $uni-border-radius-lg;
				}
			}
	
			.rigth {
				width: 100%;
				margin-top: 10rpx;
	
				.flex-start {
					flex-wrap: wrap;
				}
	
				.item1 {
					width: 100%;
					color: $uni-text-color-grey;
					font-size: $uni-font-size-base;
					flex-wrap: wrap;
	
					text {
						margin: 0px $uni-spacing-row-sm;
					}
	
					.name {
						color: $uni-text-color;
						font-size: $uni-font-size-lg;
						font-weight: 700;
					}
				}
	
				.item2 {
					margin: 0px $uni-spacing-row-sm;
					margin-top: 10rpx;
					// color:$jian-bg-color;
					font-size: $uni-font-size-base;
	
					.time {
						margin-left: 20rpx;
						color: $uni-text-color-grey;
						font-size: $uni-font-size-sm;
					}
	
					.cliname {
						color: $uni-text-color-grey;
					}
	
					.refund {
						height: 40rpx;
						width: 200rpx;
						border-radius: 20rpx;
						padding: 0 $uni-spacing-row-sm;
						background-color: #ffffff;
						color: #ec4040;
						border: solid 1rpx #ec4040;
						font-size: $uni-font-size-base;
						margin-left: 15rpx;
						line-height: 40rpx;
						text-align: center;
						margin-left: auto;
					}
				}
	
				.item3 {
					margin: 0px $uni-spacing-row-sm;
					margin-bottom: $uni-spacing-col-sm;
	
	
				}
	
				.flex-end {
					display: flex;
					justify-content: flex-end;
					align-items: center;
				}
			}
		}
	}
</style>