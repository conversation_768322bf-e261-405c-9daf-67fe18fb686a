<template>
	<view>
		<view class="base">
			<view class="details ">
				<view class="status_bar"></view>
				<!-- 分享按钮 -->
				<view class="flex-start">
					<image class="left" v-if="details.face" :src="$baseUrl+details.face" mode=""></image>
					<image class="left" v-if="!details.face&&details.gender==1" src="../../static/img/defaultman.png"
						mode=""></image>
					<image class="left" v-if="!details.face&&details.gender==0" src="../../static/img/defaultgirl.png"
						mode=""></image>
					<view class="rigth">
						<view class="flex-start">
							<text class="name">{{details.realname}}</text>
						</view>
						<view class="flex-start">
							<text v-if="details.departmentname">{{details.departmentname}}</text>
							<text>{{details.professional}}</text>
						</view>
						<view class="space-between">
							<text>{{details.cliname}}</text>
						</view>
						<view class="flex-start">
							<text>服务人数:{{details.number}}</text>
							<text>评分:{{details.score}}分</text>
						</view>
						<view>
							<text class="label" v-for="n in details.postcard">{{n}}</text>
						</view>
					</view>
				</view>
			</view>
			<view class="space-padding">
				<view class="desc">
					<view class="item"><text>&bull;您可与医生进行音视频通话</text></view>
					<view><text>&bull;医生接诊后的24小时内您和医生都可以发起通话</text></view>
					<view><text>&bull;音视频通话意外中断，在规定时间内可以重新发起通话</text></view>
					<view><text>&bull;音视频通话有总时长限制，时间未用完且咨询未结束可多次发起通话</text></view>
				</view>
				<view class="pay">
					<view class="service">
						通话总时长{{serviceinfo.duration}}分钟，共支付{{serviceinfo.price}}元
					</view>
				</view>
				<!-- #ifdef APP-PLUS -->
				<view class="select">选择支付方式：</view>
				<view class="paytype flex-start">
					<image class="alipay" src="/static/img/doctors_img/zhifubao.png" mode=""></image>
					<view class="right space-between">
						<view>
							<view><text>支付宝支付</text></view>
							<view><text>推荐支付宝用户使用</text></view>
						</view>
						<image src="/static/img/doctors_img/dui.png" mode="" v-if="pay_type==0" @click="Alipay"></image>
						<image src="/static/img/doctors_img/uncheck.png" mode="" v-if="pay_type != 0" @click="Alipay">
						</image>
					</view>
				</view>

				<view class="paytype flex-start">
					<image class="alipay" src="/static/img/doctors_img/weixin.png" mode=""></image>
					<view class="right space-between">
						<view>
							<view><text>微信支付</text></view>
							<view><text>推荐微信用户使用</text></view>
						</view>
						<image src="/static/img/doctors_img/dui.png" mode="" v-if="pay_type==2" @click="WeChat"></image>
						<image src="/static/img/doctors_img/uncheck.png" mode="" v-if="pay_type != 2" @click="WeChat">
						</image>
					</view>
				</view>
				<view class="select">用户须知：</view>
				<!-- #endif -->
				<view class="desc">
					<view><text>&bull;医生将在12小时内接诊，逾期未接诊您可选择继续等待或选择退款。</text></view>
					<view><text>&bull;在医生接诊12小时内未产生通话记录，则您可以选择退款。</text></view>
					<view><text>&bull;医生接诊后，医生和用户都可以发起音视频通话。</text></view>
					<view><text>&bull;呼叫医生如未连接上，请尝试换别的时间再发起通话。</text></view>
					<view><text>&bull;请确保手机摄像头和音频性能正常。</text></view>
					<view><text>&bull;请确保已为应用开启了通知权限，以免无法收到通话呼入消息。</text></view>
					<view><text>&bull;为确保咨询通话的效果，建议使用普通话与医生交流，同时确保在比较安静的环境进行通话。</text></view>
					<view><text>&bull;通话中出影像不清晰，无声音，或异常中断等情况，可尝试重新连接，或联系客服寻求帮助。</text></view>
					<view><text>&bull;医生已接诊，且已与医生进行过正常通话，则无法进行退款。</text></view>
					<view><text>&bull;音视频通话的总时长用完，则通话自动结束且无法再重新发起通话。</text></view>
					<view><text>&bull;咨询中请确保文明交流，互相尊重，配合医生的询问。</text></view>
					<view><text>&bull;建议在通话前准备好病情资料及想咨询的问题，以便有效利用通话时长。</text></view>
					<view><text>&bull;医生将根据患者的实际情况给出诊疗、调理建议。</text></view>
					<view><text>&bull;医生的分析判断以用户提供的信息为基础，如医院做过的检查报告单。需主动向医生提供这些信息，且不可隐瞒病情，如资料和信息不完整，可能影响医生的判断。</text></view>
					<view><text>&bull;由于医生的级别、从业年限、专业经验不同，对同一病情的判断、观点、解决方案存在差异，属于常见情况。对较复杂和较严重的病情，建议找更权威的医生进行咨询，也可以多咨询几位医生做参考。</text></view>
					<view><text>&bull;购买即同意<navigator hover-class="none" url="../login/protocol">
								《用户协议》（包含远程医疗服务风险告知及知情同意书）
							</navigator> 及 <navigator hover-class="none" url="../login/privacy">《隐私保护政策》</navigator>
							</text>
					</view>
				</view>
				<view class="emptyBtn"></view>
				<button class="buyButton" id="newOrder" type="default" @click="payorder()">购买</button>
			</view>
		</view>
		<view class="showmodetip" v-if="showopentip">
			<view class="box">
				<view class="tip_content">
					为了保证您在使用此功能时能有良好的体验，请打开设备的相关权限，如默认关闭，则可能导致视频通话功能无法正常运行
				</view>
				<view class="authority">
					1.通知权限：包含通知权限及"横幅"、"锁屏"通知选择。如无权限则无法正常收到音视频通话的呼入通知。<text @click="openSpecification(1)">（点击查看设置指南）</text>
					<view class="authority_btn" @click="toopensetting">
						前往设置
					</view>
				</view>
				<view class="authority" v-if="platform=='android'">
					2.悬浮窗权限：如无权限则音视频通话时的视频小窗口会有异常。<text @click="openSpecification(2)">（点击查看设置指南）</text>
					<view class="authority_btn" @click="openwindow">
						前往设置
					</view>
				</view>
				<checkbox-group @change="changebox">
					<view class="check" >
						<label>
							<checkbox color="#16CC9F" style="transform:scale(0.7)" value="nomore" />
							<text>不再提示</text>
						</label>
					</view>
				</checkbox-group>
				<view class="btns">
					<view class="btn" @click="canceltip">
						我已开启所需权限
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				details: '',
				serviceid: 1,
				pay_type: -1,
				serviceinfo: {

				},
				nomoretip: false,
				showopentip:false,
				platform : uni.getSystemInfoSync().platform
			};
		},
		onLoad(option) {
			this.details = JSON.parse(decodeURIComponent(option.details));
			this.getservice()
			if(!uni.getStorageSync('nomorepushtip')){
				this.showopentip = true
			}
		},
		methods: {
			Alipay() {
				this.pay_type = 0
			},
			// 点击了微信支付之后 支付宝支付为未勾选状态
			WeChat() {
				this.pay_type = 2
			},
			payorder() {
				let that = this;
				if (this.pay_type == -1) {
					uni.showToast({
						title: '请选择支付方式',
						icon: 'error'
					})
					return
				}

				this.$api.post('api/patient/videocall/generate', {
					pay_type: this.pay_type,
					doctor: this.details.id
				}).then(res => {
					if (res.success) {
						this.pay(res.data.OrderNumber)
					} else {
						uni.showModal({
							title: "温馨提示",
							content: res.msg,
							showCancel: false,
							success(data) {
								if (data.confirm) {
									if (res.error == '1006') {
										uni.navigateTo({
											url: "../order/index?tabIndex=1"
										})
									} else if (res.error == '1002') {
										uni.navigateTo({
											url: "../user/userDetails"
										})
									} else if (res.error == '1007') {
										uni.switchTab({
											url: "../inquiry/index"
										})
									}
								}
							}
						})
					}
				})
			},
			pay(OrderNumber) {
				let that = this
				// 请求支付接口
				that.$api.post('api/patient/order/payold', {
					ordernumber: OrderNumber,
					pay_type: that.pay_type
				}).then(res => {
					if (res.success) {
						let provider
						if (that.pay_type == 0) {
							provider = 'alipay'
						} else if (that.pay_type == 2) {
							provider = 'wxpay'
						}
						// 测试服调用沙盒代码代码，生产服删除
						// var EnvUtils = plus.android.importClass("com.alipay.sdk.app.EnvUtils");
						// EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);
						// 调用支付
						uni.requestPayment({
							provider: provider,
							orderInfo: res.data.PayStr,
							success(res) {
								uni.showToast({
									title: '支付成功！',
									duration: 2000,
									success() {
										uni.$emit('updateInquiry');
										uni.switchTab({
											url: '../inquiry/index'
										});
									}
								});
							},
							fail(err) {
								uni.showModal({
									title: '支付异常',
									content: "支付失败，请前往订单页面完成支付",
									showCancel: false,
									success() {
										uni.navigateTo({
											url: "../order/index?tabIndex=1"
										})
									}
								})
							}
						})
					}else{
						uni.showToast({
							title:res.msg,
							icon:'none'
						})
					}
				})
			},
			getservice() {
				this.$api.get('api/patient/videocall/getdoctorservice', {
					params: {
						drid: this.details.id
					}
				}).then(res => {
					if (res.success) {
						this.serviceinfo = res.data
					}
					console.log(res)
				})
			},
			openSpecification(val){
				let url
				if(val==1){
					url = this.$store.state.helper.notification_pr
				}else if(val==2){
					url = this.$store.state.helper.smallwindow_pr
				}
				plus.runtime.openURL(url)
			},
			changebox(e) {
				console.log(e)
				if (e.target.value.length) {
					this.nomoretip = true
				} else {
					this.nomoretip = false
				}
			},
			canceltip() {
				if (this.nomoretip) {
					uni.setStorageSync('nomorepushtip', 'true')
				}
				this.showopentip = false
			},
			openwindow(){
				var main = plus.android.runtimeMainActivity()  
				var pkName = main.getPackageName()  
				var Settings = plus.android.importClass('android.provider.Settings')  
				var Uri = plus.android.importClass('android.net.Uri')  
				var Build = plus.android.importClass('android.os.Build')  
				var Intent = plus.android.importClass('android.content.Intent')  
				var intent = new Intent(  
				  'android.settings.action.MANAGE_OVERLAY_PERMISSION',  
				  Uri.parse('package:' + pkName)  
				)  
				 main.startActivityForResult(intent, 5004) 
			},
			toopensetting() { //弹窗按钮绑定方法
				if (this.nomoretip) {
					uni.setStorageSync('nomorepushtip', 'true')
				}
				let platform = uni.getSystemInfoSync().platform; //获取安卓还是ios
				if (platform == "ios") { //如果机型是ios，ios由于权限问题，可能需要手动开启
					var UIApplication = plus.ios.import("UIApplication");
					var application2 = UIApplication.sharedApplication();
					var NSURL2 = plus.ios.import("NSURL");
					var setting2 = NSURL2.URLWithString("app-settings:");
					application2.openURL(setting2);
					
					plus.ios.deleteObject(setting2);
					plus.ios.deleteObject(NSURL2);
					plus.ios.deleteObject(application2);
				} else if (platform == "android") { //如果机型是安卓
					var main = plus.android.runtimeMainActivity();
					var pkName = main.getPackageName();
					var uid = main.getApplicationInfo().plusGetAttribute("uid");
					var Intent = plus.android.importClass("android.content.Intent");
					var Build = plus.android.importClass("android.os.Build");
					//android 8.0引导
					if (Build.VERSION.SDK_INT >= 26) { //判断安卓系统版本
						var intent = new Intent("android.settings.APP_NOTIFICATION_SETTINGS");
						intent.putExtra("android.provider.extra.APP_PACKAGE", pkName);
					} else if (Build.VERSION.SDK_INT >= 21) { //判断安卓系统版本
						//android 5.0-7.0
						var intent = new Intent("android.settings.APP_NOTIFICATION_SETTINGS");
						intent.putExtra("app_package", pkName);
						intent.putExtra("app_uid", uid);
					} else {
						//(<21)其他--跳转到该应用管理的详情页
						intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
						var uri = Uri.fromParts(
							"package",
							mainActivity.getPackageName(),
							null
						);
						intent.setData(uri);
					}
					// 跳转到该应用的系统通知设置页
					main.startActivity(intent);
				}
			},
		}
	}
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;

	}

	.status_bar {
		height: 70px;
		width: 100%;
	}

	.space-padding>view {
		margin-top: $uni-spacing-col-lg;
		margin-right: $uni-spacing-row-lg;
		margin-left: $uni-spacing-row-lg;
	}

	.details {
		background-color: $jian-bg-color;
		background: linear-gradient(-8deg, rgba(95, 231, 194, 1), rgba(88, 201, 238, 1));

		image {
			width: 80px;
			height: 60px;
			border-radius: $uni-border-radius-circle*2;
		}

		color: $uni-text-color-inverse;
		font-size: $uni-font-size-base;

		.left {
			margin: $uni-spacing-col-lg 0px;
			margin-left: $uni-spacing-row-lg;
			margin-top: 20px;
		}

		.rigth {
			width: 100%;
			margin: $uni-spacing-col-lg $uni-spacing-row-sm;

			.btn {
				position: absolute;
				right: $uni-spacing-row-sm;
				border: 1px solid #FFFFFF;
				padding: $uni-spacing-col-sm $uni-spacing-row-sm ;
				border-radius: $uni-border-radius-lg*2;
				font-weight: 500;
			}
		}

		text {
			margin: $uni-spacing-col-sm $uni-spacing-row-sm;
		}

		.name {
			font-size: $uni-font-size-base;
			font-weight: bold;
		}

		.label {
			display: inline-block;
			border-radius: $uni-border-radius-lg;
			background-color: $uni-bg-color;
			color: #5FE7C2;
			padding: 2px 6px;
			font-size: $uni-font-size-sm;
		}
	}

	.tu {
		color: $uni-text-color;
		font-weight: bold;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;

		.name {
			// color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
			margin-left: $uni-spacing-col-base;
		}
	}

	.desc {
		// margin-top: $uni-spacing-col-sm;
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;

		.item {
			margin-bottom: $uni-spacing-col-sm;
		}

		navigator {
			color: #007AFF;
			display: inline;
		}
	}

	.pay {
		margin-top: $uni-spacing-col-sm;
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;

		&::after {
			content: '';
			width: 30%;
		}

		.service {
			padding: 0 10rpx;
			margin: 10rpx 10rpx;
			text-align: center;
			line-height: 50rpx;
			color: $jian-bg-color;
		}
	}

	.paytype {
		align-items: center;

		.alipay {
			width: $uni-img-size-lg;
			height: $uni-img-size-lg;
			margin-right: $uni-spacing-row-sm;
		}

		image {
			width: $uni-img-size-sm;
			height: $uni-img-size-sm;
		}

		.right {
			width: calc(100% - 30px);
		}

		// margin-top: $uni-spacing-col-sm;
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-base $uni-spacing-row-lg;
	}

	.select {
		padding: 0 $uni-spacing-row-lg;
		margin-top: $uni-spacing-col-sm;
		font-size: $uni-font-size-base;
	}

	button {
		background-color: #16CC9F !important;
		color: $uni-text-color-inverse !important;
		margin-top: 20px;
		margin-right: $uni-spacing-row-lg;
		margin-left: $uni-spacing-row-lg;
	}

	.buyButton {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		text-align: center;
		height: 110upx;
		line-height: 60upx;
		position: fixed;
		bottom: 0upx;
		left: 0;
		right: 0;
		widht: 100%;
	}

	.emptyBtn {
		height: 90upx;
	}

	.showmodetip {
		position: fixed;
		background-color: rgba(0, 0, 0, .5);
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		z-index: 999;

		.box {
			background-color: #fff;
			width: 80%;
			padding: 30rpx 20rpx;
			border-radius: 20rpx;
			border: solid 2rpx #16CC9F;
			box-sizing: border-box;
			margin: 20vh auto;
			font-size: 28rpx;
			.tip_content {
				
			}
			.authority{
				margin-top: 15rpx;
				text{
					color: $jian-bg-color;
				}
				.authority_btn{
					margin-top: 10rpx;
					width: 150rpx;
					line-height: 60rpx;
					border: solid 2rpx $jian-bg-color;
					color: $jian-bg-color;
					border-radius: 30rpx;
					text-align: center;
					font-size: 28rpx;
					font-weight: bold;
				}
			}
			.check {
				margin-top: 30rpx;
			}

			.btns {
				display: flex;
				justify-content: center;
				margin-top: 30rpx;

				.btn {
					width: 50%;
					line-height: 80rpx;
					color: $jian-bg-color;
					border-radius: 40rpx;
					border: solid 2rpx $jian-bg-color;
					text-align: center;
					font-size: 30rpx;
					font-weight: bold;
				}

				.cancel {
					color: $jian-bg-color;
					background-color: #fff;
				}
			}
		}
	}
</style>