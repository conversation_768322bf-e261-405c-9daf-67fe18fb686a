<template>
	<view>
		<view class="title">
			<view id="face" @tap="updateAvatar()">
				<image class="avatar" :src="userInfo.face" mode=""></image>
				<image class="camera" src="/static/img/camera.png" mode=""></image>
			</view>
		</view>
		<uni-list class="tabs" id="tabs1">
			<view @tap="edit({ title: '修改姓名', name: 'patname', value: userInfo.patname, placeholder: '输入姓名', type: 'tel' })">
				<uni-list-item title="姓名" :rightText="userInfo.patname" thumb="/static/img/username.png"></uni-list-item>
			</view>
			<view @click="show()">
				<uni-list-item title="出生时间" :rightText="userInfo.birth" thumb="/static/img/birth.png"></uni-list-item>
			</view>
			<view><uni-list-item @tap="editgender" title="性别" :rightText="userInfo.gender ? '男' : '女'" thumb="/static/img/gender.png"></uni-list-item></view>
			<!-- <navigator hover-class="none" url="../doctors/doctorsList"><uni-list-item title="邮件" :rightText="userInfo.birth" thumb="/static/img/email.png"></uni-list-item></navigator> -->
		</uni-list>
		<view style="background-color: #F7F7F7;height: 18rpx;width: 100%;margin-top: 20rpx;"></view>
		<uni-list class="tabs" id="tabs2">
			<view @tap="edit({ title: '修改紧急联系人', name: 'emerg_person', value: userInfo.emerg_person, placeholder: '输入紧急联系人' })">
				<uni-list-item title="紧急联系人" :rightText="userInfo.emerg_person" thumb="/static/img/user-tab13.png"></uni-list-item>
			</view>
			<view @tap="edit({ title: '修改紧急联系电话', name: 'emerg_tel', value: userInfo.emerg_tel, placeholder: '输入紧急联系电话' })">
				<uni-list-item title="紧急联系电话" :rightText="userInfo.emerg_tel" thumb="/static/img/user-tab12.png"></uni-list-item>
			</view>
			<navigator hover-class="none" url="../recipientAddress/index"><uni-list-item title="收货地址管理" thumb="/static/img/user-tab10.png"></uni-list-item></navigator>
			<navigator hover-class="none" url="../recipientAddress/authentication"><uni-list-item title="实名认证" thumb="/static/img/user-tab1.png"></uni-list-item></navigator>
		</uni-list>
		<view style="background-color: #F7F7F7;height: 18rpx;width: 100%;margin-top: 20rpx;"></view>
		<hFormAlert
			v-if="cancelShow"
			:value="editctx.value"
			:name="editctx.name"
			:placeholder="editctx.placeholder"
			:title="editctx.title"
			type="'idcard'"
			@confirm="confirm"
			@cancel="cancel"
		></hFormAlert>
		<gender v-if="genderShow" @genderconfirm="genderconfirm" @gendercancel="gendercancel"></gender>
		<e-picker-plus ref="picker" start="1920-01-01" mode="YMD" :defaultValue="userInfo.birth" errorMsg="选择的时间超出当前时间" @confirm="confirmbirth" />
	</view>
</template>

<script>
import uniList from '@/components/uni-list/uni-list.vue';
import uniListItem from '@/components/uni-list-item/uni-list-item.vue';
import { pathToBase64, base64ToPath } from 'image-tools';
import hFormAlert from '@/components/h-form-alert/h-form-alert.vue';
import gender from '@/pages/user/gender.vue';
import moment from 'moment';

export default {
	components: { uniList, uniListItem, hFormAlert, gender },
	onLoad(options) {
		this.userInfo = Object.assign({}, this.userInfo, this.$store.state.userInfo);
		this.userInfo.birth =this.userInfo.birth?this.userInfo.birth:"";
	},
	data() {
		return {
			userInfo: null,
			cancelShow: false,
			genderShow: false,
			editctx: {
				name: '',
				value: '',
				placeholder: '',
				title: '',
				type: 'tel'
			}
		};
	},
	methods: {
		updateAvatar() {
			let that = this;
			getApp().requestAndroidPermission('android.permission.WRITE_EXTERNAL_STORAGE', '存储权限')
			uni.chooseImage({
				count: 1, //默认9
				sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
				// sourceType: ['camera '], //从相册选择
				success: function(res) {
					if(!/^.*\.(jpg|JPG|png|jpeg|webp|gif)$/.test(res.tempFilePaths[0])){
						uni.showModal({
							title:'格式错误',
							content:'只允许上传jpg、png、jpeg、webp、gif格式的图片',
							showCancel:false
						})
						return
					}
					uni.compressImage({
						src: res.tempFilePaths[0],
						quality: 20,
						success: res => {
							pathToBase64(res.tempFilePath)
								.then(base64 => {
									that.modifyavatar(base64.replace(/[\r\n]/g,''));
								})
								.catch(error => {
									console.error(error);
								});
						}
					});
				}
			});
		},
	
		modifyavatar(base64) {
			// console.log(base64)
			let that = this
			this.$api
				.post('api/patient/user/modifyavatar', {
					avatar: base64
				})
				.then(res => {
					if (res.success) {
						// this.userInfo.face = this.$baseUrl + res.msg+ '?' + Math.random();
						// this.$store.commit('updateuserInfo', this.userInfo);
						uni.setStorageSync(that.$baseUrl + res.msg,base64)
						let faceurl = uni.getStorageSync(that.$baseUrl + res.msg)
						that.userInfo.face = faceurl						
						that.$store.commit('updateuserInfo', that.userInfo);
					}
					promise.then(function(imResponse) {
					  console.log(imResponse.data); // 更新资料成功
					}).catch(function(imError) {
					  console.warn('updateMyProfile error:', imError); // 更新资料失败的相关信息
					});
					// console.log(res);
				})
				.catch(err => {
					console.log(err);
				});
		},
		edit(val) {
			this.editctx = Object.assign(this.editctx, val);
			this.cancelShow = true;
		},
		editgender() {
			this.genderShow = true;
		},
		gendercancel() {
			this.genderShow = false;
		},
		cancel() {
			this.cancelShow = false;
		},
		genderconfirm(val) {
			this.updateUser(val);
		},
		confirm(val) {
			this.updateUser(val);
		},
		updateUser(val) {
			this.$api
				.post('api/patient/user/updateinfo', val)
				.then(res => {
					if (res.success) {
						this.userInfo = Object.assign(this.userInfo, val);
						if(Object.keys(val)[0]=='birth'){
							let getage = this.getAge(val.birth)
							console.log(this.userInfo)
							this.userInfo = Object.assign(this.userInfo, {age:getage});
						}
						this.$store.commit('updateuserInfo', this.userInfo);
						this.genderShow = false;
						this.cancelShow = false;
						let promise = this.tim.updateMyProfile({
						  nick: this.userInfo.patname,
						});
						promise.then(function(imResponse) {
						  console.log(imResponse.data); // 更新资料成功
						}).catch(function(imError) {
						  console.warn('updateMyProfile error:', imError); // 更新资料失败的相关信息
						});
						uni.showToast({
							title: '更新成功',
							icon: 'none',
							duration: 2000
						});
					}else{
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				})
				.catch(err => {
					console.log(err);
					this.genderShow = false;
					this.cancelShow = false;
				});
		},
		show() {
			this.$refs.picker.show();
		},
		confirmbirth(e) {
			if(e.errorMsg){
				uni.showModal({
					title:"温馨提示",
					content:e.errorMsg,
					showCancel:false
				})
			}else{
				this.updateUser({ birth: e.result });
			}
		},
		getAge(strAge) {
			var birArr = strAge.split("-");
			var birYear = birArr[0];
			var birMonth = birArr[1];
			var birDay = birArr[2];

			d = new Date();
			var nowYear = d.getFullYear();
			var nowMonth = d.getMonth() + 1; //记得加1
			var nowDay = d.getDate();
			var returnAge;

			if (birArr == null) {
				return false
			};
			var d = new Date(birYear, birMonth - 1, birDay);
			if (d.getFullYear() == birYear && (d.getMonth() + 1) == birMonth && d.getDate() == birDay) {
				if (nowYear == birYear) {
					returnAge = 1; // 
				} else {
					var ageDiff = nowYear - birYear; // 
					if (ageDiff > 0) {
						if (nowMonth == birMonth) {
							var dayDiff = nowDay - birDay; // 
							if (dayDiff < 0) {
								returnAge = ageDiff - 1;
							} else {
								returnAge = ageDiff;
							}
						} else {
							var monthDiff = nowMonth - birMonth; // 
							if (monthDiff < 0) {
								returnAge = ageDiff - 1;
							} else {
								returnAge = ageDiff;
							}
						}
					} else {
						return  "出生日期晚于今天，数据有误"; //返回-1 表示出生日期输入错误 晚于今天
					}
				}
				return returnAge;
			} else {
				return ("输入的日期格式错误！");
			}
		}
	}
};
</script>

<style lang="scss">
page {
	background-color: $uni-bg-color;
	background-image: url(../../static/img/userbg.png);
	background-size: 100% 100px;
	background-repeat: no-repeat;
	.title {
		position: relative;
		height: 140px;
		#face {
			width: 60px;
			height: 60px;
			display: inline-block;
			position: relative;
			z-index: 999;
			top: 70px;
			left: calc(50% - 30px);
			.avatar {
				width: 60px;
				height: 60px;
				border-radius: $uni-border-radius-circle;
			}
			.camera {
				width: $uni-img-size-sm;
				height: $uni-img-size-sm;
				position: absolute;
				bottom: 5px;
				right: 2px;
			}
		}
	}
}
</style>