// 简单的使用示例
'use strict';
const uniPush = uniCloud.getPushManager({appId:"__UNI__14BD443"}) //注意这里需要传入你的应用appId
exports.main = async (event, context) => {
	let res = JSON.parse(event.body)
	return await uniPush.sendMessage({
		"push_clientid": res.cidlist, //填写上一步在uni-app客户端获取到的客户端推送标识push_clientid
		"title": res.title,
		"content": res.content,
		"payload": res.payload,
		"channel": res.channel,
		"options": {
			"XM": {
				"/extra.channel_id": res.options.XM.channel_id,
				"/extra.sound_uri":res.options.XM.sound_uri
			},
			"OP": {
				"/channel_id": res.options.OP
			}
		},
		"settings": {
			"ttl": 259200000
		},
		"sound":res.sound
	})
};
