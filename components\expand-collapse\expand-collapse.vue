<template>
  <view class="expand-collapse-container" >
    <view class="expand-collapse-title" :class="'expand-collapse-' + pos" @click="onExpandClick">
     <view class="title-label item">
      <slot name="title"></slot>
     </view>
     <view class="title-icon item">
      <text>{{ expandText }}</text>
     	<uni-icons  :size="20" class="uni-icon-wrapper" :class="isExpand && 'content-visible'" color="#bbb" type="arrowright" />
     </view>
    </view>
    <view class="expand-collapse-content" id="expand-collapse-content" :class="isExpand ? 'content-visible' : 'content-hidden'">
     <view class="content">
      <slot></slot>
     </view>
    </view>
  </view>
</template>

<script>
export default {
  name: '',
  components: {},
 props: {
  pos: {
   type: String,
   default: 'bottom' // bottom, right
  },
  expandText: {
   type: String,
   default: ''
  }
 },
  data() {
   return {
     isExpand: false, // 是否展开
     
    }
 },
 mounted() {
 },
 methods: {
    
   /**
    * 点击展开按钮时触发
    */
  onExpandClick() {
    
      this.isExpand = !this.isExpand;
    }
  }
}
</script>

<style lang="scss" scoped>
.expand-collapse-container{
 .expand-collapse-title{
  display: flex;
  &.expand-collapse-bottom {
  flex-direction: column;
  .title-icon{
   text-align: center;
  }
 }

 &.expand-collapse-right {
  flex-direction: row;
  justify-content: space-between;
  
 }
 }
 .expand-collapse-content{
  transition: height 1s ease;
  overflow: hidden;
  &.content-hidden{
   height: 0 !important;
  }
  &.content-visible{
   height: 'auto' // 元素高度
  }
  
  .content{
   padding: 5px;
  }
  
 }
 .uni-icon-wrapper{
  transform: rotate(90deg);
  transition: transform .3s ease;
  &.content-visible{
   transform: rotate(-90deg);
  }
 }

 .back {
				width: $uni-img-size-sm;
				height: $uni-img-size-sm;
			}
}
</style>
