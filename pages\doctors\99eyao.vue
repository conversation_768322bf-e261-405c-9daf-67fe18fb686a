<template>
	<view class="content">
	</view>
</template>

<script>
	export default {
		data() {
			return {
			}
		},
		onLoad() {},
		created() {
			// 进入页面显示加载中
			uni.showLoading({
					title: '加载中',
					mask: false
				}),
			// 通过接口获取商城token和user_id
			this.$api.get('api/patient/auto/get99TempToken').then(res => {
				if (res.success) {//创建页面
					this.createweb(res.data.token)
				}else{
					uni.showModal({
						title:'提示',
						content:res.msg,
						showCancel:false,
						success(res) {
							uni.navigateBack()
						}
					})
				}
			})
		},
		methods: {
			ReceiveMessage(event) {
				console.log(event)
			},
			// 创建页面
			createweb(token) {
				// 创建子页面并添加到当前页面 
				var wv = plus.webview.create('http://dev.ygplw.com/mobile?token='+token)  
				var currentWebview = this.$scope.$getAppWebview();
				currentWebview.append(wv);
			},
		}
	}
</script>

<style lang="scss">
	.content {
		background: linear-gradient(-8deg, rgba(95, 231, 194, 1), rgba(88, 201, 238, 1));
	}
</style>
