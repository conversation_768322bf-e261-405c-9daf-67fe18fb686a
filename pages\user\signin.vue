<template>
	<view>
		<ss-calendar :checks="checks" @getprevsigin="prevsigin"></ss-calendar>
		<u-popup v-model="show" mode="center" closeable border-radius="14" width="500rpx" height="380rpx">
			<view class="content" style="padding: 20rpx;text-align: center;">
				<view style="margin-top: 60rpx;line-height: 46rpx;font-size: 32rpx;font-weight: bolder;" v-if="!flag">
					立即签到
				</view>
				<view style="line-height: 46rpx;">每日签到得积分！</view>
				<view class="" style="margin: 30rpx;">
					<u-icon name="checkmark-circle" color="#2979ff" size="108"></u-icon>
				</view>
			</view>
		</u-popup>
		<button type="default" class="setbtncolor" @tap="handleqd" :disabled="flag">{{flag?'已签到':'立即签到'}}</button>
	</view>
</template>

<script>
	import ssCalendar from '@/components/ss-calendar/ss-calendar.vue'
	export default {
		data() {
			return {
				show: false,
				flag: false, // 点击签到之后 显示签到成功并且隐藏 禁用按钮
				checks: [],
				// time:16,
				// checks:JSON.parse(localStorage.getItem('data')) || '[]'
				checks:uni.getStorageSync('storage_key')||'[]',
				todaytime:'',
				userInfo:this.$store.state.userInfo
			}
		},
		components: {
			ssCalendar
		},
		created() {
			const {
				year,
				month,
				day
			} = this.getCurrentDate()
			this.todaytime = year + '-' + month + '-' + day
			this.checks = uni.getStorageSync('sigTimer-'+this.userInfo.userid+year+'-'+month) || []
			if(this.checks.length==0){
				console.log(11111111)
				this.listSig(year,month)
			}
		},
		onLoad(options) {
			if(parseInt(options.flag)){
				this.flag = true
			}
		},
		methods: {
			getCurrentDate() {
				const date = new Date()
				const year = date.getFullYear()
				const month = date.getMonth() + 1
				const day = date.getDate()
				return {
					year,
					month,
					day
				}
			},
			handleqd() {
				const {
					year,
					month,
					day
				} = this.getCurrentDate()
				this.$api.post('api/patient/user_sign/sign').then(res => {
						this.flag = true
						if (this.checks.indexOf(this.todaytime) != -1) {
							return console.log('已经签到')
						} else {
							this.checks.push(this.todaytime)
						    uni.setStorageSync('sigTimer-'+this.userInfo.userid+year+'-'+month, this.checks)
						}
					})
					.catch(err => {
						// error 
					})
			},
			listSig(year,month){
				this.$api.get('api/patient/user_sign/list',{
					params:{
						signdate:year + '-' + month + '-01'
					}
				}).then(res => {
						console.log('qqq',res.data)
						this.checks = this.checks.concat(res.data) 
						uni.setStorageSync('sigTimer-'+this.userInfo.userid+year+'-'+month, res.data)
					})
					.catch(err => {
						// error v2/user_sign/list
						console.log(err,887)
					})
			},
			prevsigin(year,month){
				let that = this
				let prevhistory = uni.getStorageSync('sigTimer-'+this.userInfo.userid+year+'-'+month)
				if(!prevhistory){
					that.listSig(year,month)
				}else{
					let arr = JSON.parse(JSON.stringify(that.checks)).concat(prevhistory)
					let newarrr = new Set(arr)
					that.checks = newarrr
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.setbtncolor {
		background-color: #2B85E4;
		margin: 0 24rpx;
	}
</style>
