.footer {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	font-size: 26upx;
	margin-top: 64upx;
	color: rgba(0, 0, 0, 0.7);
	text-align: center;
	height: 40upx;
	line-height: 40upx;
	position: absolute;
	bottom: 90upx;
	left: 50%;
	transform: translate(-50%);
}

.agreement {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	font-size: 28upx;
	margin-top: 64upx;
	color: rgba(0, 0, 0, 0.7);
	text-align: center;
	height: 40upx;
	line-height: 40upx;
	position: absolute;
	bottom: 40upx;
	left: 50%;
	transform: translate(-50%);
}

.a_mask {
	position: fixed;
	z-index: 99999;
	background-color: rgba(0, 0, 0, 0.5);
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;

}
.header-box {
	width:161upx;
	height:161upx;
	box-shadow:0upx 0upx 60upx 0upx rgba(0,0,0,0.1);
	border-radius:50%;
	background-color: #000000; 
	margin-top: 128upx;
	margin-bottom: 20upx;
	margin-left: auto;
	margin-right: auto;
}
.header-box image{
	width:161upx;
	height:161upx;
	border-radius:50%;
}
.a_box {
	width: 600upx;
	overflow: hidden;
	background-color: #fff;
	border-radius: 5upx;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.gender {
	margin: $uni-spacing-col-lg $uni-spacing-row-lg;
}


