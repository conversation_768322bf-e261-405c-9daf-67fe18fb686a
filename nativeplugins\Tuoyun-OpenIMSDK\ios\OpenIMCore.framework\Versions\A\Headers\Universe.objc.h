// Objective-C API for talking to  Go package.
//   gobind -lang=objc 
//
// File is generated by gobind. Do not edit.

#ifndef __Universe_H__
#define __Universe_H__

@import Foundation;
#include "ref.h"

@protocol Universeerror;
@class Universeerror;

@protocol Universeerror <NSObject>
- (NSString* _Nonnull)error;
@end

@class Universeerror;

@interface Universeerror : NSError <goSeqRefInterface, Universeerror> {
}
@property(strong, readonly) _Nonnull id _ref;

- (nonnull instancetype)initWithRef:(_Nonnull id)ref;
- (NSString* _Nonnull)error;
@end

#endif
