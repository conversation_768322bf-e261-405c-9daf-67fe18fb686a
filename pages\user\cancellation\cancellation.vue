<template>
	<view class="content">
		<view class="bold">为保证你的账号安全，在你提交的注销申请生效前需同时满足以下条件</view>
		<view class="bold">1.账号处于安全状态</view>
		<view class="normal">帐号处于正常使用状态，无被盗风险</view>
		<view class="bold">2.帐号财产已结清</view>
		<view class="normal">没有资产、欠款、未结清的资金和虚拟权益,本帐号及 通过本帐号接入的第三方中没有未完成或存在争议的服 务</view>
		<view class="bold">3.帐号权限解除</view>
		<view class="normal">帐号已解除与其他产品的授权登录或绑定关系</view>
		<view class="bold">4.帐号无任何纠纷，包括投诉举报</view>
		<view class="agree">
			<view class="should">
				<view class="checkbox" @click="ischecked = !ischecked"><image v-if="ischecked" src="../../../static/img/doctors_img/dui.png" mode=""></image></view>
				<view class="read">
					我已阅读并同意
					<navigator class="clink" url="./Cancellation_Agreement" open-type="navigate">《欣九康注销须知》</navigator>
				</view>
			</view>
			<wButton class="button" text="下一步" @click.native="nextStep()"></wButton>
			<!-- <button type="primary" @click="nextStep()">下一步</button> -->
		</view>
	</view>
</template>

<script>
import wButton from '../../../components/watch-login/watch-button.vue'; //button
export default {
	components:{
		wButton
	},
	data() {
		return {
			ischecked: false
		};
	},
	methods: {
		nextStep() {
			if (this.ischecked) {
				uni.navigateTo({
					url: './verification'
				});
			} else {
				uni.showToast({
					title: '请先阅读并同意《欣九康注销须知》后再进行下一步',
					icon: 'none'
				});
			}
		}
	}
};
</script>

<style lang="scss">
.content {
	padding: 0 32rpx 32rpx;
	box-sizing: border-box;
	.bold {
		font-size: 32rpx;
		font-weight: bold;
		margin-top: 20rpx;
	}
	.normal {
		margin-top: 20rpx;
	}
	.agree {
		width: 100%;
		padding: 0 32rpx;
		position: fixed;
		bottom: 20rpx;
		left: 0;
		box-sizing: border-box;
		.should {
			display: flex;
			margin-bottom: 20rpx;
			align-items: center;
			.checkbox {
				width: 32rpx;
				height: 32rpx;
				overflow: hidden;
				border-radius: 50%;
				border: 1rpx solid #6d6d72;
				display: flex;
				justify-content: center;
				align-items: center;
				image {
					width: 100%;
					height: 100%;
				}
			}
			.read {
				margin-left: 10rpx;
				display: flex;
				.clink {
					color: $jian-bg-color;
				}
			}
		}

		button {
			width: 600rpx;
			height: 80rpx;
			color: #fff;
			border-radius: 40rpx;
			background-color: #5fe7c2;
		}
	}
}
</style>
