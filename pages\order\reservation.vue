<template>
	<view id="order" class="order">
		<view class="registration">
			<view class="space-between title">
				<view class="flex-start-center left">
					<image class="tab" src="/static/img/order_img/appointment.png" mode=""></image>
					<text v-if="inquiry.ordertype==1" class="typetext">挂号</text>
					<text v-if="inquiry.ordertype==4" class="typetext">预约挂号</text>
				</view>
				<text class="status" v-if="inquiry.status==0">待付款</text>
				<text class="status" v-if="inquiry.status == 3 && inquiry.has_evaluation != null ">已评价</text>
				<text class="status" v-if="inquiry.status == 3 && inquiry.has_evaluation == null ">待评价</text>
				<text class="status" v-if="inquiry.status==4">订单超时</text>
			</view>
			<view>
				<view class="flex-start">
					<text class="label">创建订单时间:</text>
					<text class="ctx">{{inquiry.addDate}}</text>
				</view>
				<view class="flex-start">
					<text class="label">医生姓名:</text>
					<text class="ctx">{{inquiry.doctname}}</text>
				</view>
				<view class="flex-start">
					<text class="label">诊所名:</text>
					<text class="ctx">{{inquiry.cliname}}</text>
				</view>
				<view class="flex-start">
					<text class="label">科室:</text>
					<text class="ctx">{{inquiry.department}}</text>
				</view>
				<view class="flex-start" v-if="inquiry.cliaddress">
					<text class="label">诊所地址:</text>
					<text class="ctx">{{inquiry.cliaddress}}</text>
				</view>
				<view class="flex-start" v-if="inquiry.ordertype==4">
					<text class="label">预约到诊时间:</text>
					<text class="ctx">{{DateTimeformat(inquiry.arrivedate)}}</text>
				</view>
				<view class="flex-start">
					<text class="label">描述:</text>
					<text class="ctx">可预约近一周内的挂号。</text>
				</view>
				<view class="evaluation" v-if="inquiry.has_evaluation!=null">
					<view>
						<text>评价</text>
						<view class="rate"><uni-rate size="16"  :value="inquiry.has_evaluation.score" disabled="true"></uni-rate></view>
						<view class="grey"><text>{{inquiry.has_evaluation.content}}</text></view>
						<view class="flex-end-center">
							<text class="grey">{{inquiry.has_evaluation.adddate}}</text>
						</view>					
					</view>
				</view>
				<view class="flex-start" v-if="inquiry.status==0">
					<text class="label">待付款金额:</text>
					<text class="ctx notpay">￥{{inquiry.notpaying}}元</text>
				</view>
				<view class="flex-end-center">
					<text class="pay btn" @tap="pay(inquiry.orderid)" v-if="inquiry.status==0">付款</text>
					<text class="evaluationbtn btn" v-if="inquiry.status==3&&inquiry.has_evaluation==null" @tap="edit()">评价</text>
				</view>
			</view>
		</view>
		<ygc-comment ref="ygcComment"
		        :placeholder="'发布评论'" 
		        @pubComment="evaluation"></ygc-comment>
	</view>
</template>

<script>
import ygcComment from '@/components/ygc-comment/ygc-comment.vue';
import uniRate from '@/components/uni-rate/uni-rate.vue'
export default {
	components:{
		ygcComment,
		uniRate
	},
	data() {
		return {};
	},
	props:{
		inquiry:Object
	},
	methods:{
		edit(){
			 this.$refs.ygcComment.toggleMask("show")
		},
		pay(val){
			this.$emit('pay',val)
		},
		evaluation(val){ //评价
			this.$refs.ygcComment.toggleMask()
			this.$api.post('api/patient/evaluation/evaluate',{
				orderid:this.inquiry.id,
				evaluation:val.content,
				score:val.score
			}).then(res =>{
				this.$emit('tabChange');
				uni.showToast({
				    title: res.msg,
				    duration: 2000
				});
			}).catch( err =>{
				
			}).catch(err =>{
				console.log(err)
			})
		},
		DateTimeformat(val){
			let arr_date = val.split(' ');
			let sdate = arr_date[0];
			if (arr_date[1]=="08:00:00"){
				sdate = sdate + ' 上午';
			}else if (arr_date[1]=="14:00:00"){
				sdate = sdate + ' 下午';
			}
			return sdate;
		},
	},
	
};
</script>

<style lang="scss" scoped>
.order {
	margin: $uni-spacing-col-lg $uni-spacing-row-lg 0px;
	background-color: $uni-bg-color;
	border-radius: $uni-border-radius-lg;
	.registration {
		margin: 0px $uni-spacing-row-sm;
		padding: $uni-spacing-col-lg 0px;
		.title{
			border-bottom:  1px solid $uni-border-color;
		}
		.left {
			align-items: center;
			image {
				width: $uni-img-size-base;
				height: $uni-img-size-base;
				margin-right: 5rpx;
			}
			.typetext {
				color: $uni-text-color;
				font-size: $uni-font-size-base;
				font-weight: bold;
			}
		}
		.status {
			color: #ec4040;
			font-size: $uni-font-size-base;
			font-weight: bold;
		}
		.label {
			display: inline-block;
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
			width: 100px;
			margin-left: $uni-spacing-row-base;
			margin-top: $uni-spacing-col-base;
		}
		.ctx {
			margin-top: $uni-spacing-col-base;
			display: inline-block;
			width: calc(100% - 100px);
			color: $uni-text-color;
			font-size: $uni-font-size-base;
		}
		.notpay{
			color: #EC4040;
			font-weight: bold;
		}
		.pay{
			display: inline-block;
			color: #EC4040;
			border: 1px solid #EC4040;
			font-size: $uni-font-size-base;
			
			border-radius: $uni-border-radius-lg*2;
			padding:$uni-spacing-col-sm/2 $uni-spacing-row-sm;
		}
		.btn{
			margin-left: $uni-spacing-row-lg;
		}
		.evaluationbtn{
			display: inline-block;
			color: #EC4040;
			border: 1px solid #EC4040;
			font-size: $uni-font-size-base;
			border-radius: $uni-border-radius-lg*2;
			padding:$uni-spacing-col-sm/2 $uni-spacing-row-sm;
		}
		.evaluation{
			margin-top: $uni-spacing-col-sm;
			margin-bottom:  $uni-spacing-col-lg;
			background-color: #E7FFFA;
			color: $uni-text-color;
			font-size: $uni-font-size-base;
			&>view{
				margin: 0px $uni-spacing-row-base;
				padding: $uni-spacing-col-base 0px;
			}
			.grey{
				color: $uni-text-color-grey;
			}
		}
	}
	.rate{
		margin: 6px 0px;
	}
}
</style>
