<template>
	<view class="a_mask" @tap="cancel">
		<view class="a_box">
			<view class="gender" @tap.stop="setgender(1)">
				<text>男</text>
			</view>
			<view class="gender" @tap.stop="setgender(0)">
				<text>女</text>
			</view>
		</view>
		<!-- <form class="a_box" @submit="formSubmit" @reset="formReset">
			<view class="a_head">
				{{title}}
			</view>
			<view class="a_input">
				<input :type="type" :value="value" :placeholder="placeholder" :name="name"/>
			</view>
			<view class="a_btn">
				<button form-type="reset" :style="{color:cancelColor}">{{cancelText}}</button>
				<button form-type="submit" :style="{color:confirmColor}">{{confirmText}}</button>
			</view>
		</form> -->
	</view>
</template>

<script>
	export default {
		props:{
			title:{
				type:String,
				default:'提示'
			},
			placeholder:{
				type:String,
				default:'请点击输入'
			},
			name:{
				type:String,
				default:'text'
			},
			type:{
				type:String,
				default:'text'
			},
			value:{
				type:String,
				default:''
			},
			cancelColor:{
				type:String,
				default:'#999999'
			},
			confirmColor:{
				type:String,
				default:'#333333'
			},
			cancelText:{
				type:String,
				default:'取消'
			},
			confirmText:{
				type:String,
				default:'确定'
			},
		},
		data() {
			return {

			};
		},
		methods: {
			setgender: function(val) {
				console.log(val)
				this.$emit('genderconfirm',{gender:val})
			},
			cancel: function() {
				this.$emit('gendercancel')
			}
		}
	}
</script>

<style lang="scss">
	.a_mask{
		position: fixed;
		z-index: 99999;
		background-color: rgba(0,0,0,0.5);
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		.a_box{
			width: 600upx;
			overflow: hidden;
			
			background-color: #fff;
			border-radius: 5upx;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%,-50%);
	.gender{
		margin: $uni-spacing-col-lg $uni-spacing-row-lg;
			}
		}
	}
</style>
