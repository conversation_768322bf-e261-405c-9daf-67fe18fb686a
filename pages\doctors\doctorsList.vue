<template>
	<view>
		<!--S 刷选 -->
		<view class="filter">
			<view class="opensel" @click="showtypeselect=!showtypeselect">
				<text>{{searchname}}</text>
				<image :class="{'openarrow':showtypeselect}" src="../../static/img/arrow-sel.png" mode="widthFix"></image>
			</view>
			<view class="filter_box" @click="showtypeselect=false,showfilter=true">
				<text>筛选</text>
				<image src="../../static/img/filter.png" mode="widthFix"></image>
			</view>
			
			<view class="type_list" v-if="showtypeselect" @click="showtypeselect=false">
				<view class="chose_item" :class="{'chosed':search_type==item.value}"  v-for="item in filter_arr" @click.stop="chosetype(item)">
					{{item.text}}
				</view>
			</view>
			<view class="chose_bg" v-if="showfilter" @click="closefilter">
				<view class="chose_box" @click.stop="stopfilter">
					<view class="close" @click="closefilter">
						<image src="../../static/img/close.png" mode="widthFix"></image>
					</view>
					<view class="chose_group">
						<view class="chose_title">
							服务
						</view>
						<view class="chose_list">
							<view class="chose_item" v-for="item in service_list"  :class="{'choseing':item.type == filter_info.service_type}" @click="choseServeice(item.type)">
								{{item.name}}
							</view>
						</view>
					</view>
					<view class="sort">
						<view class="sort_type" @click="chosepricesort(1)" :class="{'sortchose':filter_info.price_sort==1}">
							<view>价格升序</view>
							<view class="top">
								<image v-if="filter_info.price_sort==1" src="/static/img/arrow-up-color.png" mode="widthFix"></image>
								<image v-else src="/static/img/arrow-up.png" mode="widthFix" ></image>
							</view>
						</view>
						<view class="sort_type" @click="chosepricesort(2)" :class="{'sortchose':filter_info.price_sort==2}">
							<view>价格降序</view>
							<view class="down">
								<image v-if="filter_info.price_sort==2" src="/static/img/arrow-up-color.png" mode="widthFix"></image>
								<image v-else src="/static/img/arrow-up.png" mode="widthFix"></image>
							</view>
						</view>
					</view>
					<view class="chose_group">
						<view class="chose_title">
							医生职称
						</view>
						<view class="chose_list">
							<view class="chose_item" v-for="item in professional_list" :class="{'choseing':item == filter_info.professional}" @click="choseProfessional(item)">
								{{item}}
							</view>
						</view>
					</view>
					<!-- <view class="chose_group">
						<view class="chose_title">
							是否需要开方
						</view>
						<view class="chose_list">
							<view class="chose_item" :class="{'choseing':filter_info.prescribing == 1}" @click="filter_info.prescribing=filter_info.prescribing==1?0:1">
								是
							</view>
						</view>
					</view> -->
					
					<view class="btns">
						<view class="btn_filter" @click="reset()">
							重置
						</view>
						<view class="btn_filter" @click="showfilter=false,downCallback()">
							确认
						</view>
					</view>
				</view>
			</view>
		</view>
		<!--E 刷选 -->
		<mescroll-body id="base" top="60" ref="mescrollRef" @init="mescrollInit" :down="downOption" :up="upOption"
			@down="downCallback" @up="upCallback">
			<view class="item" v-for="n in arr" @click="toDoctorInfo(n.id)">
				<view class="flex-start">
					<view class="">
						<image class="avatar" v-if="!n.face && n.gender == 0" src="../../static/img/defaultgirl.png"
							mode=""></image>
						<image class="avatar" v-if="!n.face && n.gender == 1" src="../../static/img/defaultman.png"
							mode=""></image>
						<image class="avatar" v-if="n.face" :src="$baseUrl + n.face"
							onerror="this.src='/static/img/default.png'" mode=""></image>
					</view>
					<view class="rightctx">
						<view class="flex-start-center">
							<text class="name">{{ n.realname }}</text>
						</view>
						<view class="flex-start-center box">
							<text v-if="n.cliname">{{n.cliname}}</text>
							<text v-if="n.professional">{{ n.professional }}</text>
						</view>
					</view>
				</view>
				<view class="">
					<view class="flex-start-center">
						<text class="brief" v-if="n.brief">擅长：{{ n.brief }}</text>
					</view>
					<view class="flex-between-align">
						<view class="">
							<view class="flex-start-center">
								<view class="">
									<image class="star" src="/static/img/doctors_img/star1.png" mode=""></image>
									<text class="score">{{ n.score }}</text>
								</view>
								<text>服务人次：{{ n.number }}</text>
							</view>
							<view class="space-between-center">
								{{	n.lastlogin }}
								<text v-if='n.lastlogin'>最近上线：{{lastLogin(n.lastlogin)}}</text>
							</view>
						</view>
						<button v-if='opentype==2' type="button" class="btnGiving"
							@click.stop="givingDoctor(n)">赠送礼品</button>
					</view>
					<view class="flex-start-center">
						<text class="tab" v-for="label in n.postcard" :key="label">{{ label }}</text>
					</view>
					<view class="service flex-start-center">
						<view v-if="n.isreservation==1 && n.clinid>0" class="flex-start-center">
							<image class="inquiry" src="/static/img/doctors_img/reserve.png" mode=""></image>
							<text class="money">预约挂号￥{{ n.registerfee }}</text>
						</view>
						<view v-if="n.isremote==1" class="flex-start-center">
							<image class="reserve" src="/static/img/doctors_img/inquiry.png" mode=""></image>
							<text class="money">图文咨询￥{{ n.inquiryfee }}</text>
						</view>
						<view v-if="n.is_family_doctor==1" class="flex-start-center">
							<image class="reserve" src="/static/img/doctors_img/family.png" mode=""></image>
							<text class="money">私人医生￥{{ n.family_min_price }}起</text>
						</view>
						<view v-if="n.isvideocall==1" class="flex-start-center">
							<image class="reserve" src="/static/img/doctors_img/videoCall.png" mode=""></image>
							<text class="money">视频通话￥{{ n.video_price }}元/{{n.video_duration}}分钟</text>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	import uniIcons from '@/components/uni-icons/uni-icons.vue';
	import uniDataSelect from '@/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue'
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		components: {
			uniIcons,
			uniDataSelect
		},
		onNavigationBarSearchInputChanged(e) {
			this.keywords = e.text.trim();
		},
		onNavigationBarSearchInputConfirmed() {
			//在这里执行搜索操作
			if (this.keywords.length > 0) {
				this.arr = [];
				this.mescroll.resetUpScroll();
				this.mescroll.scrollTo(0, 0)
			} else {
				uni.showToast({
					icon: 'none',
					title: '请输入关键字后再搜索'
				});
			}
		},
		onNavigationBarButtonTap(e) {
			if (this.keywords.length > 0) {
				if (e.index == 0) {
					this.arr = [];
					this.mescroll.resetUpScroll();
					this.mescroll.scrollTo(0, 0)
				}
			} else {
				uni.showToast({
					icon: 'none',
					title: '请输入关键字后再搜索'
				});
			}
		},
		data() {
			return {
				upOption: {
					auto: true,
					page: {
						num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
						size: 7 // 每页数据的数量,默认10
					}
				},
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
				args: {
					
				},
				arr: [],
				pulltext: '',
				// #ifdef MP-WEIXIN
				$baseUrl: '',
				// #endif
				opentype: 1,
				searchname:'综合排序',
				filter_arr:[
					{value:1,text:'综合排序'},
					{value:2,text:'评分从高到低'},
					{value:3,text:'服务数从高到低'}
				],
				// 职称列表
				professional_list:[
					"主任医师","副主任医师","主治医师","医师"
				],
				//服务列表
				service_list:[
					{
						type:1,
						name:'图文问诊'
					},
					{
						type:2,
						name:'私人医生'
					},
					{
						type:3,
						name:'视频通话'
					},
					{
						type:4,
						name:'预约挂号'
					}
				],
				filter_info:{
					search_type:1, 
					professional:'' ,//职称
					service_type:'' ,//服务类型 1图文问诊 2私人医生 3视频通话 4预约挂号
					price_sort:'',//1价格升序 2价格降序
					prescribing:0,//是否需要医生开方 0不需要 1需要
				},
				keywords:'',//关键词
				pager:0,//页码
				clind_id:'',//医院id，如果有此id则只搜索本院的医生
				search_type:1,//排序规则 1综合 2评分 3服务数
				showfilter:false,
				showtypeselect:false
			};
		},
		onLoad: function(option) {
			this.opentype = option.type
			if (option.id) {
				this.clind_id = option.id
			}

		},
		created() {
			// #ifdef MP-WEIXIN
			this.$baseUrl = getApp().globalData.$baseUrl
			// #endif
		},
		watch: {
		},
		methods: {
			// 赠送医生
			givingDoctor(item) {
				this.doctorId = item.id
				uni.navigateTo({
					url: 'confirmPresent?doctorId=' + this.doctorId
				})
			},
			/*下拉刷新的回调 */
			downCallback() {
				this.arr = [];
				// 下拉刷新的回调,默认重置上拉加载列表为第一页 (自动执行 page.num=1, 再触发upCallback方法 )
				this.mescroll.resetUpScroll();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				this.pager = page.num;
				this.getdoctorlist();
			},
			getdoctorlist() {
				//获取医生列表
				var tmp = JSON.parse(JSON.stringify(this.filter_info))
				if(this.clind_id){
					tmp.clind_id = this.clind_id
				}
				tmp.search_type = this.search_type
				tmp.pager = this.pager
				tmp.keywords = this.keywords
				console.log(tmp)
				for(var key in tmp){
				    if(tmp[key] ===''){
				       delete tmp[key]
				    }
				 }
				let that = this;
				this.$api
					.get('api/patient/doctor/selectdoctorlist', {
						params: tmp
					})
					.then(res => {
						console.log(res)
						if(res.success){
							that.arr = this.arr.concat(res.data);
							let hasNext = res.data.length < 7 ? false : true;
							that.mescroll.endSuccess(res.data.length, hasNext)
						}else{
							that.mescroll.endErr();
						}
						
					})
					.catch(res => {
						that.mescroll.endErr();
					});
			},
			toDoctorInfo(id) {
				uni.navigateTo({
					url: '/pages/doctors/doctorsDetails?id=' + id
				});
			},
			chosetype(data){
				this.search_type=data.value
				this.searchname=data.text
				this.showtypeselect=false
				this.downCallback()
			},
			closeKeyboard() {
				if (event.keyCode == 13) {
					event.currentTarget.blur();
				}
			},
			// 最后活跃时间

			lastLogin(val) {
				let time = val.split(' ')[0]
				return time
			},
			choseFliterType(e){
				this.downCallback()
			},
			//选择服务
			choseServeice(serviceId){
				if(this.filter_info.service_type&&this.filter_info.service_type == serviceId){
					this.filter_info.service_type=''
					this.filter_info.price_sort = ''
				}else{
					this.filter_info.service_type=serviceId
				}
			},
			//选择医生职称
			choseProfessional(professional){
				if(this.filter_info.professional&&this.filter_info.professional == professional){
					this.filter_info.professional=''
				}else{
					this.filter_info.professional=professional
				}
			},
			reset(){
				this.filter_info = {
					search_type:1, 
					professional:'' ,//职称
					service_type:'' ,//服务类型 1图文问诊 2私人医生 3视频通话 4预约挂号
					price_sort:'',//1价格升序 2价格降序
					prescribing:0,//是否需要医生开方 0不需要 1需要
				}
			},
			closefilter(){
				this.showfilter = false
			},
			stopfilter(){},
			// 选择价格排序
			chosepricesort(sorttype){
				let that = this
				if(!that.filter_info.service_type){
					uni.showToast({
						title:'你必须选择一项服务后才能使用价格排序',
						icon:'none'
					})
					return
				}
				if(that.filter_info.price_sort==sorttype){
					that.filter_info.price_sort = ''
				}else{
					that.filter_info.price_sort = sorttype
				}
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;
	}
	.filter{
		display: flex;
		justify-content: space-between;
		padding: 0 20rpx;
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		box-sizing: border-box;
		z-index: 999;
		background-color: $uni-bg-color-grey;
		align-items: center;
		padding-bottom: 10rpx;
		.opensel{
			display: flex;
			align-items: center;
			text{
				font-size: 30rpx;
			}
			image{
				width: 20rpx;
				margin-left: 10rpx;
				transform: rotate(180deg);
			}
			.openarrow{
				transform: rotate(0);
		}
		}
		.type_list{
			position: fixed;
			width: 100%;
			background-color: rgba(0, 0, 0, .5);
			left: 0;
			top: 60rpx;
			height: 100%;
			.chose_item{
				font-size: 32rpx;
				line-height: 50rpx;
				padding: 20rpx;
				background-color: $uni-bg-color-grey;
			}
			.chosed{
				color: $jian-bg-color;
			}
		}
		.filter_box{
			display: flex;
			align-items: center;
			text{
				font-size: 30rpx;
			}
			image{
				width: 30rpx;
				margin-left: 10rpx;
			}
		}
		.chose_bg{
			background-color: rgba(0, 0, 0, .4);
			position: fixed;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
			z-index: 999;
			display: flex;
			align-items: flex-end;
			.chose_box{
				width: 100%;
				background-color: #fff;
				height: 70%;
				padding: 20rpx 30rpx;
				box-sizing: border-box;
				position: relative;
				border-radius: 30rpx 30rpx 0 0;
				.close{
					position: absolute;
					right: 20rpx;
					top: 20rpx;
					image{
						width: 40rpx;
					}
				}
				.chose_group{
					margin-top: 20rpx;
					.chose_title{
						font-size: 36rpx;
						font-weight: bold;
					}
					.chose_list{
						margin-top: 10rpx;
						display: flex;
						flex-wrap: wrap;
						justify-content: space-between;
						.chose_item{
							padding: 0 15rpx;
							background-color: #F5F5F5;
							border: solid 1rpx #F5F5F5;
							line-height: 70rpx;
							border-radius: 35rpx;
							width: calc(30% - 30rpx);
							margin: 10rpx 0;
							font-size: 26rpx;
							text-align: center;
						}
						.choseing{
							border-color: $jian-bg-color;
							background-color: rgba(22,204,159, .3);
						}
						&::after{
							content: '';
							display: bolck;
							width: calc(30% - 30rpx);
						}
					}
				}
				.sort{
					display: flex;
					margin-top: 20rpx;
					justify-content: space-around;
					.sort_type{
						width: 40%;
						text-align: center;
						display: flex;
						justify-content: center;
						align-items: center;
						font-size: 26rpx;
						height: 70rpx;
						border:solid 1rpx #666666;
						color:#666666;
						border-radius: 35rpx;
						image{
							width: 30rpx;
							margin-left: 15rpx;
						}
						.down{
							image{
								transform: rotate(180deg);
							}
						}
					}
					.sortchose{
						color: $jian-bg-color;
						border-color: $jian-bg-color;
					}
				}
				.btns{
					position: absolute;
					width: 100%;
					left: 0;
					bottom: 0;
					display: flex;
					justify-content: space-around;
					padding: 20rpx 0;
					.btn_filter{
						width: 40%;
						background-color: $jian-bg-color;
						line-height: 80rpx;
						border-radius: 40rpx;
						text-align: center;
						color: #fff;
					}
				}
			}
			
		}
	}
	.btnGiving {
		width: 130rpx;
		height: 60upx;
		background-color: #15CB9F;
		font-size: 30upx;
		text-align: center;
		line-height: 60upx;
		color: rgb(255, 255, 255);
		margin-right: 0;
		margin-top: 5rpx;
	}

	#active {
		color: #5fe7c2;
	}

	.flex-end {
		margin-left: auto;
	}

	.nav {
		position: fixed;
		z-index: 9999;
		width: 100%;
		background-color: $uni-bg-color;
		flex-wrap: nowrap;
		padding: $uni-spacing-row-sm 0px;

		&>view {
			.label {
				font-size: $uni-font-size-lg;
				color: $uni-text-color;
				font-weight: 500;
				margin-right: $uni-spacing-row-sm;
			}

			.down {
				display: block;
				width: 0;
				height: 0;
				border-right: 6px solid transparent;
				border-left: 6px solid transparent;
				border-top: 6px solid #9a9a9a;
				border-radius: 3px;
			}

			.top {
				display: block;
				width: 0;
				height: 0;
				border-right: 6px solid transparent;
				border-left: 6px solid transparent;
				border-bottom: 6px solid #9a9a9a;
				border-radius: 3px;
				margin-bottom: 1px;
			}

			.topactive {
				border-bottom: 6px solid #60e7c2;
			}

			.downactive {
				border-top: 6px solid #60e7c2;
			}
		}
	}

	.item {
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;
		margin: 0px $uni-spacing-row-lg;
		margin-top: $uni-spacing-col-lg;
		background-color: $uni-bg-color;
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
		align-items: center;

		.name {
			color: $uni-text-color;
			font-size: $uni-font-size-lg;
			font-weight: bold;
			margin-right: $uni-spacing-row-sm;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.avatar {
			width: 50px;
			height: 50px;
			margin-right: $uni-spacing-row-base;
			border-radius: $uni-border-radius-circle;
		}

		.rightctx {
			width: calc(100% - 60px);

			.titles {
				display: flex;
				justify-content: space-between;
			}

			.flex-start-center {
				flex-wrap: wrap;
			}

			.box {
				margin-top: 5rpx;

				text {
					margin-right: 20rpx;
				}
			}

		}

		.star {
			width: 12px;
			height: 12px;
			margin-right: $uni-spacing-col-sm;
		}

		.brief {
			margin: 10rpx;
			color: $uni-text-color;
			font-size: $uni-font-size-base;
		}

		.score {
			color: #fcac00;
			font-weight: bold;
			font-size: $uni-font-size-base;
			margin-right: $uni-spacing-row-lg;
		}

		.tab {
			margin-top: $uni-spacing-col-sm;
			margin-bottom: $uni-spacing-col-sm;
			margin-right: $uni-spacing-row-sm;
			background: rgba(247, 247, 247, 1);
			font-size: $uni-font-size-base;
			color: #9a9a9a;
			padding: $uni-spacing-col-sm $uni-spacing-row-sm;
			font-size: $uni-font-size-sm;
			border-radius: $uni-border-radius-lg;
		}

		.departmentname {
			margin-right: $uni-spacing-col-sm;
		}

		.inquiry,
		.reserve {
			width: 12px;
			height: 12px;
			margin-right: $uni-spacing-col-sm;
		}

		.money {
			font-size: $uni-font-size-base;
			color: $uni-text-color;
			margin-right: $uni-spacing-row-lg;
		}
	}

	.flex-between-align {
		display: flex;
		justify-content: space-between;
	}

	.service {
		flex-wrap: wrap;
	}
</style>