<template>
  <view class="wave-text-container">
   <span v-for="(char, index) in text" :key="index" :style="{ animationDelay: `${index * 0.1}s` }">
      {{ char }}
    </span>
  </view>
</template>

<script>
export default {
  name: '',
  components: {},
 props: {
  text: {
   type: String,
   default: 'Loading...'
   }
  },
  data() {
    return {}
  },
}
</script>

<style lang="scss" scoped>
.wave-text-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44upx;
  font-size: 24upx;
  
  font-weight: bold;
  span {
   display: inline-block;
   animation: bounce 1s infinite ease-in-out;
 }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20upx);
  }
}
</style>
