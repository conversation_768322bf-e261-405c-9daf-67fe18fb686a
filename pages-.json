{
	"pages": [ //pages数组中第一项表示应用启动页,
		{
			"path": "pages/cover/cover",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false,
				"app-plus": {
					"titleNView": false
				}
			}
		},
		{
			"path": "pages/login/login",
			"style": {
				"navigationBarTitleText": "登录"
			}
		}, {
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "欣九康",
				"titleColor": "#393939",
				"app-plus": {
					"scrollIndicator": "none"
				}
			}
		}, {
			"path": "pages/mall/index",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom"
				}
			}

		}, {
			"path": "pages/login/forget",
			"style": {
				"navigationBarTitleText": "重置密码"
			}
		}, {
			"path": "pages/login/register",
			"style": {
				"navigationBarTitleText": "注册"
			}
		}, {
			"path": "pages/login/verifyphone",
			"style": {
				"navigationBarTitleText": "绑定手机注册"
			}
		},
		{
			"path": "pages/msg/msg",
			"style": {
				"navigationBarTitleText": "消息",
				"app-plus": {
					"scrollIndicator": "none"
				}
			}
		},
		{
			"path": "pages/user/user",
			"style": {
				"navigationBarBackgroundColor": "#5BDCD4",
				"navigationBarTextStyle": "white",
				"navigationBarTitleText": "我的",
				"app-plus": {
					"scrollIndicator": "none"
				},
				"titleNView": {
					"buttons": [{
						"text": "\ue502",
						"fontSrc": "/static/fonts/mui.ttf",
						"fontSize": "26px",
						"color": "#ffffff"
					}]
				}
			}
		}, {
			"path": "pages/user/myGiftList",
			"style": {
				"navigationBarTitleText": "我的礼品",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/user/integralList",
			"style": {
				"navigationBarTitleText": "积分明细",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/medrecord/medrecordList",
			"style": {
				"navigationBarTitleText": "我的病历"
			}
		}, {
			"path": "pages/medrecord/medrecordDeatils",
			"style": {
				"navigationBarTitleText": "病历详情"
			}
		}, {
			"path": "pages/user/signin",
			"style": {
				"navigationBarTitleText": "签到"
			}
		}, {
			"path": "pages/doctors/doctorsList",
			"style": {
				"app-plus": {
					"scrollIndicator": "none"
				},
				"titleNView": {
					"searchInput": {
						"borderRadius": "15px",
						"placeholder": "疾病,症状,医生名",
						"backgroundColor": "#F7F7F7"
					},
					"buttons": [{
						"text": "搜索",
						"fontSize": "16px",
						"width": "48px"
					}]
				}
			}
		}, {
			"path": "pages/hospitals/hospitalsList",
			"style": {
				"app-plus": {
					"scrollIndicator": "none"
				},
				"titleNView": {
					"searchInput": {
						"borderRadius": "15px",
						"placeholder": "医院名称",
						"backgroundColor": "#F7F7F7"
					},
					"buttons": [{
						"text": "搜索",
						"fontSize": "16px",
						"width": "48px"
					}]
				}
			}
		}, {
			"path": "pages/blacklist/index",
			"style": {
				"navigationBarTitleText": "我的黑名单"
			}
		}, {
			"path": "pages/user/userDetails",
			"style": {
				"navigationBarBackgroundColor": "#5BDCD4",
				"navigationBarTextStyle": "white",
				"navigationBarTitleText": "我的资料"
			}
		}, {
			"path": "pages/inquiry/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/tim/room",
			"style": {
				"navigationBarTitleText": "聊天室",
				"app-plus": {
					"scrollIndicator": "none"
				}
			}
		}, {
			"path": "pages/doctors/doctorsDetails",
			"style": {
				"navigationBarTitleText": "医生详情",
				"app-plus": {
					"titleNView": {
						"type": "transparent"
					}
				}
			}
		}, {
			"path": "pages/addressBook/index",
			"style": {
				"navigationBarTitleText": "我的医生",
				"disableScroll": true, // 禁止原生页面滚动, 解决Android小程序下拉卡顿的问题
				"app-plus": {
					"scrollIndicator": "none",
					"bounce": "none"
				}
			}
		}, {
			"path": "pages/hospitals/myHospitals",
			"style": {
				"navigationBarTitleText": "收藏医院",
				"disableScroll": true, // 禁止原生页面滚动, 解决Android小程序下拉卡顿的问题
				"app-plus": {
					"scrollIndicator": "none",
					"bounce": "none"
				}
			}
		}, {
			"path": "pages/doctors/inquiryNewOrder",
			"style": {
				"app-plus": {
					"titleNView": {
						"type": "transparent"
					}
				}
			}
		}, {
			"path": "pages/doctors/reservationNewOrder",
			"style": {
				"app-plus": {
					"titleNView": {
						"type": "transparent"
					}
				}
			}
		}, {
			"path": "pages/doctors/confirmPresent",
			"style": {
				"navigationBarTitleText": "选择礼品",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/inquiry/health",
			"style": {
				"titleNView": {
					"buttons": [{
						"text": "编辑健康档案",
						"fontSize": "16px",
						"width": "106px"
					}]
				}
			}
		}, {
			"path": "pages/order/index",
			"style": {
				"navigationBarTitleText": "我的订单"
			}
		}, {
			"path": "pages/order/details",
			"style": {
				"navigationBarTitleText": "订单详情",
				"app-plus": {
					"titleNView": {
						"type": "transparent"
					}
				}
			}
		}

		, {
			"path": "pages/recipientAddress/authentication",
			"style": {
				"navigationBarTitleText": "实名认证"
			}

		}, {
			"path": "pages/recipientAddress/verifyexample",
			"style": {
				"navigationBarTitleText": "图片示例"
			}
		}, {
			"path": "pages/recipientAddress/index",
			"style": {
				"navigationBarTitleText": "收货地址管理"
			}
		}, {
			"path": "pages/recipientAddress/editAddress",
			"style": {
				"navigationBarTitleText": "编辑收货地址"
			}
		}, {
			"path": "pages/login/protocol",
			"style": {
				"navigationBarTitleText": "欣九康用户协议及使用须知"
			}
		}, {
			"path": "pages/user/set",
			"style": {
				"navigationBarTitleText": "设置"
			}
		}, {
			"path": "pages/user/AboutUs",
			"style": {
				"navigationBarTitleText": "关于我们"
			}
		}, {
			"path": "pages/order/logistics",
			"style": {}
		},
		{
			"path": "pages/user/edit-phone",
			"style": {
				"navigationBarTitleText": "手机号换绑"
			}
		}, {
			"path": "pages/guide/index",
			"style": {
				"navigationBarTitleText": "用户指南"
			}
		}, {
			"path": "pages/login/privacy",
			"style": {
				"navigationBarTitleText": "欣九康隐私保护政策"
			}
		}, {
			"path": "pages/user/myAppointment",
			"style": {
				"navigationBarTitleText": "我的预约"
			}
		},
		{
			"path": "pages/user/queue",
			"style": {
				"navigationBarTitleText": "查看排队",
				"enablePullDownRefresh": false
			}
		}, {
			"path": "pages/hospitals/hospitalDetail/hospitalDetail",
			"style": {
				"navigationBarBackgroundColor": "#5BDCD4",
				"navigationBarTextStyle": "white",
				"navigationBarTitleText": "医院详情",
				"app-plus": {
					"titleNView": {
						"buttons": [{
							"text": "\ue405",
							"fontSrc": "/static/fonts/mui.ttf",
							"fontSize": "26px",
							"color": "#ffffff"
						}]
					}
				}
			}
		}, {
			"path": "pages/hospitals/hospitalDetail/recommendedList",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/hospitals/hospitalSearch",
			"style": {
				"app-plus": {
					"scrollIndicator": "none"
				},
				"titleNView": {
					"searchInput": {
						"borderRadius": "15px",
						"placeholder": "医院名称",
						"backgroundColor": "#F7F7F7"
					},
					"buttons": [{
						"text": "搜索",
						"fontSize": "16px",
						"width": "48px"
					}]
				}
			}

		}, {
			"path": "pages/hospitals/myCollect",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/hospitals/History",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/hospitals/hospitalDetail/ServiceHistory",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/user/account",
			"style": {
				"navigationBarTitleText": "账户与安全",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/user/cancellation/cancellation",
			"style": {
				"navigationBarTitleText": "申请注销账号",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/user/cancellation/verification",
			"style": {
				"navigationBarTitleText": "注销账号",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/user/cancellation/result",
			"style": {
				"navigationBarTitleText": "注销账号",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/user/cancellation/Cancellation_Agreement",
			"style": {
				"navigationBarTitleText": "欣九康注销须知",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/webview/webview",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/doctors/homeDoctor",
			"style": {
				"navigationBarTitleText": "私人医生",
				"app-plus": {
					"titleNView": {
						"type": "transparent"
					}
				}
			}

		}, {
			"path": "pages/doctors/99eyao",
			"style": {
				"navigationBarTitleText": "",
				"navigationStyle": "custom",
				"app-plus": {
					"titleNView": false,
					"animationType": "slide-in-bottom"
				}
			}

		}

		, {
			"path": "pages/user/stores/store_list",
			"style": {
				"navigationBarTitleText": "门店会员",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/user/stores/members_list",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/user/stores/store_detail",
			"style": {
				"titleNView": {
					"buttons": [{
						"text": "充值记录",
						"fontSize": "16px",
						"width": "72px"
					}]
				}
			}

		}, {
			"path": "pages/user/stores/accountBook",
			"style": {
				"navigationBarTitleText": "我的账单",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/user/stores/rechargeBook",
			"style": {
				"navigationBarTitleText": "充值记录",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/user/stores/tickets",
			"style": {
				"navigationBarTitleText": "我的券包",
				"enablePullDownRefresh": false,
				"titleNView": {
					"buttons": [{
						"text": "无效券",
						"fontSize": "16px",
						"width": "72px"
					}]
				}
			}

		}, {
			"path": "pages/user/stores/coupon_detail",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		},
		{
			"path": "pages/medrecord/health/record_list",
			"style": {
				"navigationBarTitleText": "健康档案",
				"enablePullDownRefresh": false,
				"titleNView": {
					"buttons": [{
						"text": "保存",
						"fontSize": "16px",
						"width": "72px"
					}]
				}
			}

		},
		{
			"path": "pages/user/stores/unuse_tickets",
			"style": {
				"navigationBarTitleText": "无效券",
				"enablePullDownRefresh": false
			}

		}, {
			"path": "pages/doctors/videoCall",
			"style": {
				"navigationBarTitleText": "视频通话",
				"app-plus": {
					"titleNView": {
						"type": "transparent"
					}
				}
			}

		}, {
			"path": "pages/order/videoCall",
			"style": {
				"navigationBarTitleText": "",
				"enablePullDownRefresh": false
			}

		},
		{
			"path": "pages/user/permission",
			"style": {
				"navigationBarTitleText": "权限设置",
				"enablePullDownRefresh": false
			}
		}
	],
	"tabBar": {
		"color": "#B2B2B2",
		"selectedColor": "#16CC9F",
		"backgroundColor": "#ffffff",
		"list": [{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "static/img/home.png",
				"selectedIconPath": "static/img/homeHL.png"
			},
			{
				"pagePath": "pages/msg/msg",
				"text": "消息",
				"iconPath": "static/img/msg.png",
				"selectedIconPath": "static/img/msgHL.png"
			},
			{
				"pagePath": "pages/inquiry/index",
				"text": "咨询",
				"iconPath": "static/img/inquiry.png",
				"selectedIconPath": "static/img/inquiryHL.png"
			},
			{
				"pagePath": "pages/mall/index",
				"text": "商城",
				"iconPath": "static/img/mall.png",
				"selectedIconPath": "static/img/mallHL.png"
			},
			{
				"pagePath": "pages/user/user",
				"text": "我的",
				"iconPath": "static/img/user.png",
				"selectedIconPath": "static/img/userHL.png"
			}
		]
	},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarBackgroundColor": "#FFFFFFFF",
		"backgroundColor": "#393939",
		"app-plus": {
			"scrollIndicator": "none",
			"bounce": "none"
		}
	}
	// "condition": { //模式配置，仅开发期间生效
	// 	"current": 0, //当前激活的模式(list 的索引项)
	// 	"list": [{
	// 		"name": "", //模式名称
	// 		"path": "", //启动页面，必选
	// 		"query": "" //启动参数，在页面的onLoad函数里面得到
	// 	}]
	// }
}