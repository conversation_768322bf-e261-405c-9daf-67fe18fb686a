import Request from 'luch-request'
export const BASEURL = "https://hs.xxjk99.com/";
export const HISBASEURL = "https://his.xxjk99.com/";
let urlArr = [] //url地址
let shownetpop = true
var timer
var testTimer
uni.onNetworkStatusChange(function(res) {
	if (!res.isConnected && shownetpop) {
		shownetpop = false
		let num = 0
		uni.showLoading({
			title: '网络差',
			mask: true
		})
		Disconnecttime()

		function Disconnecttime() {
			let countdown = setTimeout(function() {
				if (num < 10) {
					num = num + 1
					uni.getNetworkType({
						success(res) {
							if (res.networkType === 'none') {
								clearTimeout(countdown)
								Disconnecttime()
							} else {
								clearTimeout(countdown)
								uni.hideLoading()
							}
						}
					})
				} else {
					uni.hideLoading()
					clearTimeout(countdown)
					uni.showModal({
						title: "温馨提示",
						content: "当前网络出问题了，请检查网络连接正常且网络权限已开启后重启应用！",
						cancelText: '设置权限',
						confirmText: '重启应用',
						success(res) {
							if (res.confirm) {
								plus.runtime.restart();
								shownetpop = true
							} else {
								uni.openAppAuthorizeSetting()
								uni.showModal({
									title: '重启应用',
									content: '重启应用以获取数据',
									showCancel: false,
									success() {
										plus.runtime.restart();
									}
								})
							}
						}
					})
				}
			}, 1000);
		}

	} else {
		networkTest()
	}
});
const api = new Request({
	baseURL: BASEURL,
	header: {
		'content-type': 'application/x-www-form-urlencoded',
		'accept': 'application/json'
	},
	custom: {
		retryCount: 0
	},
})
const getTokenStorage = () => {
	let token = ''
	// #ifdef APP-PLUS||MP-WEIXIN
	try {
		token = uni.getStorageSync('token')
	} catch (e) {
		//TODO handle the exception
	}
	// #endif
	// #ifdef H5
	token = getApp().globalData.token;
	// #endif

	return token
}
const networkTest = () => {
	clearTimeout(timer);
	uni.getNetworkType({
		success(res) {
			if (res.networkType == '2G') {
				uni.showToast({
					title: '网络差...',
					mask: true,
					duration: 10000,
					icon: 'loading',
					success() {
						testTimer = setTimeout(function() {
							networkTest()
						}, 10000);
					}
				})
			} else {
				clearTimeout(timer);
				uni.hideToast()
			}
		}
	})
}

api.interceptors.request.use((config) => { // 可使用async await 做异步操作
	config.custom.retryCount += 1
	config.header = {
		...config.header,
		'Authorization': getTokenStorage()
	}
	if (urlArr.indexOf(config.url) != -1) {
		return Promise.reject(config)
	} else {
		urlArr.push(config.url)
	}
	uni.getNetworkType({
		success: function(res) {
			if (res.networkType == 'none' && shownetpop) {
				shownetpop = false
				uni.showModal({
					title: "温馨提示",
					content: "当前网络出问题了，请检查网络连接正常且网络权限已开启后重启应用！",
					cancelText: '设置权限',
					confirmText: '重启应用',
					success(res) {
						if (res.confirm) {
							plus.runtime.restart();
							shownetpop = true
						} else {
							uni.openAppAuthorizeSetting()
							uni.showModal({
								title: '重启应用',
								content: '重启应用以获取数据',
								showCancel: false,
								success() {
									plus.runtime.restart();
								}
							})
						}
					}
				})
			}
		}
	});
	if (config.method == "POST" || config.method == "GET") {
		uni.showLoading({
			title: '加载中..',
			mask: true
		});
	}
	timer = setTimeout(function() { //20秒后关闭加载中弹窗
		uni.hideLoading();
	}, 15000)
	return config
}, config => { // 可使用async await 做异步操作
	return Promise.reject(config)
})

api.interceptors.response.use((response) => {
	console.log(response)
	if (urlArr.indexOf(response.config.url) != -1) {
		urlArr.splice(urlArr.indexOf(response.config.url), 1)
	}
	if (response.errMsg = "request:ok") {
		uni.hideLoading();
		clearTimeout(timer);
		// console.log('response', response)
	}
	return response.data
}, (response) => {
	/*  对响应错误做点什么 （statusCode !== 200）*/
	console.log(response)
	if (urlArr.indexOf(response.config.url) != -1) {
		urlArr.splice(urlArr.indexOf(response.config.url), 1)
	}
	if (response.config.method == "POST" || response.config.method == "GET") {
		uni.hideLoading();
		clearTimeout(timer);
	}
	if (response.statusCode == 422) {
		uni.showModal({
			title: "温馨提示",
			content: response.data.msg
		})
	}
	if (response.statusCode == 401) {
		uni.removeStorageSync('token');
		uni.reLaunch({
			url: "/pages/login/login"
		});
		uni.showModal({
			title: "温馨提示",
			content: '您的账户在另一台设备登录或登录状态已过期，请重新登录。',
			showCancel: false
		});
	} else {
		//将请求异常上传到bugly
		// let crashTool = uni.requireNativePlugin('CL-CrashTool');
		// if (crashTool) {
		// 	crashTool.sendReportException(JSON.stringify(response));
		// }
	}

	if (response.statusCode == 500) {
		let that = this
		uni.showModal({
			title: "温馨提示",
			content: "服务器出问题了，请联系客服，反应情况。",
			showCancel: false
		})
	}
	if (response.statusCode == 404) {
		uni.showModal({
			title: "温馨提示",
			content: "当前访问路径不存在",
		})
	}
	if (response.statusCode == undefined) {
		if (response.config.custom.retryCount < 2) {
			return api.middleware(response.config)
		} else {
			if (shownetpop) {
				uni.hideLoading();
				clearTimeout(timer);
				shownetpop = false
				uni.showModal({
					title: "温馨提示",
					content: "请求失败,请重新操作！",
					showCancel: false,
					success(res) {
						if (res.confirm) {
							shownetpop = true
						}
					}
				})
			}
		}
	}
	return Promise.reject(response)
})

export {
	api
}