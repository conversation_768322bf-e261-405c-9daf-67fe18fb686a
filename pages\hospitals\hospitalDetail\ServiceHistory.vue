<template>
	<view class="content">
		<view class="title space-between">
			<view class="space-between">
				<text class="rectangle"></text>
				<text style="color: #333333;font-size: 16px;">服务动态</text>
			</view>
		</view>
		<view class="list">
			<view class="item" v-for="n in list">
				<view class="headpic">
					<image v-if="n.avatar" :src="$baseUrl+n.avatar" mode="widthFix"></image>
					<image v-else src="../../../static/img/defaultman.png" mode="widthFix"></image>
				</view>
				<view class="doctor">
					<view class="name">
						<text v-if="n.department">{{n.department}}-</text>
						<text v-if="n.professional">{{n.professional}}-</text>
						{{n.doctname}}
					</view>
					<text class="time">{{n.paydate|nolyDay}}</text>
					<view class="msg">
						患者{{n.patname|hidename}}使用了
						<text v-if="n.ordertype==0">远程问诊</text>
						<text v-if="n.ordertype==4">预约挂号</text>
						<!-- <text v-if="n.ordertype==1">挂号</text>
						<text v-if="n.ordertype==2">病历开方</text>
						<text v-if="n.ordertype==3">直接售药</text> -->
						服务
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			clincid: Number & String
		},
		data() {
			return {
				list: [],
				$baseUrl: ''
			};
		},
		created() {
			// #ifdef MP-WEIXIN
			this.$baseUrl = getApp().globalData.$baseUrl
			// #endif
		},
		watch:{
			clincid(){
				this.loadService()
			}
		},
		methods: {
			loadService() {
				this.$api.get('api/patient/hospital/getrecentlogs', {
						params: {
							id: this.clincid
						}
					})
					.then(res => {
						this.list = res
					})
			}
		},
		filters:{
			hidename(val){
				return (val.substring(1, 0)) + '**'
			},
			nolyDay(val){
				let ios_date = val.replace(/-/g,'/')
				let beginTime = new Date(ios_date).getTime()
				let endTime = new Date().getTime()
				let timeDiff = endTime - beginTime
				let days = Math.floor(timeDiff / (24 * 3600 * 1000)); // 计算出天数
				let leavel1 = timeDiff % (24 * 3600 * 1000); // 计算天数后剩余的时间
				let hours = Math.floor(leavel1 / (3600 * 1000)); // 计算天数后剩余的小时数
				let leavel2 = timeDiff % (3600 * 1000); // 计算剩余小时后剩余的毫秒数
				let minutes = Math.floor(leavel2 / (60 * 1000)); // 计算剩余的分钟数
				let showStr = ''
				if(days>30){
					let monthNum = parseInt(days/30)
					return showStr = monthNum + '月前'
				}else if(days>7){
					let weekNum = parseInt(days/7)
					return showStr = weekNum + '周前'
				}else if(0<days<7){
					return showStr = days + '天前'
				}else if(hours>0){
					return showStr = hours + '小时前'
				}else if(minutes>0){
					return showStr = minutes + '分钟前'
				}
			}
		}
	}
</script>

<style lang="scss">
	.content {
		padding: 0 20rpx;

		.title {
			height: 60px;

			.space-between {
				display: flex;
				justify-content: space-between;
				align-items: center;
				color: $uni-text-color;
			}

			.rectangle {
				display: inline-block;
				width: 4px;
				height: 18px;
				background-color: #16cc9f;
				margin-right: 20rpx;
			}
		}

		.list {
			padding-bottom: 20rpx;
			.item {
				display: flex;
				justify-content: space-between;
				margin-top: 25rpx;
				border-bottom: solid 1rpx #C0C0C0;
				.headpic {
					width: 100rpx;
					height: 100rpx;
					overflow: hidden;
					border: solid 1rpx #DDDDDD;
					border-radius: 15rpx;
					image {
						display: block;
						width: 100%;
					}
				}

				.doctor {
					width: calc(100% - 120rpx);
					padding-bottom: 10rpx;
					.name{
						color: #1E1E1E;
						font-size: $uni-font-size-base;
						font-weight: bold;
					}
					.time{
						margin-left: auto;
						font-size: 24rpx;
						color: $uni-text-color-grey;
					}
					.msg {
						color:#595959;
						font-size:26rpx;
					}
				}
			}
		}
	}
</style>
