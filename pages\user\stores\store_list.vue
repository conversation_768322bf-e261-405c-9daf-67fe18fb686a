<template>
	<view>
		<!-- 菜单 -->
		<!-- <view class="top-warp">
			<app-tabs v-model="tabIndex" :tabs="tabs"></app-tabs>
		</view> -->
		<members-list></members-list>
		<!-- <history :index="tabIndex"></history> -->
	</view>
</template>

<script>
	import AppTabs from "@/components/other/app-tabs.vue";
	import membersList from "@/pages/user/stores/members_list.vue";
	import history from "@/pages/user/stores/history";
	export default {
		components: {
			AppTabs,
			membersList,
			history
		},
		data() {
			return {
				tabs: ['会员列表', '最近去过'],
				tabIndex: 0, // 当前tab下标
			};
		}
	}
</script>

<style lang="scss">
page{
	background-color: $uni-bg-color-grey;
}
</style>