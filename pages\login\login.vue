<template>
	<view class="login">
		<view class="content">
			<!-- 头部logo -->
			<view class="header">
				<image :src="logoImage"></image>
			</view>
			<!-- 主体表单 -->
			<view class="main">
				<wInput v-model="phoneData" type="text" placeholder="用户名/电话"></wInput>
				<wInput v-model="passData" type="password" placeholder="密码"></wInput>
			</view>
			<wButton text="登 录" :rotate="isRotate" @click.native="startLogin()" class="wbutton"></wButton>
			<view class="otherway">
				其他登录方式
			</view>
			<view class="other-login">
				<view v-if="isios" class="login-btn login-apple" @click="login_Apple()">
					<image src="../../static/guideimg/apple.png" mode="widthFix"></image>
					<!-- <text>通过pple登录</text> -->
				</view>
				<view class="login-btn login-weixn" @click="login_weixin()">
					<image src="../../static/guideimg/weixin.png" mode="widthFix"></image>
					<!-- <text>微信登录</text> -->
				</view>
				<view class="login-btn login-phone" v-if="showUniverify" @click="login_univerify">
						<image src="../../static/guideimg/phone.png" mode="widthFix"></image>
				</view>
			</view>
			<view class="footer">
				<navigator url="forget" open-type="navigate">找回密码</navigator>
				<text>|</text>
				<navigator url="register" open-type="navigate">注册账号</navigator>
			</view>
			<view class="agreement">
				<!-- 协议地址 -->
				<navigator class='clink' url="./protocol" open-type="navigate">《用户协议》</navigator>
				<navigator class='clink' url="./privacy" open-type="navigate">《隐私政策》</navigator>
			</view>
		</view>
	</view>
</template>

<script>
	var that;
	import wInput from '../../components/watch-login/watch-input.vue'; //input
import wButton from '../../components/watch-login/watch-button.vue'; //button
	import { gotoAppPermissionSetting } from '../../js_sdk/wa-permission/permission'; //权限设置
	export default {
		data() {
			return {
				showUniverify: true,
				//logo图片 base64
				logoImage: '/static/img/logo.png',
				phoneData: '', //用户/电话
				passData: '', //密码
				isRotate: false, //是否加载旋转
				showAgree: false, //协议是否选择
				isios: false,
			};
		},
		components: {
			wInput,
			wButton,
		},
		onLoad: function(option) {
			// let args= plus.runtime.arguments;
			// if(args){
			// 	let obj= {}
			// 	let url = args.split('?')[1]
			// 	let arr = url.split("&");
			// 	for(let i=0;i<arr.length;i++){
			// 		let newArr=arr[i].split("=");
			// 		obj[newArr[0]]=newArr[1];
			// 	}
			// 	let id = obj.drId
			// 	uni.setStorageSync('doctor_id',id)	
			// }
		
		},
		onShow: function() {
			// this.isTimLogin();
		},
		mounted() {
			that = this;
			// this.isTimLogin();
			if (uni.getSystemInfoSync().platform == "ios") {
				this.isios = true;
			}
			this.isAutoUniverify()
		},
	methods: {
			// isTimLogin() {
			// 	//判断缓存中是否登录过，直接登录
			// 	// uni.removeStorageSync('token');
			// 	try {
			// 		// #ifdef APP-PLUS || MP-WEIXIN
			// 		const value = uni.getStorageSync('token');
			// 		// #endif
			// 		// #ifdef H5
			// 		const value = getApp().globalData.token;
			// 		// #endif

			// 		if (value) {
			// 			//有登录信息
			// 			console.log('已登录用户：', value);
			// 			uni.reLaunch({
			// 				url: '/pages/index/index'
			// 			});
			// 		}
			// 	} catch (e) {
			// 		// error
			// 	}
			// },
			startLogin() {
				//登录
				if (this.isRotate) {
					//判断是否加载中，避免重复点击请求
					return false;
				}
				if (this.phoneData.length == '') {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '用户名不能为空'
					});
					return;
				}
				if (!(/^.*(?=.{6,})(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*? ]).*$/.test(this.passData))) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '密码最少6位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符!'
					});
					return;
				}
				this.isRotate = true;
				setTimeout(function() {
					that.isRotate = false;
				}, 3000);

				// #ifdef APP-PLUS
				this.$api.post("api/patient/auth/login", {
					username: this.phoneData,
					password: this.passData,
					// #ifdef APP-PLUS
					client_id: plus.push.getClientInfo().clientid
					// #endif
					// #ifdef H5
					client_id: "21"
					// #endif
				}).then(res => {
					this.isRotate = false;
					console.log(res, 66)
					if (res.success) {
						getApp().globalData.token = "Bearer " + res.msg;
						try {
							uni.setStorageSync('token', "Bearer " + res.msg); //存入缓存
						} catch (e) {
							// error
							console.log(e, 77)
						}
						getApp().fileInfo();
						uni.showToast({
							icon: 'success',
							position: 'bottom',
							title: '登录成功'
						});
						// uni.switchTab({
						// 	url: '/pages/index/index'
						// });
					} else {
						that.passData = '';
						// console.log(res)
						uni.showToast({
							icon: 'error',
							position: 'bottom',
							title: res.msg
						});
					}
				}).catch(err => {
					console.log(err)
					this.isRotate = false;
				})
				// #endif
			},
			//微信登录
			login_weixin() {
				let _this = this;
				uni.login({
					provider: 'weixin',
					success: function(loginRes) {
						console.log(loginRes)
						// 获取用户信息
						uni.getUserInfo({
							provider: 'weixin',
							success: function(infoRes) {
								console.log(infoRes)
								_this.other_login(loginRes, infoRes, 'wx');
							}
						});
					},
					fail: function(res) {
						uni.showToast({
							icon: 'error',
							position: 'bottom',
							title: '请求微信授权失败'
						});
						console.log(res)
					},
				});
			},
			//授权登录
			other_login(loginRes, infoRes, type) {
				let _this = this;
				let url;
				let pram = {};
				switch (type) {
					case 'qq':
						url = '/login-qq';
						pram = {
							'openid': loginRes.authResult.openid,
							'nickname': infoRes.userInfo.nickname,
							'gender': infoRes.userInfo.gender,
							'province': infoRes.userInfo.province,
							'city': infoRes.userInfo.city,
							'figureurl': infoRes.userInfo.figureurl_qq
						}
						break;
					case 'wx':
						url = 'api/v2/oauth/login_oauth';
						pram = {
							'role': 0,
							'type': 1,
							'openid': loginRes.authResult.openid,
							'access_token': loginRes.authResult.access_token,
							'unionid': loginRes.authResult.unionid,
							// #ifdef APP-PLUS
							'client_id': plus.push.getClientInfo().clientid,
							// #endif
							// #ifdef H5
							'client_id': "21",
							// #endif
							'nickname': infoRes.userInfo.nickName,
							'sex': infoRes.userInfo.gender,
							'province': infoRes.userInfo.province,
							'city': infoRes.userInfo.city,
							'country': infoRes.userInfo.country,
							'headimgurl': infoRes.userInfo.avatarUrl,
						}
						break;
					case 'apple':
						url = 'api/v2/oauth/login_oauth_apple';
						pram = {
							identityToken: infoRes.userInfo.identityToken,
							openId: infoRes.userInfo.openId,
							'type': 4,
							// #ifdef APP-PLUS
							'client_id': plus.push.getClientInfo().clientid,
							// #endif
							// #ifdef H5
							'client_id': "21",
							// #endif
							role: 0
						}
						break;
					case 'univerify':
						url = '/api/patient/auth/regorlogin';
						pram = {
							"access_token": loginRes.access_token,
							"openid": loginRes.openid,
							// #ifdef APP-PLUS
							'client_id': plus.push.getClientInfo().clientid,
							// #endif
							// #ifdef H5
							'client_id': "21",
							// #endif
						}
						break;
							
					default:
				}
				console.log(pram, '-登录平台返回的信息')
				//向后端请求微信login
				this.$api.post(url, pram).then(res => {
					console.log(res)
					if (type === 'univerify') {
						uni.closeAuthView()
						if (res.success) {
							res.errno = 0;
							res.data = {}
							res.data.token = res.msg;
							res.msg = '登录成功'
						} else {
							res.errno = 20
						}
					}
					if (res.errno == 0) {
						getApp().globalData.token = "Bearer " + res.data;
						// #ifdef APP-PLUS
						try {
							uni.setStorageSync('token', "Bearer " + res.data.token); //存入缓存
						} catch (e) {
							// error
						}
						// #endif
						getApp().fileInfo();
						uni.showToast({
							icon: 'success',
							position: 'bottom',
							title: res.msg
						});
						uni.switchTab({
							url: '/pages/index/index'
						});
					} else if (res.errno == '10') {
						console.log(res)
						//需要绑定手机号
						uni.navigateTo({
							url: '/pages/login/verifyphone?logindata=' + encodeURIComponent(JSON.stringify(
								pram))
						});
					} else {
						that.passData = '';
						uni.showToast({
							icon: 'error',
							position: 'bottom',
							title: res.msg
						});
					}
				}).catch(err => {
					console.log(err)
				})
			},
			//Apple登录
			login_Apple() {
				let _this = this;
				uni.login({
					provider: 'apple',
					success: function(loginRes) {
						// 登录成功  
						uni.getUserInfo({
							provider: 'apple',
							success: function(infoRes) {
								_this.other_login(loginRes, infoRes, 'apple')
							}
						})
					},
					fail: function(err) {
						// 登录失败 
						// console.log(err)
					}
				});

			},
			//微信聊天分享
			share_weixin() {
				uni.share({
					provider: "weixin",
					scene: "WXSceneSession",
					type: 0,
					href: 'https://hs.xxjk99.com/weixinpat/1',
					title: '李小平中医',
					summary: "李小平医生：https://hs.xxjk99.com/weixinpat/1",
					success: function(res) {
						console.log("success:" + JSON.stringify(res));
					},
					fail: function(err) {
						console.log("fail:" + JSON.stringify(err));
					}
				});
			},
			shareQ_weixin() {
				uni.share({
					provider: "weixin",
					scene: "WXSenceTimeline",
					type: 0,
					href: 'https://hs.xxjk99.com/weixinpat/1',
					title: '李小平中医',
					summary: "李小平医生：https://hs.xxjk99.com/weixinpat/1",
					success: function(res) {
						console.log("success:" + JSON.stringify(res));
					},
					fail: function(err) {
						console.log("fail:" + JSON.stringify(err));
					}
				});
			},
			isAutoUniverify() {
				uni.preLogin({
					provider: 'univerify',
					success: function() {
						this.showUniverify = true
					},
					fail(res){  // 预登录失败
						this.showUniverify = false
							console.err('预登录失败', res);
					}
				})
			},
			/**
				* 手机号一键登录
				*/
			login_univerify() {
				let _this = this;
				uni.login({
					provider: 'univerify',
					univerifyStyle: {
						fullScreen: true,
						icon: {
        path: this.logoImage, // 自定义显示在授权框中的logo，仅支持本地图片 默认显示App logo
        width:  "60px",  //图标宽度 默认值：60px
        height: "60px"   //图标高度 默认值：60px
    		},
					},
					success: function (loginRes) {
						console.log('loginRes.authResult.access_token',loginRes.authResult.access_token);
						_this.other_login({
							access_token: loginRes.authResult.access_token,
							openid: loginRes.authResult.openid
						}, {}, 'univerify')
					},
					fail: function(err) {
						if(err.errCode === 30002) {
							uni.closeAuthView()
						}
						// 登录失败 
						console.error('失败',err)
						
					}
				})
			}
		}
	};
</script>

<style lang="scss">
	@import url('../../components/watch-login/css/icon.css');
	@import url('./css/main.css');

	.otherway {
		text-align: center;
		margin-top: 30rpx;
		font-size: 30rpx;
		color: #555555;
	}

	.other-login {
		margin-top: 50upx;
		width: 100%;
		display: flex;
		justify-content: center;
		height: 200upx;
		color: rgba(0, 0, 0, 0.7);

		.login-btn {
			position: relative;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			color: rgba(0, 0, 0, 0.7);
			font-size: 30upx;
			width: 130rpx;
			height: 130rpx;
			border-radius: 20rpx;
			margin: 0 20rpx;

			image {
				width: 70rpx;
				display: block;
				margin: 0;
				padding: 0;
			}
		}

		.login-weixn {
			background-color: #62b900;
		}

		.login-apple {
			background-color: black;
		}
		.login-phone {
			background-color: #3CB1FF;
		}
	}
</style>