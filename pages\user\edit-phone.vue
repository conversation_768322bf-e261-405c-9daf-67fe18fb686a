<template>
	<view class="base">
		<view class="block1" v-if="!ismscode">
			<view class="tmpone">
				<image src="/static/img/camera.png" mode=""></image>
				<view class="crrut">当前手机号{{oldphoneData}}</view>
				<text>为确认身份，将会发送一条有验证码的短信至旧手机号</text>
				<button class="btn" type="button" @tap="getsmscode()">更换手机号</button>
			</view>
		</view>
		<view class="block2" v-if="ismscode">
			<view class="content">
				<!-- 主体 -->
				<view class="main">
					<wInput
						v-model="phoneData"
						type="text"
						maxlength="11"
						placeholder="请输入新手机号码"
					></wInput>
					<wInput
						v-model="verCode"
						type="number"
						maxlength="6"
						placeholder="验证码"
						
						isShowCode
						codeText="获取验证码"
						setTime="30"
						ref="runCode"
						@setCode="getVerCode()"
					></wInput>
					<wInput
						v-model="oldverCode"
						type="text"
						maxlength="6"
						placeholder="旧手机安全验证码"
					></wInput>
				</view>
				
				<button class="btn" type="button" @tap="modifytelephone()">验证并更换</button>
			
			</view>
		</view>
	</view>
</template>

<script>
var _this;
import wInput from '../../components/watch-login/watch-input.vue' //input
import wButton from '../../components/watch-login/watch-button.vue' //button
export default {
	components:{
		wInput,
		wButton
	},
	data() {
		return {
			ismscode:false,
			phoneData: "", //电话
			oldphoneData:"",
			passData: "", //密码
			verCode:"", //验证码
			oldverCode:"", //旧验证码
			isRotate: false, //是否加载旋转
		};
	},
	mounted() {
		_this= this;
	},
	onLoad(options) {
		this.oldphoneData = options.tel
	},
	methods: {
		getsmscode(){ //获取旧手机号验证码    
			this.$api.get('api/patient/user/getmodifytelephonesmscode').then(res=>{
				this.ismscode = true
			})
		},
		getVerCode(){
			//获取验证码
			if (!(/^1[3|4|5|7|8|9][0-9]{9}$/.test(this.phoneData))) {
			     uni.showToast({
			        icon: 'none',
					position: 'bottom',
			        title: '手机号不正确'
			    });
			    return false;
			}
			this.$refs.runCode.$emit('runCode'); 
			setTimeout(function(){
				_this.$refs.runCode.$emit('runCode',0); 
			},60000)
			this.$api
				.get('api/patient/getsmscode', { params: { telephone: this.phoneData } })
				.then(res => {})
				.catch(err => {});
		},
		modifytelephone() {
			//重置密码
			if(this.isRotate){
				//判断是否加载中，避免重复点击请求
				return false;
			}
			if (!(/^1[3|4|5|7|8|9][0-9]{9}$/.test(this.phoneData))) {
			    uni.showToast({
			        icon: 'none',
					position: 'bottom',
			        title: '手机号不正确'
			    });
			    return false;
			}
		
			if (!(/^\d{6}$/.test(this.verCode))) {
			    uni.showToast({
			        icon: 'none',
					position: 'bottom',
			        title: '验证码不正确'
			    });
			    return false;
			}
			if (!(/^\d{6}$/.test(this.oldverCode))) {
			    uni.showToast({
			        icon: 'none',
					position: 'bottom',
			        title: '安全验证码不正确'
			    });
			    return false;
			}
			_this.isRotate=true
			setTimeout(function(){
				_this.isRotate=false
			},3000)
			this.$api.post('api/patient/user/modifytelephone',{
				telephone:this.phoneData,
				smscode:this.verCode,
				oldsmscode:this.oldverCode
			}).then(res =>{	
				if(res.success){
					this.$store.commit("updateuserInfo",{telephone:this.phoneData})
				}
				uni.showToast({
				    icon: 'none',
				    title: res.msg,
						success() {
							if(res.success){
								uni.navigateBack()
							}
						}
				});
				
			}).catch(err =>{
				
			})
		}
	}
};
</script>

<style lang="scss">
.base{
	margin: 0px $uni-spacing-row-lg;
	// .crrut{text-align: center;}
	text{
		font-size: $uni-font-size-base;
		color: $uni-text-color;
	}
	p{
		font-size: $uni-font-size-base;
		color: $uni-text-color-grey;
	}
}
.btn {
	margin-top: $uni-spacing-row-lg*4;
	font-size: $uni-font-size-lg;
	width: 60%;
	border-radius: 5px;
	color: $uni-text-color-inverse;
	background-color: #15CB9F;
	border-radius: $uni-border-radius-lg*4;
}
input {
	width: 100%;
	height: 40px;
}
.tmpone {
	display: block;
	text-align: center;
	image{
		margin-top: $uni-spacing-row-lg*3;
		width: $uni-spacing-row-lg*4;
		height: $uni-spacing-row-lg*4;
	}
}
.tmptwo {
	display: none;
}
.crrut{
	margin-top: $uni-spacing-row-lg*4;
	margin-bottom: $uni-spacing-row-lg*2;
}
</style>
