import Vue from 'vue'
import App from './App'
import commen from './common/commen.js'
import COS from "cos-wx-sdk-v5";
import store from './store/index.js'
import {
	api
} from '@/common/api.js' // 全局挂载引入，配置相关在该index.js文件里修改
import {
	BASEURL
} from "@/common/api.js"
import {
	HISBASEURL
} from "@/common/api.js"

import MescrollBody from "@/components/mescroll-uni/mescroll-body.vue"
import MescrollUni from "@/components/mescroll-uni/mescroll-uni.vue"
import IMSDK from 'openim-uniapp-polyfill';
Vue.component('mescroll-body', MescrollBody)
Vue.component('mescroll-uni', MescrollUni)
Vue.config.productionTip = false
Vue.prototype.$store = store
Vue.prototype.$commen = commen
Vue.prototype.$api = api
Vue.prototype.$baseUrl = BASEURL
Vue.prototype.$hisBaseUrl = HISBASEURL
Vue.prototype.$IMSDK = IMSDK

Vue.prototype.$YeIMCallKit = uni.requireNativePlugin('wz-YeIMCallKit');
App.mpType = 'app'

const app = new Vue({
	...App
})
app.$mount()