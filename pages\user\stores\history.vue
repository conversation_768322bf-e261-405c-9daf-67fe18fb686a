<template>
	<view v-show="index==1">
		<mescroll-uni class="friend" ref="mescrollRef" height="87%" >
			<view class="store_item" v-for="(item,index) in 8" :key="index" @tap="godetail">
				<view class="title">
					<view class="name">
						诊所名称2
					</view>
					<view class="score">
						诊所评分：5
					</view>
				</view>
				<view class="address">
					诊所地址
				</view>
			</view>
		</mescroll-uni>
	</view>
</template>

<script>
	export default { 
		props:{
			index: {
				// 当前tab的下标
				type: Number,
				default() {
					return 0;
				}
			}
		},
		data() {
			return {
				// 下拉刷新的配置(可选)
				downOption: {
					auto: false
				},
				// 上拉加载的配置(可选)
				upOption: {
					auto: true,
					noMoreSize: 2, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
					empty: {
						tip: '~ 空空如也 ~' // 提示
					}
				},
			};
		},
		created() {
		},
		methods:{
			downCallback(){
				
			},
			upCallback(){
				
			},
			godetail(){
				uni.navigateTo({
					url:'/pages/user/stores/store_detail'
				})
			}
		}
	}
</script>

<style lang="scss">
.store_item{
	width: 90%;
	padding: 24rpx;
	box-sizing: border-box;
	border-radius: 5px;
	margin: 15rpx auto 0;
	background-color: #fff;
	.title{
		display: flex;
		justify-content: space-between;
		.name{
			font-size: 30rpx;
			font-weight: bold;
		}
		.score{
			font-size: 24rpx;
		}
	}
	.address{
		font-size: 24rpx;
		margin-top: 15rpx;
	}
}
</style>
