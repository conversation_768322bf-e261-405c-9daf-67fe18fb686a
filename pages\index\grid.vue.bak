<template>
	<view class="content">
		<navigator url="../medicalRecords/list?type=1" hover-class="none">
			<image src="/static/img/index_img/grid11.png" mode="widthFix"></image>
			<text id="navtext">病历处方</text>
		</navigator>
		<navigator class="item" url="../addressBook/index" hover-class="none">
			<image src="/static/img/index_img/grid5.png" mode="widthFix"></image>
			<text id="navtext">通讯录</text>
		</navigator>
		<navigator class="item" url="../user/share" hover-class="none">
			<image src="/static/img/index_img/grid9.png" mode="widthFix"></image>
			<text id="navtext">添加患者</text>
		</navigator>
		<navigator class="item" url="../recordtemplate/recordtemplate" hover-class="none">
			<image src="/static/img/index_img/grid8.png" mode="widthFix"></image>
			<text id="navtext">处方模板</text>
		</navigator>
		<navigator class="item" url="./remoteSettings" hover-class="none">
			<image src="/static/img/index_img/grid10.png" mode="widthFix"></image>
			<text id="navtext">服务设置</text>
		</navigator>
		<view class="item" @tap="scan()">
			<image src="/static/img/index_img/grid12.png" mode="widthFix"></image>
			<text id="navtext">扫码</text>
		</view>
	</view>
</template>

<script>
	import {
		pathToBase64,
		base64ToPath
	} from 'image-tools';
	export default {
		data() {
			return {
				grid: [{
						url: '/pages/doctors/doctorsList',
						imgsrc: '/static/img/grid1.png',
						navtext: '找医生'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid2.png',
						navtext: '预约挂号'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid3.png',
						navtext: '找诊所'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid4.png',
						navtext: '我的医生'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid5.png',
						navtext: '添加医生'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid6.png',
						navtext: '我的订单'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid7.png',
						navtext: '疾病自查'
					},
					{
						url: '/pages/addressBook/index',
						imgsrc: '/static/img/grid8.png',
						navtext: '我的医生'
					}
				],
			};
		},
		methods: {
			updateAvatar(id, count = 1) {
				let that = this;
				getApp().requestAndroidPermission('android.permission.WRITE_EXTERNAL_STORAGE', '存储权限')
				uni.chooseImage({
					count: 1, // 数量调整：一次只上传一张
					sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
					// sourceType: ['camera '], //从相册选择
					success: function(res) {
						if (!/^.*\.(jpg|JPG|png|jpeg|webp|gif)$/.test(res.tempFilePaths[0])) {
							uni.showModal({
								title: '格式错误',
								content: '只允许上传jpg、png、jpeg、webp、gif格式的图片',
								showCancel: false
							})
							return
						}
						that.compressImages(id, res.tempFilePaths)
					}
				});
			},
			compressImages(id, tempFilePaths) {
				const that = this
				const promises = tempFilePaths.map(filePath => {
					return new Promise((resolve, reject) => {
						uni.compressImage({
							src: filePath,
							quality: 20,
							success: res => {
								pathToBase64(res.tempFilePath)
									.then(base64 => {
										resolve(base64.replace(/[\r\n]/g, ''));
									})
									.catch(error => {
										reject(error);
									});
							},
							fail: err => {
								reject(err);
							}
						});
					});
				});

				Promise.all(promises)
					.then(base64Images => {
						// 所有图片都已成功处理，base64Images 是一个包含所有 Base64 编码的数组
						that.uploadMedImgs(id, base64Images); // 假设 modifyavatars 接受一个数组
					})
					.catch(error => {
						console.error('Error compressing or converting images:', error);
					});
			},
			uploadMedImgs(id, images) {
				let that = this
				this.$api
					.post('api/doctor/medrecord/uploadimage', {
						id,
						images
					})
					.then(res => {
						console.log('api/doctor/medrecord/uploadimage', res);
						if (res.success) uni.showToast({
							title: '上传成功',
							duration: 2000
						})
						else {
							uni.showToast({
								title: res.msg || '上传失败',
								icon: 'none',
								duration: 2000
							})
						}
					})
					.catch(err => {
						console.error(err);
					});
			},
			async scan() {
				let that = this;
				uni.scanCode({
					success: (res) => {
						console.warn('that.$store.state ', that.$store.state);
						console.log(res)
						let result = ''
						try {
							// 扫码上传图片
							result = JSON.parse(res.result)
							if (result?.scanType != 'uploadImage') {
								return uni.showToast({
									title: '未识别',
									icon: 'none',
									duration: 2000
								})
							} else if (result.doctorId != that.$store.state.userInfo.id) {
								return uni.showToast({
									title: '请确保当前患者和是您的病人',
									icon: 'none',
									duration: 2000
								})
							}
							this.updateAvatar(result.medrecordId, result.uploadNum)
							console.warn('result ', result);
							return
						} catch (e) {
							return uni.showToast({
								title: '未识别',
								icon: 'none',
								duration: 2000
							})
						}

					},
					fail: (err) => {
						// #ifdef MP
						uni.getSetting({
							success: res => {
								let authStatus = res.authSetting['scope.camera'];
								if (!authStatus) {
									uni.showModal({
										title: '授权失败',
										content: '欣九康app需要使用您的相机，请在设置界面打开相关权限',
										success: res => {
											if (res.confirm) {
												uni.openSetting();
											}
										}
									});
								}
							}
						});
						// #endif
					}
				});
			},
		}
	};
</script>

<style lang="scss">
	.content {
		display: flex;
		flex-wrap: wrap;

	navigator,
	.item {
		display: block;
		width: 25%;
		// height: $jian-list-height-sm;
		// margin: auto;
		margin-top: $uni-spacing-col-lg;
		text-align: center;

		image {
			width: $uni-img-size-lg;
			height: $uni-img-size-lg;
			margin: auto;
			display: block;
			margin-bottom: $uni-spacing-col-sm;
		}

		#navtext {
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
			}
		}
	}
</style>