<template>
	<view class="mescroll-body">
		<view class="head">
			<view class="Integral_box">
				<view class="a_box">
					<view class="integral-top">
						<text>总积分:</text>
						<text>{{userIntegral}}</text>
					</view>

					<view class="giftList">
						<view class="giftListChild" @click="btnGiftList">兑换礼品</view>
					</view>
				</view>
			</view>

			<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback"
				:down="downOption" :up="upOption">
				<!-- 积分列表 :showArrow="false" -->
				<view class="uni-list-item" v-for="(item,index) in IntegralList" :key="index">
					<view class="uni-list-item-left">
						<image src="../../static/img/jifen.png" mode="widthFix" class="images"></image>
					</view>
					<view class="uni-list-item-right">
						<view class="top">
							<text class="type">{{item.desc}}</text>
							<text v-if="item.point<0" class="consumption">{{ item.point}}</text>
							<text v-if="item.point>0" class="increase">{{ item.point}}</text>
						</view>
						<text class="time">{{item.created_at}}</text>
					</view>
				</view>
			</mescroll-body>
		</view>
	</view>
</template>

<script>
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				userIntegral: '', //我的积分
				downOption: {
					use: true, // 是否启用下拉刷新; 默认true
				},
				upOption: {
					auto: true, // 是否在初始化完毕之后自动执行上拉加载的回调; 默认true
					empty: {
						use: true, // 是否显示空布局
						icon: "/static/img/msg-empty.png", // 图标路径 (建议放入static目录, 如 /static/img/mescroll-empty.png )
						tip: '~ 空空如也 ~' // 提示
					}
				},
				IntegralList: [],
				params: {
					page: 0,
				}, // 页码长度 
				// 是否显示
				downState: false,
				last_page: ''
			}
		},
		onShow() {
			this.getuserIntegral()
		},
		onLoad() {

		},
		methods: {
			// 点击跳转到礼品列表
			btnGiftList() {
				uni.switchTab({
					url: '../mall/index'
				})
			},
			// 我的积分
			getuserIntegral() {
				this.$api.get('api/patient/user_point/points', {
					header: {
						'content-type': 'application/json',
					},
				}).then(res => {
					console.log("---", res)
					this.userIntegral = res.data.points
				}).catch(err => {
					console.log(err)
				})
			},
			// 积分列表
			getIntegralList() {
				if (this.IntegralList.length == 0) {
					uni.showLoading({
						title: '加载中',
						mask: true
					});
				}
				// 判断如果用户是上拉刷新那么就 拉去第一页的 否则就是第二页
				let params = {
					page: 1
				}
				this.$api.get('api/patient/user_point/list', params = params).then(res => {
					console.log(res)
					this.IntegralList = res.data.data
					this.last_page = res.data.last_page
					this.mescroll.endBySize(res.data.per_page, res.data.total);
					uni.hideLoading()
				}).catch(err => {
					
				})
			},
			/*下拉刷新的回调 */
			downCallback() {
				this.params.page = 0
				this.IntegralList = []
				// this.getIntegralList()
				// 下拉刷新的回调,默认重置上拉加载列表为第一页 (自动执行 page.num=1, 再触发upCallback方法 )
				this.mescroll.resetUpScroll()
				// this.mescroll.endErr();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				console.log(22222222222)
				this.params.page++
				let params = {
					params: this.params
				}
				this.$api.get('api/patient/user_point/list', params = params).then(res => {
					// 调用此方法则 已经没有数据的时候就不会在显示加载中 而是 END
					this.mescroll.endBySize(res.data.per_page, res.data.total);
					this.IntegralList = this.IntegralList.concat(res.data.data) //将数据拼接在一起
					this.last_page = res.data.last_page

					// this.mescroll.endErr();
				}).catch(err => {
					this.mescroll.endErr();
				})
			},
			// 点击回到顶部按钮的回调
			topClick() {
				console.log('点击了回到顶部按钮');
			}
		},

	}
</script>

<style lang="scss">
	page {
		background-color: rgb(240, 240, 240);
	}

	.head {
		width: 90%;
		margin-left: 5%;
		padding-top: 200upx;

		.Integral_box {
			height: 200upx;
			top: 0;
			left: 0;
			width: 100%;
			background-color: rgb(240, 240, 240);
			position: fixed;
			z-index: 999;

			.a_box {
				width: 90%;
				height: 100upx;
				background-color: rgb(255, 255, 255);
				text-align: center;
				display: flex;
				justify-content: space-around;
				align-items: center;
				border-radius: 20rpx;
				margin: 50upx 5% 50upx 5%;

				.integral-top {
					
				}

				.giftList {
					

					.giftListChild {
						outline: none; //消除默认点击蓝色边框效果
						font-size: 26upx;
						text-align: center;
						width: 160upx;
						height: 60upx;
						padding: 0;
						line-height: 60upx;
						color: $jian-bg-color;
						background-color: #fff;
						border: solid 1rpx $jian-bg-color;
						border-radius: 30upx;
					}
				}
			}
		}

	}

	.mescroll-body {
		background-color: rgb(240, 240, 240);
		padding: 20upx 0upx;
		// background-color: #FF5053;

	}

	.uni-list-item {
		display: flex;
		width: 100%;
		height: 140upx;
		margin-bottom: 15upx;
		border-radius: 20upx;
		background-color: rgb(255, 255, 255);
		align-items: center;
		padding: 0 30rpx;
		box-sizing: border-box;

		.uni-list-item-left {
			width: 100rpx;

			.images {
				width: 100%;
			}
		}

		.uni-list-item-right {
			margin-left: 20rpx;
			width: calc(100% - 120rpx);

			.top {
				.type {
					color: rgb(137, 137, 137);
					margin-right: 15rpx;
					font-size: 30rpx;
					width: 200rpx;
				}

				.consumption {
					color: #FF5053;
					font-weight: bold;
				}

				.increase {
					color: $jian-bg-color;
					font-weight: bold;
				}
			}

			.time {
				display: block;
				font-size: 30upx;
				color: rgb(137, 137, 137);
				margin-left: auto;
			}
		}
	}

	.upwarp-nodata {
		text-align: center;
		font-size: 30upx;
		color: rgb(137, 137, 137);
	}
</style>
