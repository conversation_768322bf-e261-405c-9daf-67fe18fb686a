'use strict';
exports.main = async (event, context) => {
  try {
    // // 1. 获取前端传递的参数
    const { access_token, openid, appid, provider } = event.queryStringParameters;
    if (!access_token || !openid || !appid || !provider) {
      return { code: 400, message: '参数缺失' };
    }
    // 2. 调用 uniCloud 解密手机号
    const res = await uniCloud.getPhoneNumber({
      provider: provider, // 固定值，代表一键登录
      access_token: access_token,
	  appid: appid,
      openid: openid
    });

    // 3. 返回手机号
    return {
      code: 200,
      data: {
        phoneNumber: res.phoneNumber
      }
    };
  } catch (e) {
    return { code: 500, message: '服务器错误: ' + e.message };
  }
};