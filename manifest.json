{
    "name" : "欣九康健康版",
    "appid" : "__UNI__14BD443",
    "description" : "",
    "versionName" : "1.1.4",
    "versionCode" : 17,
    "transformPx" : false,
    "networkTimeout" : {
        "request" : 15000
    },
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "Payment" : {},
            "VideoPlayer" : {},
            "Push" : {},
            "OAuth" : {},
            "Share" : {},
            "Geolocation" : {},
            "Maps" : {},
            "Barcode" : {},
            "Camera" : {},
            "Record" : {},
            "Contacts" : {}
        },
        "privacy" : {
            "prompt" : "template",
            "template" : {
                "title" : "服务协议和隐私政策",
                "message" : "　　请你务必审慎阅读、充分理解“服务协议”和“隐私政策”各条款，包括但不限于：为了更好的向你提供服务，我们需要收集你的设备标识、操作日志等信息用于分析、优化应用性能。<br/>　　你可阅读<a href=\"https://hs.xxjk99.com/patient/protocol.html\">《服务协议》</a>和<a href=\"https://hs.xxjk99.com/patient/privacy.html\">《隐私政策》</a>了解详细信息。如果你同意，请点击下面按钮开始接受我们的服务。",
                "buttonAccept" : "同意",
                "buttonRefuse" : "暂不同意",
                "second" : {
                    "title" : "温馨提示",
                    "message" : "　　进入应用前，你需先同意<a href=\"https://hs.xxjk99.com/patient/protocol.html\">《服务协议》</a>和<a href=\"https://hs.xxjk99.com/patient/privacy.html\">《隐私政策》</a>，否则将退出应用。",
                    "buttonAccept" : "同意并继续",
                    "buttonRefuse" : "退出应用"
                }
            }
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECEIVE_BOOT_COMPLETED\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.SYSTEM_ALERT_WINDOW\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ],
                "schemes" : "xxjk",
                "permissionPhoneState " : {
                    "request" : "once",
                    "prompt" : "为保证您正常、安全地使用，需要获取设备识别码（部分手机提示为获取手机号码）使用权限，请允许。"
                },
                "permissionExternalStorage" : {
                    "request" : "none",
                    "prompt" : "应用保存运行状态等信息，需要获取读写手机存储（系统提示为访问设备上的照片、媒体内容和文件）权限，请允许。"
                },
                "minSdkVersion" : 21,
                "targetSdkVersion" : 30
            },
            /* ios打包配置 */
            "ios" : {
                "privacyDescription" : {
                    "NSPhotoLibraryUsageDescription" : "若不允许，你将无法从相册选择图片上传头像或在聊天时发送图片消息",
                    "NSPhotoLibraryAddUsageDescription" : "若不允许，你将无法保存图片到相册",
                    "NSCameraUsageDescription" : "若不允许，你将无法使用拍照功能用于聊天时发送图片消息或上传头像",
                    "NSMicrophoneUsageDescription" : "若不允许，你将无法在聊天时发送语音消息功能",
                    "NSRemindersUsageDescription" : "若不允许，你将无法访问提醒事项",
                    "NSLocationWhenInUseUsageDescription" : "若不允许，您将无法获取附近的诊所信息",
                    "NSLocalNetworkUsageDescription" : "若不允许，你将无法访问网络"
                },
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:hs.xxjk99.com" ]
                    }
                },
                "urltypes" : [
                    {
                        "urlidentifier" : "com.xxjk99.hs",
                        "urlschemes" : [ "xxjk" ]
                    }
                ],
                "dSYMs" : false,
                "deploymentTarget" : "11.0"
            },
            /* SDK配置 */
            "sdkConfigs" : {
                "payment" : {
                    "alipay" : {},
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wxfcf342913a5a3362",
                        "UniversalLinks" : "https://hs.xxjk99.com/weixinpat/"
                    }
                },
                "ad" : {},
                "push" : {
                    "unipush" : {
                        "version" : "2",
                        "offline" : true,
                        "hms" : {},
                        "oppo" : {},
                        "vivo" : {},
                        "mi" : {},
                        "icons" : {
                            "small" : {
                                "xxhdpi" : "unpackage/res/icons/72x72.png"
                            }
                        }
                    }
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "e75eaebb4fd9ee50a6abddb1f3c21674",
                        "appkey_android" : "5d11040b436008cae166d5e7a3edc109"
                    }
                },
                "speech" : {},
                "oauth" : {
                    "apple" : {},
                    "weixin" : {
                        "appid" : "wxfcf342913a5a3362",
                        "appsecret" : "59fc278d917f20536654311f77aa3426",
                        "UniversalLinks" : "https://hs.xxjk99.com/weixinpat/"
                    },
                    "univerify" : {}
                },
                "share" : {
                    "weixin" : {
                        "appid" : "wxfcf342913a5a3362",
                        "UniversalLinks" : "https://hs.xxjk99.com/weixinpat/"
                    }
                },
                "geolocation" : {
                    "amap" : {
                        "__platform__" : [ "ios", "android" ],
                        "appkey_ios" : "e75eaebb4fd9ee50a6abddb1f3c21674",
                        "appkey_android" : "5d11040b436008cae166d5e7a3edc109"
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            },
            "splashscreen" : {
                "android" : {
                    "hdpi" : "unpackage/res/android/qi.png",
                    "xhdpi" : "unpackage/res/android/qi.png",
                    "xxhdpi" : "unpackage/res/android/qi.png"
                },
                "ios" : {
                    "iphone" : {
                        "portrait-896h@3x" : "unpackage/res/IOS/1242引导页.png",
                        "portrait-896h@2x" : "unpackage/res/IOS/828引导页.png",
                        "iphonex" : "unpackage/res/IOS/1125引导页.png",
                        "retina55" : "unpackage/res/IOS/1242X2208引导页.png",
                        "retina47" : "unpackage/res/IOS/750X1334引导页.png",
                        "retina40" : "unpackage/res/IOS/640X1136引导页.png",
                        "retina35" : "unpackage/res/IOS/640X960引导页.png"
                    }
                },
                "androidStyle" : "default",
                "iosStyle" : "common",
                "useOriginalMsgbox" : true
            }
        },
        "uniStatistics" : {
            "enable" : true
        },
        "nativePlugins" : {
            "DCloud-PushSound" : {
                "__plugin_info__" : {
                    "name" : "自定义推送铃声和渠道",
                    "description" : "自定义推送铃声同时支持 Android、iOS 平台",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=7482",
                    "android_package_name" : "com.xxjk99.patient",
                    "ios_bundle_id" : "com.xxjk99.health",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "7482",
                    "parameters" : {}
                }
            },
            "Tuoyun-OpenIMSDK" : {
                "__plugin_info__" : {
                    "name" : "OpenIMSDK",
                    "description" : "IM",
                    "platforms" : "Android,iOS",
                    "url" : "",
                    "android_package_name" : "",
                    "ios_bundle_id" : "",
                    "isCloud" : false,
                    "bought" : -1,
                    "pid" : "",
                    "parameters" : {}
                }
            },
            "wz-YeIMCallKit" : {
                "__plugin_info__" : {
                    "name" : "YeIM-CallKit 私有化部署的实时音视频聊天原生插件",
                    "description" : "基于LiveKit WebRTC SDK的实时音视频聊天 uni-app 原生插件，支持Android和iOS，无需第三方服务支持，本地架设流媒体服务端，适合内网、公网、私有化、私密化部署。",
                    "platforms" : "Android,iOS",
                    "url" : "https://ext.dcloud.net.cn/plugin?id=12875",
                    "android_package_name" : "com.xxjk99.patient",
                    "ios_bundle_id" : "com.xxjk99.health",
                    "isCloud" : true,
                    "bought" : 1,
                    "pid" : "12875",
                    "parameters" : {}
                }
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "mp-qq" : {
        "uniStatistics" : {
            "enable" : false
        }
    },
    "vueVersion" : "2"
}
