<template>
	<view class="base">
		<view class="space-between item">
			<text>姓名：{{userInfo.patname}}</text>
		</view>
		<view class="space-between item">
			<text>性别：{{userInfo.gender?"男":"女"}}</text>
		</view>
		<view class="space-between item">
			<text>出生日期：{{userInfo.birth}}</text>
		</view>
		<view class="space-between item" v-if="userInfo.height">
			<text>身高：{{userInfo.height}}CM</text>
		</view>
		<view class="space-between item" v-if="userInfo.weight">
			<text>体重：{{userInfo.weight}}KG</text>
		</view>
		<view class="space-between item" v-if="userInfo.waist">
			<text>腰围：{{userInfo.waist}}CM</text>
		</view>
		<view class="space-between item">
			<text>现病史：</text>
		</view>
		<textarea v-model="userInfo.nowillness" :disabled="!isedit"  placeholder-class="placeholder" auto-height="true" :placeholder="!isedit?'':'填写现病史...'"></textarea>
		<view class="space-between item group">
			<text>个人史：</text>
		</view>
		<textarea v-model="userInfo.personalill" :disabled="!isedit" placeholder-class="placeholder" auto-height="true" :placeholder="!isedit?'无':'填写个人史...'"></textarea>
		<view class="space-between item">
			<text>疾病史：{{userInfo.pastill}}</text>
		</view>
		<view class="space-between item">
			<text>家族病史：{{userInfo.familyill}}</text>
		</view>
		<view class="space-between item">
			<text>药物过敏：{{userInfo.allergies}}</text>
		</view>
		<view class="space-between item">
			<text>食物/接触物过敏：{{userInfo.other_allergy}}</text>
		</view>
		<view class="space-between item">
			<text>个人习惯：{{userInfo.habit}}</text>
		</view>
		<view class="space-between item">
			<text>肝功能：{{userInfo.liver}}</text>
		</view>
		<view class="space-between item">
			<text>肾功能：{{userInfo.renal}}</text>
		</view>
		<view class="space-between item">
			<text>婚姻状态：{{userInfo.marital_status}}</text>
		</view>
		<view class="space-between item">
			<text>生育状态：{{userInfo.fertility_status}}</text>
		</view>
		<view class="space-between item">
			<text v-if="userInfo.smoke_history.is_smoke">吸烟史：{{userInfo.smoke_history.is_smoke}};烟龄{{userInfo.smoke_history.smoke_age}};每日{{userInfo.smoke_history.smoke_num}}</text>
			<text v-else>吸烟史：无</text>
		</view>
		<view class="space-between item">
			<text v-if="userInfo.drinking_history.is_drinking">饮酒史：{{userInfo.drinking_history.is_drinking}};酒龄{{userInfo.drinking_history.drinking_age}};每日{{userInfo.drinking_history.drinking_capacity}};种类：{{userInfo.drinking_history.drinking_type}}</text>
			<text v-else>饮酒史：无</text>
		</view>
		<button v-if="isedit" type="default" @tap="sethealth()">发送</button>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo:{
				patname:"",
				gender:"",
				birth:"",
				nowillness:"" ,//修改现病史
				pastill:"" ,//修改既往史
				personalill:"" ,//修改个人史
				allergies:"" ,//修改过敏史
				familyill:"" ,//修改家族史
			},
			isedit:true,
			toEdit:false
		}; 
	},
	created() {
		if(!this.isedit){
			var webView = this.$mp.page.$getAppWebview();
			webView.setTitleNViewButtonStyle(0,{  
				width: '0'  
			});
		}
	},
	onNavigationBarButtonTap(e) {
		this.toEdit = true
		uni.navigateTo({
			url:'/pages/medrecord/health/record_list'
		})
	},
	onLoad(option) {
		if(option.userInfo=='create'){
			this.isedit =true
			this.userInfo.patname=this.$store.state.userInfo.patname
			this.userInfo.gender=this.$store.state.userInfo.gender
			this.userInfo.birth=this.$store.state.userInfo.birth
			this.getArchives()
		}else{
			this.userInfo = JSON.parse(option.userInfo)
			this.isedit =false
		}
	},
	onShow() {
		if(this.toEdit&&this.isedit){
			this.getArchives()
		}
		this.toEdit = false
	},
	methods: {
		bindTextAreaBlur: function(e) {
			// console.log(e.detail.value)
		},
		sethealth(){
			uni.setStorage({
				key: 'healthData',
				data:JSON.stringify(this.userInfo),
				success: function () {
					uni.navigateBack();
				}
			});
		},
		getArchives(){
			this.$api.get('api/patient/records/getinfo').then(res=>{
				console.log(res)
				if(res.success){
					this.userInfo.pastill =  res.data.sickness_history
					this.userInfo.allergies = res.data.drug_allergy
					this.userInfo.familyill = res.data.family_sickness_history
					this.userInfo = Object.assign({},this.userInfo,res.data)
				}
			})
		}
	}
};
</script>

<style lang="scss">
	page{
		.base{
			margin: 10px $uni-spacing-row-lg;
			padding-bottom: 20rpx;
		}
		textarea{
			margin-top:$uni-spacing-col-base ;
			padding:$uni-spacing-col-sm $uni-spacing-row-sm;
			width: calc(100% - 20px);
			border: 1px solid $uni-border-color;
			min-height: 50px;
			border-radius: 10rpx;
		}
		.placeholder{
			color: $uni-text-color-placeholder;
			font-size: $uni-font-size-base;
		}
		button{
			margin: $uni-spacing-col-lg 0;
			background-color: #02cfc9 !important;
			color: $uni-text-color-inverse !important;
		}
		.group{
			margin-top: 10px;
			line-height: 50upx;
		}
		.item{
			font-size: 30rpx;
			margin-top: 10rpx;
			line-height: 50rpx;
		}
	}
</style>
