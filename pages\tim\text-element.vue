<template>
 <rich-text :nodes="nodesFliter(msgtext)"></rich-text>
</template>
<script>
import {emojiName, emojiMap, emojiUrl}  from '../../common/commen.js'
export default {
  name: 'TextElement',
  props: {
    payload: {
      type: Object,
      required: true
    }
  },
  computed: {
    msgtext() {
      return this.replaceEmoji(this.payload.textElem.content) 
    }
  },
	methods:{
		//替换表情符号为图片
		replaceEmoji(str) {
			let replacedStr = str.replace(/\[([^(\]|\[)]*)\]/g, (item, index) => {
				// console.log('item: ' + emojiMap[item]);
				emojiMap[item]
				if(emojiMap[item]){
					let imgstr = '<img width="20px" height="20px" src="' + emojiUrl + emojiMap[item] + '">';
					// console.log('imgstr: ' + imgstr);
					return imgstr;
				}
			});
			return replacedStr;
		},
		nodesFliter(str) {
			let nodeStr = '<div style="align-items: center;word-wrap:break-all;white-space: pre-line;">' + str + '</div>';
			return nodeStr;
		},
	}
}
</script>