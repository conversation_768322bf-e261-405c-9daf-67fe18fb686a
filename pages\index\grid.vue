<template>
	<view class="content">
		<!-- <navigator :url="n.url" v-for="n in grid" hover-class="none">
			<image :src="n.imgsrc" mode=""></image>
			<text id="navtext">{{ n.navtext }}</text>
		</navigator> -->
		<navigator url="/pages/user/stores/store_list" hover-class="none">
			<image src="/static/img/grid2.png" mode="widthFix"></image>
			<text id="navtext">会员门店</text>
		</navigator>
		<navigator url="/pages/doctors/doctorsList" hover-class="none">
			<image src="/static/img/grid1.png" mode="widthFix"></image>
			<text id="navtext">找医生</text>
		</navigator>
		<navigator url="/pages/addressBook/index" hover-class="none">
			<image src="/static/img/grid4.png" mode="widthFix"></image>
			<text id="navtext">我的医生</text>
		</navigator>
		<navigator url="/pages/medrecord/medrecordList" hover-class="none">
			<image src="/static/img/grid8.png" mode="widthFix"></image>
			<text id="navtext">我的病历</text>
		</navigator>
		<view class="item" @tap="scan()">
			<image src="/static/img/grid5.png" mode="widthFix"></image>
			<text id="navtext">扫码</text>
		</view>
		<navigator url="/pages/hospitals/hospitalsList" hover-class="none">
			<image src="/static/img/grid3.png" mode="widthFix"></image>
			<text id="navtext">附近医院</text>
		</navigator>
		<navigator url="/pages/hospitals/myHospitals" hover-class="none">
			<image src="/static/img/grid2.png" mode="widthFix"></image>
			<text id="navtext">收藏医院</text>
		</navigator>

		<!-- <navigator url="/pages/doctors/99eyao" hover-class="none">
			<image src="/static/img/grid2.png" mode="widthFix"></image>
			<text id="navtext">99eyao</text>
		</navigator> -->
	</view>
</template>

<script>
	import {
		pathToBase64,
		base64ToPath
	} from 'image-tools';
	export default {
		data() {
			return {
				grid: [{
						url: '/pages/doctors/doctorsList',
						imgsrc: '/static/img/grid1.png',
						navtext: '找医生'
					},
					{
						url: '/pages/hospitals/hospitalsList',
						imgsrc: '/static/img/grid1.png',
						navtext: '找医院'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid2.png',
						navtext: '预约挂号'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid3.png',
						navtext: '找诊所'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid4.png',
						navtext: '我的医生'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid5.png',
						navtext: '添加医生'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid6.png',
						navtext: '我的订单'
					},
					{
						url: '/pages/medrecord/medrecordList',
						imgsrc: '/static/img/grid7.png',
						navtext: '疾病自查'
					},
					{
						url: '/pages/addressBook/index',
						imgsrc: '/static/img/grid8.png',
						navtext: '我的医生'
					}
				]
			};
		},
		methods: {
			updateAvatar(id, count = 1) {
				let that = this;
				getApp().requestAndroidPermission('android.permission.WRITE_EXTERNAL_STORAGE', '存储权限')
				uni.chooseImage({
					count: 1, // 数量调整：一次只上传一张
					sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
					// sourceType: ['camera '], //从相册选择
					success: function(res) {
						if (!/^.*\.(jpg|JPG|png|jpeg|webp|gif)$/.test(res.tempFilePaths[0])) {
							uni.showModal({
								title: '格式错误',
								content: '只允许上传jpg、png、jpeg、webp、gif格式的图片',
								showCancel: false
							})
							return
						}
						that.compressImages(id, res.tempFilePaths)
					}
				});
			},
			compressImages(id, tempFilePaths) {
				const that = this
				const promises = tempFilePaths.map(filePath => {
					return new Promise((resolve, reject) => {
						uni.compressImage({
							src: filePath,
							quality: 20,
							success: res => {
								pathToBase64(res.tempFilePath)
									.then(base64 => {
										resolve(base64.replace(/[\r\n]/g, ''));
									})
									.catch(error => {
										reject(error);
									});
							},
							fail: err => {
								reject(err);
							}
						});
					});
				});

				Promise.all(promises)
					.then(base64Images => {
						// 所有图片都已成功处理，base64Images 是一个包含所有 Base64 编码的数组
						that.uploadMedImgs(id, base64Images); // 假设 modifyavatars 接受一个数组
					})
					.catch(error => {
						console.error('Error compressing or converting images:', error);
					});
			},
			uploadMedImgs(id, images) {
				let that = this
				this.$api
					.post('api/patient/medrecord/uploadimage', {
						id,
						images
					})
					.then(res => {
						console.log('api/patient/medrecord/uploadimage', res);
						if (res.success) uni.showToast({
							title: '上传成功',
							duration: 2000
						})
						else {
							uni.showToast({
								title: res.msg || '上传失败',
								icon: 'none',
								duration: 2000
							})
						}

					})
					.catch(err => {
						console.error(err);
					});
			},
			async scan() {
				let that = this;
				uni.scanCode({
					success: (res) => {
						console.warn('that.$store.state ', that.$store.state);
						console.log(res)
						let result = ''
						try {
							// 扫码上传图片
							result = JSON.parse(res.result)
							if (result?.scanType != 'uploadImage') {
								return uni.showToast({
									title: '未识别',
									icon: 'none',
									duration: 2000
								})
							} else if (result.userId != that.$store.state.userInfo.userid) {
								return uni.showToast({
									title: '请确保正在就诊患者和您是同一人',
									icon: 'none',
									duration: 2000
								})
							}
							this.updateAvatar(result.medrecordId, result.uploadNum)
							return
						} catch (e) {
							// 扫码添加医生
							result = res.result
							if (!result.includes('patient/share.html')) return uni.showToast({
								title: '未识别',
								icon: 'none',
								duration: 2000
							})
						}
						console.warn('result ', result);
						if (typeof result != 'string') return
						let scaninfo = that.getUrlParams(res.result)
						console.log(scaninfo)
						let confrimFrom = (res.result.split('?')[0]).replace(/[\u{0080}-\u{FFFF}]/gu, "")
						if (confrimFrom == (that.$baseUrl + "patient/share.html")) {
							if (scaninfo.drId) {
								uni.showModal({
									title: '添加好友',
									content: '是否确认添加此医生为好友？',
									success(res) {
										if (res.confirm) {
											that.$api
												.post('api/patient/doctor/AddDoctor', {
													doctorId: scaninfo.drId
												})
												.then(res => {
													if (res.success) {
														uni.showToast({
															title: '添加成功',
															success() {
																uni.navigateTo({
																	url: '/pages/addressBook/index?tab=1'
																});
															}
														});
													} else {
														uni.showToast({
															title: res.msg,
															icon: 'none'
														});
													}
												}).catch(err => {
													console.log(err);
												});
										}
									}
								});
							} else if (scaninfo.hospitalId) {
								if (scaninfo.type == 'join') {
									uni.showModal({
										title: '会员邀请',
										content: '是否成为' + decodeURIComponent(scaninfo.cliname) +
											"的会员",
										success(res) {
											if (res.confirm) {
												that.$api
													.post('api/patient/vip/addvip', {
														id: scaninfo.hospitalId, //诊所id
														status: 2, //2 同意 3 拒绝
													})
													.then(res => {
														if (res.success) {
															uni.showToast({
																icon: 'none',
																title: `您已成为${decodeURIComponent(scaninfo.cliname)}的的会员`
															});
														} else {
															uni.showToast({
																icon: 'none',
																title: res.msg
															});
														}
													});
											}
										}
									})
								} else {
									uni.navigateTo({
										url: "/pages/hospitals/hospitalDetail/hospitalDetail?id=" +
											scaninfo.hospitalId + "&type=invite"
									})
								}
							} else {
								uni.showToast({
									title: '扫描地址错误',
									icon: 'error'
								});
							}
						}
					},
					fail: (err) => {
						// #ifdef MP
						uni.getSetting({
							success: res => {
								let authStatus = res.authSetting['scope.camera'];
								if (!authStatus) {
									uni.showModal({
										title: '授权失败',
										content: '欣九康app需要使用您的相机，请在设置界面打开相关权限',
										success: res => {
											if (res.confirm) {
												uni.openSetting();
											}
										}
									});
								}
							}
						});
						// #endif
					}
				});
			},
			getUrlParams(url) {
				// 通过 ? 分割获取后面的参数字符串
				let urlStr = url.split('?')[1];
				// 创建空对象存储参数
				let obj = {};
				// 再通过 & 将每一个参数单独分割出来
				let paramsArr = urlStr.split('&');
				for (let i = 0, len = paramsArr.length; i < len; i++) {
					// 再通过 = 将每一个参数分割为 key:value 的形式
					let arr = paramsArr[i].split('=');
					obj[arr[0]] = arr[1];
				}
				return obj;
			}
		}
	};
</script>

<style lang="scss">
	.content {
		display: flex;
		flex-wrap: wrap;
	}

	navigator,
	.item {
		display: block;
		width: 25%;
		// height: $jian-list-height-sm;
		// margin: auto;
		margin-top: $uni-spacing-col-lg;
		text-align: center;

		image {
			width: $uni-img-size-lg;
			height: $uni-img-size-lg;
			margin: auto;
			display: block;
			margin-bottom: $uni-spacing-col-sm;
		}

		#navtext {
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
		}
	}
</style>