<template>
	<view class="a_box">
		<!-- 确认赠送医生页面 -->
		<view class="navlist">
				<text>请选择您想赠送的礼品</text>
		</view>
		
		<view class="activeGift" >
			<view class="activeGiftItem" v-for="item in presentlist" v-if="item.leave_num>0">
				<image :src="item.img" mode=""></image>
				<view class="name">
					{{item.spec.title}}
				</view>
				<view class="msg">
					持有数：{{item.leave_num}}
				</view>
				<view class="sent" @click="activeGiftItem(item)">赠送</view>
			</view>
		</view>
		<!-- <hFormAlert
			v-if="cancelShow"
			:value="editctx.value"
			:name="editctx.name"
			:placeholder="editctx.placeholder"
			:title="editctx.title"
			type="'idcard'"
			@confirm="confirm"
			@cancel="cancel"
		></hFormAlert> -->
		<!-- <wButton text="完 成"  @click.native="confirmPurchase" class='payButton' ></wButton> -->
	</view>
</template>

<script>
	import hFormAlert from '@/components/h-form-alert/h-form-alert.vue';
	// import wButton from '@/components/watch-login/watch-button.vue'; //button
	export default {
		components: {
			hFormAlert, 
			// wButton,
			},
			onLoad(e) {
				this.doctorId = e.doctorId
				this.getPresentlist()
			},
		data() {
			return {
				// 用于接收用户礼品的数组
				presentlist:[],
				// 传递给组件的数据
				editctx: {
					name: 'giftNumber',
					value: '1',
					placeholder: '输入礼品个数',
					title: '请输入礼品数量',
					type: 'tel',
				},
			    doctorId:'',// 医生id 用于点击赠送后 需要传递的参数
				cancelShow:false,//控制组件的显示和隐藏
				giftList:{},//  用于接收 礼品id 和礼品的数量
				select_item:{}
			};
		},
		methods:{
			// 请求礼品列表
			getPresentlist(){
				this.$api.get('api/patient/present/getsendpresentlist').then(res => {
					if(res.success){
						this.presentlist = res.data
					}
				}).catch(err => {
					console.log(err)
				})
			},
			// 点击了 我的礼品
			activeGiftItem(item){
				// this.select_item = item
				// this.cancelShow = true
				let that = this
				uni.showModal({
					title:'赠送礼品',
					content:'您将送给医生'+item.spec.title+"×1",
					success(res) {
						console.log(res)
						if(res.confirm){
							that.$api.post('api/patient/present/senddoctor',{
								prod_id:item.prod_id,
								present_num:1,
								doctor_id:that.doctorId
							}).then(res=>{
								console.log(res)
								if(res.success){
									uni.showToast({
										title:"赠送成功",
										icon:'success',
										success() {
											setTimeout(function() {
												that.getPresentlist()
											}, 1000);
										}
									})
									
								}else{
									console.log(res)
									uni.showToast({
										title:res.msg,
										icon:'none'
									})
								}
							})
						}
					}
				})
				
			}
		}
	}
</script>

<style lang="scss">
page {
		background-color: rgb(240, 240, 240);
	}
.a_box {
	padding: 20upx 40upx;
}
.navlist{
	width: 100%;
	height: 100upx;
	background-color: rgb(255, 255, 255);
	display: flex;
	margin: 50upx 0upx;
	justify-content: center;
	line-height: 100upx;
	border-radius: 16upx;
	font-weight: bold;
}
.activeGift {
	width: 100%;

	// background-color: red;
	display: flex;
	flex-wrap:wrap;
}
.activeGiftItem {
	background-color: rgb(255,255,255);
	box-shadow: 0upx 0upx 2upx 0upx rgba(0,0,0,0.5);
	width: 100%;
	display: flex;
	justify-content: space-between;
	padding: 20rpx;
	margin-top: 20rpx;
	box-sizing: border-box;
	align-items: center;
	border-radius: 20rpx;
	box-shadow: 0px 0px 5px #ddd;
	image{
		width: 100rpx;
		height: 100rpx;
	}
	.name{
		color: $jian-bg-color;
		font-size: 36rpx;
		font-weight: bold;
	}
	.msg{
		color: #6D6D72;
		font-size: 32rpx;
	}
	.sent{
		width: 120upx;
		height: 60upx;
		font-size: 30upx;
		text-align: center;
		line-height: 60upx;
		color: $jian-bg-color;
		background-color: #fff;
		border: solid 1rpx $jian-bg-color;
		border-radius: 30upx;
	}
	.uni-list-item-right {
		color: #ff0000;
		font-size: 30upx;
	}
}
</style>
