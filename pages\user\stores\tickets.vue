<template>
	<view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" top="0" :down="downOption" @down="downCallback"
			@up="upCallback">
		<view class="ticket" v-for="item in couponList">
			<view class="item_left">
				<view class="title">
					{{item.title}}
				</view>
				<view class="times">
					<view>{{item.endTime}}到期</view>
				</view>
				<view class="use">
					满{{item.limit}}元可用
				</view>
			</view>
			<view class="item_right">
				<view class="discounts">
					{{item.type==1?item.discount+'元':(item.discount/10)+'折'}}
				</view>
				<view class="type">
					{{item.type==1?'满减券':'折扣券'}}
				</view>
				<view class="btn" @click="toUsercoupon(item)">
					去使用
				</view>
			</view>
		</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				clindId:'',
				couponList: [],
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
			};
		},
		onLoad(option) {
			this.clindId = option.id
		},
		created() {},
		onNavigationBarButtonTap(e) {
			if (e.index == 0) {
				uni.navigateTo({
					url: '/pages/user/stores/unuse_tickets?id=' + this.clindId
				});
			}
		},
		methods:{
			downCallback() {
				this.couponList = [];
				this.mescroll.resetUpScroll();
			},
			upCallback(page) {
				let that = this
				that.$api.get('api/patient/coupons/getlist', {
					params:{
						id: that.clindId,
						pager: page.num
					}
				}).then(res=>{
					if(res.success){
						that.couponList = that.couponList.concat(res.data)
						that.mescroll.endSuccess(res.data.length);
					}else{
						uni.showToast({
							title:res.msg,
							icon:'none'
						})
						that.mescroll.endErr();
					}
				}).catch(err => {
					that.mescroll.endErr();
				})
			},
			toUsercoupon(data){
				uni.navigateTo({
					url: `/pages/user/stores/coupon_detail?data=`+JSON.stringify(data)
				})
			}
		}
	}
</script>

<style lang="scss">
.ticket{
	width: 90%;
	margin: 10rpx auto;
	border: solid 1rpx #000;
	border-radius: 24rpx;
	display: flex;
	justify-content: space-between;
	padding: 24rpx;
	box-sizing: border-box;
	font-size: 28rpx;

	.item_left{
		.title{
			font-size: 36rpx;
			font-weight: bold;
		}

		.times{
			display: flex;
			margin-top: 10rpx;
		}

		.use{
			margin-top: 10rpx;
		}
	}

	.item_right{
		text-align: center;

		.discounts{
			font-size: 40rpx;
		}

		.type{
			margin-top: 10rpx;
		}

		.btn{
			width: 160rpx;
			line-height: 50rpx;
			background-color: $jian-bg-color;
			color: #fff;
			border-radius: 25rpx;
			margin-top: 20rpx;
		}
	}
}
</style>