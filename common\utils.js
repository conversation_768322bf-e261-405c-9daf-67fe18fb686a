/**
 * 根据时间计算出，3天内、7天内、30天内、3个月内、半年内、超过半年、超过一年
 * @param {String} dateTime 时间
 */
function timeDiff(dateTime) {
 let nowTime = new Date().getTime();
 dateTime = new Date(dateTime).getTime();
  let diffTime = nowTime - dateTime;
  let days = Math.floor(diffTime / (24 * 3600 * 1000));
  if (days <= 3) {
    return "3天内";
  } else if (days <= 7) {
    return "7天内";
  } else if (days <= 30) {
    return "30天内";
  } else if (days <= 90) {
    return "3个月内";
  } else if (days <= 180) {
    return "半年内";
  } else if (days <= 365) {
    return "超过半年";
  } else {
    return "超过一年";
  }
}

console.log(new Date('2025-05-28 19:27:10').getTime());
