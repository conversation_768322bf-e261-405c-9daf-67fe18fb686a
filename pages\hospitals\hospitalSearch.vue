<template>
	<view>
		<!--S 刷选 -->
		<view class="space-around nav">
			<view class="flex-start-center" @click="getDoctorByRate">
				<text class="label" :id="isclick == 1 ? 'active' : ''">评分</text>
				<div>
					<text :class="rateSort == 1 && isclick == 1 ? 'top topactive' : 'top'"></text>
					<text :class="rateSort == 0 && isclick == 1 ? 'down downactive' : 'down'"></text>
				</div>
			</view>
		</view>
		<!--E 刷选 -->
		<mescroll-body id="base" top="80" ref="mescrollRef" @init="mescrollInit" :down="downOption" :up="upOption" @down="downCallback" @up="upCallback">
			<navigator class="flex-start item" v-for="n in arr" :url="'./hospitalDetail/hospitalDetail?id='+n.id">
				<image class="avatar" v-if="n.face" :src="$hisBaseUrl + n.face" onerror="this.src='/static/img/default.png'" mode=""></image>
				<image class="avatar" v-else src="../../static/img/defaultman.png" mode=""></image>
				<view class="rightctx">
					<view class="flex-start-center">
						<text class="name">{{ n.cliname }}</text>
					</view>
					<view class="flex-start-center">
						<text>{{ n.cliaddress }}</text>
					</view>
					<view class="flex-start-center">
						<text>诊所评分：{{ n.score }}</text>
					</view>
				</view>
			</navigator>
		</mescroll-body>
	</view>
</template>

<script>
import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
import uniIcons from '@/components/uni-icons/uni-icons.vue';
export default {
	mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
	components: {
		uniIcons
	},
	onNavigationBarSearchInputChanged(e) {
		this.args.name = e.text;
	},
	onNavigationBarSearchInputConfirmed() {
		//在这里执行搜索操作
		if (this.args.name.length > 0) {
			this.arr = [];
			this.mescroll.resetUpScroll();
		} else {
			uni.showToast({
				icon: 'none',
				title: '请输入关键字后再搜索'
			});
		}
	},
	onNavigationBarButtonTap(e) {
		if (this.args.name.length > 0) {
			if (e.index == 0) {
				this.arr = [];
				this.mescroll.resetUpScroll();
			}
		} else {
			uni.showToast({
				icon: 'none',
				title: '请输入关键字后再搜索'
			});
		}
	},
	data() {
		return {
			upOption: {
				auto: true,
				page: {
					num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
					size: 7 // 每页数据的数量,默认10
				}
			},
			downOption: {
				auto: false //是否在初始化后,自动执行downCallback; 默认true
			},
			args: {
				pager: 1, //当前页
				name: '', //关键字
				search_type: 1, // 1 评分
				search_method: 1 //0 降序 1 升序
			},
			isclick: 1,
			rateSort: 1, //评分排序
			arr: [],
			isPullDown: false,
			isPullUp: false,
			pulltext: '',
		};
	},
	onLoad:function(option){
		this.args.name = option.keywords
	},
	created() {
	},
	watch: {
		rateSort: function(val) {
			this.args.search_method = val;
			this.arr = [];
			this.args.pager = 1;
			this.mescroll.resetUpScroll();
		}
	},
	methods: {
		/*下拉刷新的回调 */
		downCallback() {
			this.arr = [];
			// 下拉刷新的回调,默认重置上拉加载列表为第一页 (自动执行 page.num=1, 再触发upCallback方法 )
			this.mescroll.resetUpScroll();
		},
		/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
		upCallback(page) {
			this.args.pager = page.num;
			this.getdoctorlist();
		},
		getdoctorlist(args) {
			//获取附近诊所列表
			var tmp = {
				pager: this.args.pager,
				name : this.args.name,
				search_type : this.args.search_type,
				search_method : this.args.search_method
			};
			if (tmp.keywords == '') {
				delete tmp.keywords
			}
			let that = this;
			this.$api.get('api/patient/hospital/getlist',{ params: tmp })
			.then(res => {
				that.mescroll.endBySize(res.data.length, 20);
				that.arr = res.data;
				console.log(that.arr)
			}).catch(res => {
				that.mescroll.endErr();
			});
		},
		getDoctorByRate() {
			//根据评分排序
			this.isclick = 1;
			if (this.args.search_type == 1) {
				if (this.rateSort == 1) {
					this.rateSort = 0;
				} else {
					this.rateSort = 1;
				}
			} else {
				this.args.search_type = 1;
				this.args.pager = 1;
				this.args.search_method = this.rateSort;
				this.arr = [];
				var tmp = {
					pager: this.args.pager,
					search_type: this.args.search_type,
					search_method: this.args.search_method
				};
				this.mescroll.resetUpScroll();
			}
		},
		closeKeyboard() {
			if (event.keyCode == 13) {
				event.currentTarget.blur();
			}
		},
	}
};
</script>

<style lang="scss">
page {
	background-color: $uni-bg-color-grey;
}
	.btnGiving {
		position: absolute;
		top: 10upx;
		right: 10upx;
		width: 120upx;
		height: 60upx;
		background-color: #15CB9F;
		font-size: 30upx;
		text-align: center;
		line-height: 60upx;
		color: rgb(255, 255, 255);
	}
#active {
	color: #5fe7c2;
}
.flex-end{
	margin-left: auto;
}
.nav {
	position: fixed;
	z-index: 9999;
	width: 100%;
	background-color: $uni-bg-color;
	flex-wrap: nowrap;
	padding: $uni-spacing-row-sm 0px;
	& > view {
		.label {
			font-size: $uni-font-size-lg;
			color: $uni-text-color;
			font-weight: 500;
			margin-right: $uni-spacing-row-sm;
		}
		.down {
			display: block;
			width: 0;
			height: 0;
			border-right: 6px solid transparent;
			border-left: 6px solid transparent;
			border-top: 6px solid #9a9a9a;
			border-radius: 3px;
		}
		.top {
			display: block;
			width: 0;
			height: 0;
			border-right: 6px solid transparent;
			border-left: 6px solid transparent;
			border-bottom: 6px solid #9a9a9a;
			border-radius: 3px;
			margin-bottom: 1px;
		}
		.topactive {
			border-bottom: 6px solid #60e7c2;
		}
		.downactive {
			border-top: 6px solid #60e7c2;
		}
	}
}
.item {
	padding: $uni-spacing-col-lg $uni-spacing-row-lg;
	margin: 0px $uni-spacing-row-lg;
	margin-top: $uni-spacing-col-lg;
	background-color: $uni-bg-color;
	color: $uni-text-color-grey;
	font-size: $uni-font-size-base;
	.name {
		color: $uni-text-color;
		font-size: $uni-font-size-lg;
		font-weight: bold;
		margin-right: $uni-spacing-row-sm;
	}
	.avatar {
		width: 50px;
		height: 50px;
		margin-right: $uni-spacing-row-base;
		border-radius: $uni-border-radius-circle;
	}
	.rightctx {
		width: calc(100% - 60px);
	}
	.star {
		width: 12px;
		height: 12px;
		margin-right: $uni-spacing-col-sm;
	}
	.brief {
		color: $uni-text-color;
		font-size: $uni-font-size-base;
	}
	.score {
		color: #fcac00;
		font-weight: bold;
		font-size: $uni-font-size-base;
		margin-right: $uni-spacing-row-lg;
	}
	.tab {
		margin-top: $uni-spacing-col-sm;
		margin-bottom: $uni-spacing-col-sm;
		margin-right:$uni-spacing-row-sm ;
		background: rgba(247, 247, 247, 1);
		font-size: $uni-font-size-base;
		color: #9a9a9a;
		padding: $uni-spacing-col-sm $uni-spacing-row-sm;
		font-size: $uni-font-size-sm;
		border-radius:$uni-border-radius-lg;
	}
	.departmentname {
		margin-right: $uni-spacing-col-sm;
	}
	.inquiry,
	.reserve {
		width: 12px;
		height: 12px;
		margin-right: $uni-spacing-col-sm;
	}
	.money {
		font-size: $uni-font-size-base;
		color: $uni-text-color;
		margin-right: $uni-spacing-row-lg;
	}
}
</style>
