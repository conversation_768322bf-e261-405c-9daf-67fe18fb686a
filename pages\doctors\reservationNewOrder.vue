<template>
	<view>
		<view class="details ">
			<view class="status_bar"></view>
			<!-- 分享按钮 -->
			<view class="flex-start">
				<image class="left" v-if="details.face" :src="$baseUrl + details.face" mode=""></image>
				<image class="left" v-if="!details.face && details.gender == 1" src="../../static/img/defaultman.png"
					mode=""></image>
				<image class="left" v-if="!details.face && details.gender == 0" src="../../static/img/defaultgirl.png"
					mode=""></image>
				<view class="rigth">
					<view class="flex-start">
						<text class="name">{{ details.realname }}</text>
					</view>
					<view class="flex-start">
						<text>{{ details.departmentname }}</text>
						<text>{{ details.professional }}</text>
					</view>
					<view class="space-between">
						<text>{{ details.cliname }}</text>
					</view>
					<view class="flex-start">
						<text>服务人数:{{ details.number }}</text>
						<text>评分:{{ details.score }}分</text>
					</view>
					<view>
						<text class="label" v-for="n in details.postcard">{{ n }}</text>
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="tu">
			<text>预约挂号</text>
			<text class="name">医生：{{details.realname}}</text>
		</view> -->
		<view class="space-padding">
			<view class="desc">
				<view class="item"><text>&bull;可预约近一周内的挂号。</text></view>
				<view><text>&bull;挂号服务由医生所在医院提供。</text></view>
				<view><text>&bull;请在预约时间到医生所在的医院，先到挂号处报到，即可得到优先就诊。</text></view>
				<view><text>&bull;医生每天上午、下午具体的上班时间请看医院的介绍，请在医生上班时间内到院就诊。</text></view>
				<view><text>&bull;挂号订单付款后不支持退款</text></view>
			</view>
			<view class="pay">
				<text>共支付{{ details.registerfee }}元</text>
			</view>
			<view class="top-warp">
				<!-- <app-tabs v-model="tabIndex" :tabs="tabs" @change="tabChange"></app-tabs> -->
				<selectdate v-model="tabIndex" v-if="tabs.length > 0" :tabs="tabs" @change="tabChange" ref="child">
				</selectdate>
			</view>
			<view class="desc">
				<view v-if="!availabelres[tabIndex].am == 0">
					{{ details.reservationNumArr[tabIndex].date }}
					周{{ dateToWeek(details.reservationNumArr[tabIndex].date) }} 上午：8:00-11:00
					<view class="appoint">
						<view>{{ details.departmentname }} {{ details.professional }}</view>
						<button @click="reservationNewOrder(1)">剩余{{ availabelres[tabIndex].am }}个号</button>
					</view>
				</view>
				<view v-if="!availabelres[tabIndex].pm == 0">
					{{ details.reservationNumArr[tabIndex].date }}
					周{{ dateToWeek(details.reservationNumArr[tabIndex].date) }} 下午：14:00-17:00
					<view class="appoint">
						<view>{{ details.departmentname }} {{ details.professional }}</view>
						<button @click="reservationNewOrder(2)">剩余{{ availabelres[tabIndex].pm }}个号</button>
					</view>
				</view>
				<view v-if="availabelres[tabIndex].am == 0 && availabelres[tabIndex].pm == 0">
					此医生暂无今日的预约信息，请预约其他时间或预约其他医生！</view>
			</view>
		</view>
		<paytypeselector v-if="payTypeShow" @payTypeConfirm="payTypeConfirm" @payTypeCancel="payTypeCancel">
		</paytypeselector>
	</view>
</template>

<script>
	// import AppTabs from "@/components/other/app-tabs.vue";
	import Paytypeselector from '@/pages/order/paytypeselector.vue';
	import Selectdate from '@/components/other/select-date.vue';

	export default {
		components: {
			// AppTabs,
			Paytypeselector,
			Selectdate
		},
		data() {
			return {
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
				details: '', //医生信息
				tabs: [], //日期tab页
				availabelres: [], //一周各天上下午挂号可用额度
				tabIndex: 0, // tab下标
				payTypeShow: false, //是否显示付款方式选择窗口
				pay_type: -1, //支付方式类型
				am_or_pm: 0 //预约上午或下午
			};
		},
		onLoad(option) {
			this.details = JSON.parse(decodeURIComponent(option.details));
			this.$api
				.get('api/patient/doctor/getdoctorreservation', {
					params: {
						id: this.details.id
					}
				})
				.then(res => {
					this.details.reservationNumArr = res.data;
					this.details.reservationNumArr.forEach((item, index) => {
						let week = this.dateToWeek(item.date);
						let hascode = item.am + item.pm;
						this.tabs.push(item.date + '^' + week + '^' + hascode);
						this.availabelres.push({
							am: item.am,
							pm: item.pm
						});
					});
					console.log(this.tabs);
				});
		},
		methods: {
			reservationNewOrder(val) {
				let hasname = this.$store.state.userInfo.patname;
				let hasage = this.$store.state.userInfo.age;
				console.log(hasage, 'hasage');
				console.log(hasname, 'hasname');
				if (!hasname || !hasage) {
					uni.showModal({
						title: '温馨提示',
						content: '请先前往填写您的真实姓名和出生日期信息后方可预约挂号！',
						showCancel: false,
						cancelText: '',
						confirmText: '确定',
						success: res => {
							if (res.confirm) {
								uni.navigateTo({
									url: '../user/userDetails'
								});
							}
						},
						fail: () => {},
						complete: () => {}
					});
				} else if (parseInt(hasage) < 10) {
					uni.showModal({
						title: '温馨提示',
						content: '年龄未满十岁无法进行预约挂号，如果您未正确设置年龄信息，请先修改出生日期信息后再进行预约挂号！',
						showCancel: false,
						cancelText: '',
						confirmText: '确定',
						success: res => {
							if (res.confirm) {
								uni.navigateBack();
							}
						},
						fail: () => {},
						complete: () => {}
					});
				} else {
					this.payTypeShow = true;
					this.am_or_pm = val;
				}
			},
			payTypeCancel() {
				this.payTypeShow = false;
			},
			payTypeConfirm(val) {
				var that = this;
				that.pay_type = val
				that.payTypeShow = false;
				that.$api
					.post('api/patient/register/reservation', {
						doctor_id: that.details.id,
						arrival_time: that.details.reservationNumArr[that.tabIndex].date,
						am_or_pm: that.am_or_pm,
						pay_type: that.pay_type
					})
					.then(res => {
						if (res.success) {
							that.pay(res.data.OrderNumber);
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							});
						}
					})
					.catch(err => {
						uni.showToast({
							title: '支付异常，请稍后重试',
							icon: 'none'
						});
						console.log(err);
					});
			},
			pay(OrderNumber) {
				let that = this
				// 请求支付接口
				that.$api.post('api/patient/order/payold', {
					ordernumber: OrderNumber,
					pay_type: that.pay_type
				}).then(res => {
					if (res.success) {
						let provider
						if (that.pay_type == 0) {
							provider = 'alipay'
						} else if (that.pay_type == 2) {
							provider = 'wxpay'
						}
						// 测试服调用沙盒代码代码，生产服删除
						//						var EnvUtils = plus.android.importClass("com.alipay.sdk.app.EnvUtils");
						//						EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);
						// 调用支付
						uni.requestPayment({
							provider: provider,
							orderInfo: res.data.PayStr,
							success(res) {
								uni.showToast({
									title: '支付成功！',
									duration: 2000,
									success() {
										uni.showToast({
											title: '您已预约成功！',
											duration: 2000,
											success: function(res) {
												setTimeout(function() {
													uni.navigateBack();
												}, 2000);
											}
										});
									}
								});
							},
							fail(err) {
								uni.showModal({
									title: '支付异常',
									content: "支付失败，请前往订单页面完成支付",
									showCancel: false,
									success() {
										uni.navigateTo({
											url: "../order/index?tabIndex=1"
										})
									}
								})
							}
						})
					}else{
						uni.showToast({
							title:res.msg,
							icon:'none'
						})
					}
				})

			},
			tabChange(e) {
				this.tabIndex = e;
			},
			dateToWeek(e) {
				let arr_date = e.split('-');
				let ssdate = new Date(arr_date[0], parseInt(arr_date[1] - 1), arr_date[2]);
				// let week = String(ssdate.getDay()).replace("0","日").replace("1","一").replace("2","二").replace("3","三").replace("4","四").replace("5","五").replace("6","六")
				// console.log(week)
				let weekday = ['日', '一', '二', '三', '四', '五', '六'];
				let week = weekday[ssdate.getDay()];
				return week;
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;
	}

	.space-padding>view {
		margin-top: $uni-spacing-col-lg;
		margin-right: $uni-spacing-row-lg;
		margin-left: $uni-spacing-row-lg;
	}

	.status_bar {
		height: 70px;
		width: 100%;
	}

	.details {
		background-color: $jian-bg-color;
		background: linear-gradient(-8deg, rgba(95, 231, 194, 1), rgba(88, 201, 238, 1));

		image {
			width: 80px;
			height: 60px;
			border-radius: $uni-border-radius-circle * 2;
		}

		color: $uni-text-color-inverse;
		font-size: $uni-font-size-base;

		.left {
			margin: $uni-spacing-col-lg 0px;
			margin-left: $uni-spacing-row-lg;
			margin-top: 20px;
		}

		.rigth {
			width: 100%;
			margin: $uni-spacing-col-lg $uni-spacing-row-sm;

			.btn {
				position: absolute;
				right: $uni-spacing-row-sm;
				border: 1px solid #ffffff;
				padding: $uni-spacing-col-sm $uni-spacing-row-sm;
				border-radius: $uni-border-radius-lg * 2;
				font-weight: 500;
			}
		}

		text {
			margin: $uni-spacing-col-sm $uni-spacing-row-sm;
		}

		.name {
			font-size: $uni-font-size-base;
			font-weight: bold;
		}

		.label {
			display: inline-block;
			border-radius: $uni-border-radius-lg;
			background-color: $uni-bg-color;
			color: #5fe7c2;
			padding: 2px 6px;
			font-size: $uni-font-size-sm;
		}
	}

	.tu {
		color: $uni-text-color;
		font-weight: bold;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;

		.name {
			// color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
			margin-left: $uni-spacing-col-base;
		}
	}

	.desc {
		margin-top: $uni-spacing-col-sm;
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;

		.item {
			margin-bottom: $uni-spacing-col-sm;
		}

		navigator {
			color: #007aff;
			display: inline;
		}

		.appoint {
			display: flex;
			justify-content: space-between;
			align-items: center;
			height: 80rpx;

			button {
				font-size: 34rpx;
				height: 50rpx;
				line-height: 50rpx;
				margin: 0;
				border-radius: 25rpx;
				border: none;
				background-color: $jian-bg-color;
				color: #fff;
			}
		}
	}

	.pay {
		margin-top: $uni-spacing-col-sm;
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;
	}
</style>