<template>
	<view>
		<view class="details ">
			<view class="status_bar"></view>
			<!-- 分享按钮 -->
			<view class="flex-start">
				<image class="left" v-if="details.face" :src="$baseUrl + details.face" mode=""></image>
				<image class="left" v-if="!details.face && details.gender == 1" src="../../static/img/defaultman.png"
					mode=""></image>
				<image class="left" v-if="!details.face && details.gender == 0" src="../../static/img/defaultgirl.png"
					mode=""></image>
				<view class="rigth">
					<view class="flex-start">
						<text class="name">{{ details.realname }}</text>
					</view>
					<view class="flex-start">
						<text>{{ details.departmentname }}</text>
						<text>{{ details.professional }}</text>
					</view>
					<view class="space-between">
						<text>{{ details.cliname }}</text>
					</view>
					<view class="flex-start">
						<text>服务人数:{{ details.number }}</text>
						<text>评分:{{ details.score }}分</text>
					</view>
					<view>
						<text class="label" v-for="n in details.postcard">{{ n }}</text>
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="tu">
			<text>图文咨询</text>
			<text class="name">医生：{{details.realname}}</text>
		</view> -->
		<view class="space-padding">
			<view class="desc">
				<view class="item"><text>&bull;可通过文字、图片的形式和医生沟通</text></view>
				<view><text>&bull;一次咨询的时效为24小时，在24小时内随时和医生沟通</text></view>
					<view v-if="details.is_prescribing"><text>&bull;如果您需要，医生可根据您的病情开方一次</text></view>
					<view v-if="details.is_prescribing"><text>&bull;如果您需要，可由第三方药房根据医生的处方提供中药代加工、药品购买、邮递服务，相关费用需另付</text></view>
			</view>
			<view class="pay">
				<text>共支付{{ details.inquiryfee }}元</text>
			</view>
			<view class="select">选择支付方式：</view>
			<view class="paytype flex-start">
				<image class="alipay" src="/static/img/doctors_img/zhifubao.png" mode=""></image>
				<view class="right space-between">
					<view>
						<view><text>支付宝支付</text></view>
						<view><text>推荐支付宝用户使用</text></view>
					</view>
					<image src="/static/img/doctors_img/dui.png" mode="" v-if="pay_type==0" @click="Alipay"></image>
					<image src="/static/img/doctors_img/uncheck.png" mode="" v-if="pay_type != 0" @click="Alipay">
					</image>
				</view>
			</view>

			<view class="paytype flex-start">
				<image class="alipay" src="/static/img/doctors_img/weixin.png" mode=""></image>
				<view class="right space-between">
					<view>
						<view><text>微信支付</text></view>
						<view><text>推荐微信用户使用</text></view>
					</view>
					<image src="/static/img/doctors_img/dui.png" mode="" v-if="pay_type==2" @click="WeChat"></image>
					<image src="/static/img/doctors_img/uncheck.png" mode="" v-if="pay_type != 2" @click="WeChat">
					</image>
				</view>
			</view>
			<view class="select">用户须知：</view>
			<view class="desc">
				<view class="item"><text>&bull;复诊必须提供包含诊断的实体医疗机构的病历资料，针对同诊断复诊。</text></view>
				<view><text>&bull;医生将在12小时内接诊，逾期未回复您可选择继续等待或退款。</text></view>
				<view><text>&bull;医生将与您通过图片、文字进行交流。</text></view>
				<view><text>&bull;医生将根据患者的实际情况辩证开方、给出调理建议。</text></view>
				<view><text>&bull;购买成功后请如实填写健康档案并发送给医生，医生将按照接诊先后顺序回复。</text></view>
				<view><text>&bull;问诊过程中请避免向医生咨询非患者本人的问题，否则医生有权提前结束咨询。</text></view>
				<view><text>&bull;本服务有效期：医生接诊后24小时内有效。</text></view>
				<view>
					<text>
						&bull;在线复诊过程中，若由于您无法提供实体医疗机构的诊断证明，或医生无法在线得出和您之前线下实体机构相同的诊断而无法为您开出处方和诊断的情况，将不会退还复诊费用。
					</text>
				</view>
				<view>
					<text>
						&bull;购买即同意
						<navigator hover-class="none" url="../login/protocol">《用户协议》 （包含远程医疗服务风险告知及知情同意书）</navigator>
						及
						<navigator hover-class="none" url="../login/privacy">《隐私保护政策》</navigator>
					</text>
				</view>
			</view>
			<view class="emptyBtn"></view>
			<button class="buyButton" @click="inquiryNewOrder()" id="newOrder" type="default">购买</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pay_type: -1,
				// show:false, //支付宝支付默认为未勾选
				flag: true, //微信支付默认为未勾选
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
				details: ''
			};
		},
		onLoad(option) {
			this.details = JSON.parse(decodeURIComponent(option.details));
		},
		methods: {
			// // 点击了支付宝支付之后 微信支付为未勾选状态
			Alipay() {
				this.pay_type = 0
			},
			// 点击了微信支付之后 支付宝支付为未勾选状态
			WeChat() {
				this.pay_type = 2
			},
			pay(OrderNumber) {
				let that = this
				// 请求支付接口
				that.$api.post('api/patient/order/payold', {
					ordernumber: OrderNumber,
					pay_type: that.pay_type
				}).then(res => {
					if (res.success) {
						let provider
						if (that.pay_type == 0) {
							provider = 'alipay'
						} else if (that.pay_type == 2) {
							provider = 'wxpay'
						}
						// 测试服调用沙盒代码代码，生产服删除
						//						var EnvUtils = plus.android.importClass("com.alipay.sdk.app.EnvUtils");
						//						EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);
						// 调用支付
						uni.requestPayment({
							provider: provider,
							orderInfo: res.data.PayStr,
							success(res) {
								uni.showToast({
									title: '支付成功！',
									duration: 2000,
									success() {
										uni.$emit('updateInquiry');
										uni.switchTab({
											url: '../inquiry/index'
										});
									}
								});
							},
							fail(err) {
								uni.showModal({
									title: '支付异常',
									content: "支付失败，请前往订单页面完成支付",
									showCancel: false,
									success() {
										uni.navigateTo({
											url: "../order/index?tabIndex=1"
										})
									}
								})
							}
						})
					}else{
						uni.showToast({
							title:res.msg,
							icon:'none'
						})
					}
				})
			},
			inquiryNewOrder() {
				let that = this;
				if (that.pay_type == -1) {
					uni.showToast({
						title: '请选择支付方式',
						icon: 'error'
					})
					return
				}
				uni.showModal({
					title: '温馨提示',
					content: '是否购买图文咨询',
					success: function(result) {
						if (result.confirm) {
							console.log(result.confirm);
							that.$api
								.post('api/patient/inquiry/generate', {
									doctor: that.details.id,
									pay_type: that.pay_type
								})
								.then(res => {
									console.log(res);
									if (res.success) {
										that.pay(res.msg.OrderNumber);
									} else {
										uni.showModal({
											title: '温馨提示',
											content: res.msg,
											showCancel: false,
											success(data) {
												if (data.confirm) {
													if (res.msg == '您与指定的医生已经有未付款的咨询订单') {
														uni.navigateTo({
															url: '../order/index?tabIndex=1'
														});
													} else if (res.msg.search('完善您的个人信息') > 0) {
														uni.navigateTo({
															url: '../user/userDetails'
														});
													} else if (res.msg == '您与指定的医生已经有正在进行中的问诊') {
														uni.switchTab({
															url: '../inquiry/index'
														});
													}
												}
											}
										});
									}
								})
								.catch(err => {
									console.log(err);
								});
						}
					}
				});

				return;
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;
	}

	.status_bar {
		height: 70px;
		width: 100%;
	}

	.space-padding>view {
		margin-top: $uni-spacing-col-lg;
		margin-right: $uni-spacing-row-lg;
		margin-left: $uni-spacing-row-lg;
	}

	.details {
		background-color: $jian-bg-color;
		background: linear-gradient(-8deg, rgba(95, 231, 194, 1), rgba(88, 201, 238, 1));

		image {
			width: 80px;
			height: 60px;
			border-radius: $uni-border-radius-circle * 2;
		}

		color: $uni-text-color-inverse;
		font-size: $uni-font-size-base;

		.left {
			margin: $uni-spacing-col-lg 0px;
			margin-left: $uni-spacing-row-lg;
			margin-top: 20px;
		}

		.rigth {
			width: 100%;
			margin: $uni-spacing-col-lg $uni-spacing-row-sm;

			.btn {
				position: absolute;
				right: $uni-spacing-row-sm;
				border: 1px solid #ffffff;
				padding: $uni-spacing-col-sm $uni-spacing-row-sm;
				border-radius: $uni-border-radius-lg * 2;
				font-weight: 500;
			}
		}

		text {
			margin: $uni-spacing-col-sm $uni-spacing-row-sm;
		}

		.name {
			font-size: $uni-font-size-base;
			font-weight: bold;
		}

		.label {
			display: inline-block;
			border-radius: $uni-border-radius-lg;
			background-color: $uni-bg-color;
			color: #5fe7c2;
			padding: 2px 6px;
			font-size: $uni-font-size-sm;
		}
	}

	.tu {
		color: $uni-text-color;
		font-weight: bold;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;

		.name {
			// color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
			margin-left: $uni-spacing-col-base;
		}
	}

	.desc {
		// margin-top: $uni-spacing-col-sm;
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;

		.item {
			margin-bottom: $uni-spacing-col-sm;
		}

		navigator {
			color: #007aff;
			display: inline;
		}
	}

	.pay {
		margin-top: $uni-spacing-col-sm;
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;
	}

	.paytype {
		align-items: center;

		.alipay {
			width: $uni-img-size-lg;
			height: $uni-img-size-lg;
			margin-right: $uni-spacing-row-sm;
		}

		image {
			width: $uni-img-size-sm;
			height: $uni-img-size-sm;
		}

		.right {
			width: calc(100% - 30px);
		}

		// margin-top: $uni-spacing-col-sm;
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-base $uni-spacing-row-lg;
	}

	.select {
		padding: 0 $uni-spacing-row-lg;
		margin-top: $uni-spacing-col-sm;
		font-size: $uni-font-size-base;
	}

	button {
		background-color: #16cc9f !important;
		color: $uni-text-color-inverse !important;
		// margin-top: 20px;
		// margin-right: $uni-spacing-row-lg;
		// margin-left: $uni-spacing-row-lg;
		margin:  20px $uni-spacing-row-lg 0;
	}

	.buyButton {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		text-align: center;
		height: 110upx;
		line-height: 60upx;
		position: fixed;
		bottom: 0upx;
		left: 0;
		right: 0;
		width: calc(100% - $uni-spacing-row-lg*2);
	}

	.emptyBtn {
		height: 90upx;
	}
</style>