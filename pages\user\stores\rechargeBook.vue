<template>
	<view>
		<view class="gai">
			<view class="flex-start-center time" @click="show()">
				<text>{{time}}</text><uni-icons type="arrowdown" size="20"></uni-icons>
			</view>
			<view class="flex-start-center money">
				<text>到账总额￥{{accounInfo.spending}}</text>
				<text>账户余额￥{{accounInfo.balance}}</text>
			</view>
		</view>
		<e-picker-plus ref="picker" errorMsg="选择的时间超出当前时间" mode="YM" @confirm="confirm" />
		<mescroll-body ref="mescrollRef" @init="mescrollInit" top="180" :down="downOption" @down="downCallback"
			@up="upCallback">
			<view class="list" v-for="n in dataList" :key="n.id"  @click="show()">
				<view class="space-between">
					<view>
						<text>会员充值</text>
					</view>
					<text class="amount">￥{{n.turnover}}</text>
				</view>
				<view class="time">	{{n.created_time}}</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	import uniIcons from "@/components/uni-icons/uni-icons.vue";
	import moment from 'moment';
	export default {
		components: {
			uniIcons
		},
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
				time: moment().format('YYYY年MM月'),
				end: moment().format('yyyy-MM-dd'),
				expenditure: 0, //指定月份的总支出金额
				dataList: [],
				clindId: '',
				accounInfo: {}
			};
		},
		onLoad(option) {
			this.clindId = option.id
		},
		methods: {
			/*下拉刷新的回调 */
			downCallback() {
				this.dataList = [];
				this.mescroll.resetUpScroll();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				let that = this;
				this.$api
					.get('api/patient/vip/getAddBalance', {
						params: {
							id: this.clindId,
							page: page.num,
							startTime: moment(this.time, 'YYYY年MM月').startOf('month').format(
								"YYYY-MM-DD"), //yyyy-mm-dd 开始时间 必填
							endTime: moment(this.time, 'YYYY年MM月').endOf('month').format(
								"YYYY-MM-DD"), //yyyy-mm-dd 结束时间 必填，必须大于startTime
						}
					})
					.then(res => {
						console.log(res);
						//设置列表数据
						that.dataList = this.dataList.concat(res.data.withdrawList);
						that.accounInfo = {
							spending: res.data.spending,
							balance: res.data.balance
						}
						that.mescroll.endSuccess(res.data.withdrawList.length);
						
					})
					.catch(res => {
						that.mescroll.endErr();
					});

			},

			show() {
				this.$refs.picker.show();
			},
			confirm(e) {
				// console.log(e);
				if (e.errorMsg) {
					uni.showModal({
						title: "温馨提示",
						content: e.errorMsg,
						showCancel: false
					})
				} else {
					this.time = moment(e.result, 'YYYY-MM').format('YYYY年MM月')
					this.dataList = [];
					this.mescroll.resetUpScroll();
				}
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;
		padding: $uni-spacing-col-base 0px;
	}

	.gai {
		position: fixed;
		top: 0px;
		left: 0px;
		z-index: 99;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg ;
		background-color: #FFFFFF;
		width: 100%;

		.time {
			&>text {
				font-size: $uni-font-size-lg;
				font-weight: bold;
				color: $uni-text-color;
			}

			margin-bottom: $uni-spacing-row-base;
		}

		.money {
			&>text {
				font-size: $uni-font-size-base;
				color: $uni-text-color-grey;
				margin-right: $uni-spacing-row-lg;
			}
		}
	}

	.list {
		background-color: #FFFFFF;
		margin: $uni-spacing-col-lg $uni-spacing-row-lg;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;

		&>view {
			font-size: $uni-font-size-lg;
			color: $uni-text-color;
		}

		.amount {
			font-size: $uni-font-size-base;
			color: $uni-text-color;
			font-weight: bold;
		}

		&>.time {
			font-size: $uni-font-size-base;
			color: $uni-text-color-grey;
		}
	}
</style>