<template>
	<view>
		<mescroll-body ref="mescrollRef" :down="downOption" @down="downCallback" @up="upCallback">
			<view class="placeholder"></view>
			<view class="scroll_item" v-for="(item,index) in videoList">
				<view class="info">
					<view class="avatar">
						<image class="avatar" v-if="item.face" :src="$baseUrl+item.face" mode=""></image>
						<image class="avatar" v-if="!item.face&&item.gender==0" src="../../static/img/defaultgirl.png"
							mode="">
						</image>
						<image class="avatar" v-if="!item.face&&item.gender==1" src="../../static/img/defaultman.png"
							mode="">
						</image>
					</view>
					<view class="userinfo">
						<view class="name">
							{{item.doctname}}
						</view>
						<view class="">
							{{item.department}}
						</view>
						<view class="time" v-if="item.status == 0 && item.ischarge == 1">
							下单时间：{{item.adddate}}
						</view>
						<view class="time" v-if="item.status == 1 && item.ischarge == 1">
							接诊时间：{{item.startdate}}
						</view>
						<view class="time" v-if="item.status == 2 && item.ischarge == 1">
							结束时间：{{item.enddate}}
						</view>
						<view class="btns" v-if="item.status == 1&& item.ischarge == 1">
							<view class="btn" @click="videostart(item,index)">视频通话</view>
							<view class="btn" @click="closeOrder(item,index)">结束咨询</view>
						</view>
						<view class="btns">
							<view></view>
							<view class="btn" @click="refund(item,index)"
								v-if="item.status == 0&&item.isBefore&& item.ischarge == 1">申请退款</view>
							<!-- <view class="btn" @click="refund(item,index)"  v-if="item.status == 2&& item.ischarge == 1&&item.duration==0">申请退款</view> -->
						</view>
					</view>
					<view class="status" v-if="item.status == 0 && item.ischarge == 1">待接诊</view>
					<view class="status" v-if="item.status == 1 && item.ischarge == 1">服务中</view>
					<view class="status" v-if="item.status == 2 && item.ischarge == 1">已结束</view>
					<view class="status" v-if="item.ischarge == 2">退款中</view>
					<view class="status" v-if="item.ischarge == 3">已退款</view>
				</view>

			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	import moment from 'moment';
	let timer
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
				videoList: [],
				isWebsoket: false,
				ordertimeout:false,
				isVideoConnect:false
			};
		},
		created() {
			let that = this
			uni.$on('updateCommlist', function() {
				that.videoList = [];
				that.mescroll.resetUpScroll();
			});
		},
		methods: {
			downCallback() {
				this.videoList = [];
				this.mescroll.resetUpScroll();
			},
			upCallback(page) {
				this.$api.get('api/patient/videocall/getlist', {
					params: {
						pager: page.num
					}
				}).then(res => {
					if (res.success) {
						let arr = res.data.map(n => {
							n.isBefore = (n.payDate != null) ? moment().isAfter(moment(n.payDate).add(12,
								'h')) : false;
							return n;
						})
						console.log(arr)
						this.videoList = this.videoList.concat(arr)
						this.mescroll.endSuccess(res.data.length);
					} else {
						this.mescroll.endErr();
					}
				}).catch(err => {
					this.mescroll.endErr();
				})
			},
			// 结束问诊
			closeOrder(data, index) {
				let that = this
				uni.showModal({
					title: '结束咨询',
					content: '结束后您将无法继续向医生发起通话请求，请确认医生已经完成服务后再关闭',
					success(res) {
						if (res.confirm) {
							that.$api.post('api/patient/videocall/close', {
								id: data.id
							}).then(res => {
								if (res.success) {
									that.downCallback()
								} else {
									uni.showToast({
										title: res.msg,
										icon: 'none'
									})
								}
							})
						}
					}
				})

			},
			// 开始通话
			async videostart(data, index) {
				if (uni.getSystemInfoSync().platform == 'ios') {
					let result = await getApp().requestIosPermission('camera', '摄像头权限')
					if (!result) {
						return
					}
				} else {
					let result = await getApp().requestAndroidPermission('android.permission.CAMERA', '摄像头权限')
					if (result != 1) {
						return
					}
				}
				let that = this
				that.$api.post('api/patient/videocall/getremainingtime', {
					inquiry: data.id
				}).then(res => {
					console.log(res)
					if (res.success) {
						let restTime = data.remainingtime * 60 - res.data.time
						let timetip
						if (restTime > 5) {
							timetip = '此订单的剩余通话时长剩余' + (parseInt(restTime / 60) == 0 ? "" : parseInt(restTime /
								60) + '分钟') + ((restTime % 60) == 0 ? '' : (restTime % 60) + '秒，是否确认开始通话')
						} else {
							uni.showToast({
								title: '您的通话时长已结束',
								icon: 'none'
							})

							if (restTime > 0) {
								that.$api.post('api/patient/videocall/close', {
									id: data.id
								}).then(res => {
									if (res.success) {
										that.downCallback()
									} else {
										uni.showToast({
											title: res.msg,
											icon: 'none'
										})
									}
								})
							} else {
								that.downCallback()
							}
							return
						}
						uni.showModal({
							title: '开始通话',
							content: timetip,
							success(res) {
								if (res.confirm) {
									// 获取token
									that.$api.post('api/patient/videocall/gettoken', {
										doctor: data.doctid,
										issingle: 1
									}).then(res => {
										// return
										if (res.success) {
											let soketOn = that.connectSocket(res.url.ws, res
												.data.token.token.ws, data.doctid)
											// 建立连接
											that.$YeIMCallKit.startC2CVideoCall({
												wsURL: res.url.livekiturl,
												token: res.data.token.token.livekit,
												userInfo: {
													nickname: that.$store.state
														.userInfo.patname,
													avatar: that.$store.state.userInfo
														.face,
													identity: res.data.token.from,
													userId: res.data.token.from
												},
												callerUserInfo: {
													nickname: data.doctname,
													avatar: that.$baseUrl + data.face,
													identity: res.data.token.to,
													userId: res.data.token.to
												},
												videoOptions: {
													encoding: 'HD'
												},
												callRing: '/static/audio/ring.mp3',
												answerRing: '/static/audio/ring.mp3'
											}, (e) => {
												console.log('callstatus', e)
												if (e.code == 20001) {
													// 接听后循环发送ws消息
													that.isVideoConnect = true
												} else {
													that.hangup(res.data.token.from,
														res.data.token.to, data.id,
														e.code)
												}
											});
											let subProcesses = plus.runtime.getSubNvueIds()
											console.log('subProcesses',subProcesses) // 输出包含所有子进程ID的数组
										} else {
											uni.showToast({
												title: res.msg,
												icon: 'none'
											})
											if (res.code == 1004) {
												that.downCallback()
											}
										}
									})
								}
							}
						})
					}

				})

			},
			// 挂断通话
			hangup(patient, doctor, inquiryid, code) {
				let that = this
				uni.sendSocketMessage({
					data: '2',
					complete: () => {
						that.isWebsoket = false
						uni.closeSocket()
						clearInterval(timer)
					}
				})
				that.isVideoConnect = false
				this.$api.post('api/patient/videocall/hangup', {
					patient: patient,
					doctor: doctor,
					inquiry: inquiryid
				}).then(res => {
					let title
					if (code == 10003) {
						title = "通话链接异常"
					} else if (code == 10005) {
						title = "对方暂未接听"
					} else if (code == 10006) {
						if (that.ordertimeout) {
							title = "您的通话时长已结束"
							that.ordertimeout = false
							that.downCallback()
						} else {
							title = "通话断开"
						}
					} else if (code == 20002) {
						title = "通话结束"
					} else if (code == 20003) {
						title = "对方无应答"
					}
					if (title) {
						uni.showToast({
							title: title,
							icon: 'none',
							duration: 3000
						})
					}
				})

			},
			// 链接websoket
			async connectSocket(url, token, toid) {
				let that = this
				uni.connectSocket({
					url: url + '?Token=' + token,
					// url: url,
					header: {
						'content-type': 'application/json'
					},
					method: 'GET',
					success: (res) => {},
					fail: (err) => {
						console.log(err)
					}
				});
				uni.onSocketOpen(function(res) {
					console.log('WebSocket连接已打开！');

				});
				uni.onSocketError(function(res) {
					console.log('WebSocket连接打开失败，请检查！');
				})
				uni.onSocketMessage(function(res) {
					console.log('收到服务器内容：', JSON.parse(res.data));
					let data = JSON.parse(res.data)
					if (data.code == 1000) {
						console.log("ws连接成功")
						that.sendWsMsg()
						that.$api.post('api/patient/videocall/pushmsg', {
							doctor: toid
						}).then(res => {
							console.log(res)
						})
					} else if (data.code == 2000) {
						console.log(JSON.parse(res.data))
					} else if (data.code == 2001) {
						that.ordertimeout = true
					} else {
						clearInterval(timer)
						uni.closeSocket()
					}
				});

			},
			//循环发送websoket消息
			sendWsMsg() {
				let that = this
				that.isWebsoket = true
				timer = setInterval(() => {
					console.log(11111)
					uni.sendSocketMessage({
						data: that.isVideoConnect?'1':'3',
						success(res) {
							console.log(res)
							if (!that.isWebsoket) {
								uni.closeSocket()
								clearInterval(timer)
							}
						},
						fail(err) {
							console.log(err)
						}
					})
				}, 2000);
			},
			refund(data, index) {
				let that = this
				let msg
				if (data.status == 0) {
					msg = "该医生长时间未接诊您的视频通话服务，是否申请视频通话服务退费？"
				} else if (data.status == 2) {
					msg = "检测到医生接诊后与您的通话时长不足5秒，您可以申请退款，是否确认退款？"
				}
				uni.showModal({
					title: '温馨提示',
					content: msg,
					success(result) {
						if (result.confirm) {
							that.$api
								.post('api/patient/order/refund', {
									ordernumber: data.orderid
								})
								.then(res => {
									console.log(res);
									uni.showToast({
										title: res.msg,
										icon: 'none'
									});
									if (res.success) {
										that.downCallback();
									}
								});
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss">
	.placeholder {
		padding-top: var(--status-bar-height);
		height: 110rpx;
	}

	.scroll_item {
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-base;
		margin: $uni-spacing-col-sm $uni-spacing-row-lg;
		margin-bottom: $uni-spacing-col-lg;
		border-radius: $uni-border-radius-base;
		box-sizing: border-box;

		.info {
			display: flex;
			justify-content: space-between;
			position: relative;

			.avatar {
				width: 120rpx;
				height: 120rpx;
				border-radius: 50%;
				overflow: hidden;

				image {
					width: 120rpx;
					height: 120rpx;
				}
			}

			.userinfo {
				width: calc(100% - 140rpx);
				color: $uni-text-color-grey;
				font-size: $uni-font-size-base;

				.name {
					color: $uni-text-color;
					font-size: $uni-font-size-lg;
					font-weight: 700;
				}
			}

			.status {
				position: absolute;
				right: 0;
				top: 0;
				color: $jian-bg-color;
				font-size: 26rpx;
				z-index: 999;
			}
		}

		.btns {
			display: flex;
			justify-content: space-between;
			margin-top: 10rpx;

			.btn {
				border-radius: 20rpx;
				padding: 0 $uni-spacing-row-sm;
				background-color: #ffffff;
				color: #ec4040;
				border: solid 1rpx #ec4040;
				font-size: $uni-font-size-base;
				margin-left: 15rpx;
				line-height: 40rpx;
				text-align: center;
				width: 45%;
			}
		}
	}
</style>