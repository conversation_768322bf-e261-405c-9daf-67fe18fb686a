<template>
	<view class="container">
		<bw-swiper :swiperList="swiperList" :interval="3000" :swiperType="true" @clickItem='clickswiper'
			style="width:100%;"></bw-swiper>
		<!--S 常用功能 -->
		<grid></grid>
		<!--E 常用功能 -->
		<!--S 推荐医生 -->
		<view style="background-color: #F7F7F7;height: 18rpx;width: 100%;margin-top: 20rpx;"></view>
		<doctorList></doctorList>
		<!--S 推荐医生 -->
		<!--S 健康百科 -->
		<!-- <view style="background-color: #F7F7F7qsd;height: 18rpx;width: 100%;margin-top: 20rpx;"></view>
		<articleList></articleList> -->
		<!--E 健康百科 -->
		<!--S 用户指南 -->
		<view style="background-color: #F7F7F7;height: 18rpx;width: 100%;margin-top: 20rpx;"></view>
		<guide></guide>
		<!--E 用户指南 -->
	</view>
</template>
<script>
	import bwSwiper from '@/wxcomponents/bw-swiper/bw-swiper.vue';
	import grid from '@/pages/index/grid.vue';
	import doctorList from "@/pages/index/doctorList.vue";
	import articleList from "@/pages/index/articleList.vue";
	import guide from "@/pages/index/guide.vue";
	import TIM from 'tim-wx-sdk';
	import COS from "cos-wx-sdk-v5"
	export default {
		data() {
			return {
				swiperList: [{
					img: '/static/img/banner.png'
				}],
				operationID: ''
			};
		},
		components: {
			bwSwiper,
			doctorList,
			grid,
			articleList,
			guide
		},
		onLoad() {
			// getApp().navtopages(0)
			let that = this;
			uni.$on('updateAuthStatus', function(status) {
				that.$store.commit("updateuserInfo", {
					authstatus: status
				})
			});
			this.getbannerlist();
			// this.getusersig();
			this.getUserinfo();
		},
		onReady() {

		},
		methods: {
			getusersig() {
				var that = this
				// #ifdef MP-WEIXIN
				this.tim = getApp().globalData.tim
				// #endif
				this.$api.get("api/patient/user/getusersig").then(res => {
					let promise = this.tim.login({
						userID: res.id,
						userSig: res.sig
					});
					promise.then((res) => {
						// console.log(res)
						//登录成功后 更新登录状态
						that.$store.commit("toggleisTimLogin", true);
						//自己平台的用户基础信息
					}).catch((err) => {
						console.warn('login error:', err); // 登录失败的相关信息
					});
				}).catch(err => {
					console.log(err)
				})

			},
			getUserinfo() {
				this.$api.get("api/patient/user/getinfo").then(res => {
					console.log('info:', res)
					if (res.face) {
						let faceurl = uni.getStorageSync(this.$baseUrl + res.face)
						if (faceurl) {
							res.face = faceurl

						} else {
							res.face = this.$baseUrl + res.face
						}

					} else {
						res.face = res.gender ? "/static/img/defaultman_user.png" :
							"/static/img/defaultgirl_user.png"
					}
					this.$store.commit("updateuserInfo", res)
					getApp().initPush()
					
				}).catch(err => {
					console.log(err)
				})
			},
			getbannerlist() { //获取banner
				this.$api.get('api/patient/getbannerlist').then(res => {
					console.log(res)
					this.swiperList = res
				})
			},
			clickswiper(data) {
				if (data.target == '') {
					return
				}
				if (data.urltype == 1) {
					console.log(data)
					uni.navigateTo({
						url: '/pages/webview/webview?data=' + JSON.stringify(data)
					})
				} else if (data.urltype == 2) {
					uni.navigateTo({
						url: data.target
					})
				} else if (data.urltype == 3) {
					uni.switchTab({
						url: data.target
					})
				}
			}
		}
	};
</script>
<style>
	page {
		background-color: #FFFFFF;
	}
</style>