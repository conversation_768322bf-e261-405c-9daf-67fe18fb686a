<template>
	<view class="base" v-show="i === index">
		<mescroll-uni class="friend" ref="mescrollRef2" height="87%" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption" :up="upOption">
			<navigator :url="'/pages/doctors/doctorsDetails?id='+n.doctor_id" class="list flex-start" v-for="n in addressBook.list">
				<view class="avatar">
					<image v-if="n.face" :src="$baseUrl+n.face" mode="widthFix"></image>
					<image v-if="!n.face && n.gender==1" src="../../static/img/defaultman.png" mode="widthFix"></image>
					<image v-if="!n.face && n.gender==0" src="../../static/img/defaultgirl.png" mode="widthFix"></image>
				</view>
				<view class="right">
					<view>
						<text class="name">{{n.realname}}</text>
						<text class="gender">{{n.gender?'男':'女'}}</text>
					</view>
					<view class="item">
						<text>{{n.brief}}</text>					
					</view>
				</view>
			</navigator>
		</mescroll-uni>
	</view>
</template>

<script>
import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
import MescrollUni from '@/components/mescroll-uni/mescroll-uni.vue';
import { mapState } from 'vuex';
export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MescrollUni
	},
	props:{
		i: Number, // 每个tab页的专属下标
		index: { // 当前tab的下标
			type: Number,
			default(){
				return 0
			}
		}
	},
	data() {
		return {
			// 下拉刷新的配置(可选)
			downOption: {
				auto: true,
				empty: {
					tip: '~ 空空如也 ~', // 提示					
				}
			},
			// 上拉加载的配置(可选)
			upOption: {
				auto: false,
				noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					tip: '~ 空空如也 ~', // 提示					
				}
			}
		};
	},
	computed: {
		...mapState({
			addressBook: state => state.addressBook
		})
	},
	methods: {
		/*下拉刷新的回调*/
		downCallback() {
			this.mescroll.endSuccess(0);
		},
		/*上拉加载的回调*/
		upCallback(page) {
			// 与 mescroll-body 的处理方式一致 >
			this.mescroll.endSuccess(0);
		}
	}
};
</script>

<style lang="scss">
.list {
	border-bottom: 1px solid $uni-border-color;
	padding: $uni-spacing-col-sm $uni-spacing-row-sm;
	box-sizing: border-box;
	.avatar {
		width: $uni-img-size-lg;
		height: $uni-img-size-lg;
		border-radius: $uni-border-radius-circle;
		margin-right: $uni-spacing-row-base;
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;
		image{
			width: 100%;
		}
	}
	.right{
		width: calc(100% - 100rpx);
	}
	.name {
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		margin-right: $uni-spacing-row-sm;
	}
	.gender{
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
	}
	.item {
		color: $uni-text-color-grey;
		font-size: $uni-font-size-sm;
	}
}
</style>
