{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__14BD443", "name": "欣九康健康版", "version": {"name": "1.1.4", "code": 17}, "description": "", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Payment": {}, "VideoPlayer": {}, "Push": {}, "OAuth": {}, "Share": {}, "Geolocation": {}, "Maps": {"coordType": "gcj02"}, "Barcode": {}, "Camera": {}, "Record": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#FFFFFFFF"}, "usingComponents": true, "nvueCompiler": "uni-app", "compilerVersion": 3, "privacy": {"prompt": "template", "template": {"title": "服务协议和隐私政策", "message": "　　请你务必审慎阅读、充分理解“服务协议”和“隐私政策”各条款，包括但不限于：为了更好的向你提供服务，我们需要收集你的设备标识、操作日志等信息用于分析、优化应用性能。<br/>　　你可阅读<a href=\"https://hs.xxjk99.com/patient/protocol.html\">《服务协议》</a>和<a href=\"https://hs.xxjk99.com/patient/privacy.html\">《隐私政策》</a>了解详细信息。如果你同意，请点击下面按钮开始接受我们的服务。", "buttonAccept": "同意", "buttonRefuse": "暂不同意", "second": {"title": "温馨提示", "message": "　　进入应用前，你需先同意<a href=\"https://hs.xxjk99.com/patient/protocol.html\">《服务协议》</a>和<a href=\"https://hs.xxjk99.com/patient/privacy.html\">《隐私政策》</a>，否则将退出应用。", "buttonAccept": "同意并继续", "buttonRefuse": "退出应用"}}}, "uniStatistics": {"enable": true}, "nativePlugins": {"DCloud-PushSound": {"__plugin_info__": {"name": "自定义推送铃声和渠道", "description": "自定义推送铃声同时支持 Android、iOS 平台", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=7482", "android_package_name": "com.xxjk99.patient", "ios_bundle_id": "com.xxjk99.health", "isCloud": true, "bought": 1, "pid": "7482", "parameters": {}}}, "Tuoyun-OpenIMSDK": {"__plugin_info__": {"name": "OpenIMSDK", "description": "IM", "platforms": "Android,iOS", "url": "", "android_package_name": "", "ios_bundle_id": "", "isCloud": false, "bought": -1, "pid": "", "parameters": {}}}, "wz-YeIMCallKit": {"__plugin_info__": {"name": "YeIM-CallKit 私有化部署的实时音视频聊天原生插件", "description": "基于LiveKit WebRTC SDK的实时音视频聊天 uni-app 原生插件，支持Android和iOS，无需第三方服务支持，本地架设流媒体服务端，适合内网、公网、私有化、私密化部署。", "platforms": "Android,iOS", "url": "https://ext.dcloud.net.cn/plugin?id=12875", "android_package_name": "com.xxjk99.patient", "ios_bundle_id": "com.xxjk99.health", "isCloud": true, "bought": 1, "pid": "12875", "parameters": {}}}}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#ffffff", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.45", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"color": "#B2B2B2", "selectedColor": "#16CC9F", "backgroundColor": "#ffffff", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "static/img/home.png", "selectedIconPath": "static/img/homeHL.png"}, {"pagePath": "pages/msg/msg", "text": "消息", "iconPath": "static/img/msg.png", "selectedIconPath": "static/img/msgHL.png"}, {"pagePath": "pages/inquiry/index", "text": "咨询", "iconPath": "static/img/inquiry.png", "selectedIconPath": "static/img/inquiryHL.png"}, {"pagePath": "pages/mall/index", "text": "商城", "iconPath": "static/img/mall.png", "selectedIconPath": "static/img/mallHL.png"}, {"pagePath": "pages/user/user", "text": "我的", "iconPath": "static/img/user.png", "selectedIconPath": "static/img/userHL.png"}], "borderStyle": "rgba(0,0,0,0.4)", "height": "50px"}, "launch_path": "__uniappview.html"}}