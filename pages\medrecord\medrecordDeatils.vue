<template>
	<view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" :up="upOption" :down="downOption" @down="downCallback">
			<div id="medrecord_info">
				<!--S 基本信息 -->
				<div class="infocss">
					<p style="text-align: center;">&middot; 诊断 &middot;</p>
					<p>【处方医生】{{ info.realname }}</p>
					<p v-if="info.cliname">【医院名称】{{ info.cliname }}</p>
					<p v-if="info.department">【所在科室】{{ info.department }}</p>
					<p>【患者姓名】{{ info.patname }}</p>
					<p>【患者性别】{{ info.gender == 0 ? '女' : '男' }}</p>
					<p v-if="info.birth">【患者年龄】{{info.birth|getage}}岁</p>
					<p v-else>【患者年龄】{{ info.age }}{{info.ageunit|getunit}}</p>
					<p v-if="info.time">【开方时间】{{ info.time }}</p>
					<p v-if="info.nowillness">【主述/现病史】{{ info.nowillness }}</p>
					<p v-if="info.pastill">【既往史】{{ info.pastill }}</p>
					<p v-if="info.pastill">【家族史】{{ info.familyill }}</p>
					<p v-if="info.personalill">【个人史】{{ info.personalill }}</p>
					<p v-if="info.allergies">【过敏史】{{ info.allergies }}</p>
					<p v-if="info.advice">【医嘱】{{ info.advice }}</p>
					<p>
						<span>【诊断】{{ info.diagonsis }}</span>
					</p>
				</div>
				<!--E 基本信息 -->
				<!--S 成药开方 -->
				<div class="prescribe" v-for="item in info.medicine">
					<p style="text-align: center;">&middot; 成药开方 &middot;</p>
					<div id="drug">
						<p>R:</p>
						<p v-for="d in item.drug">{{ d.name }} ,{{ d.usages }} ,{{ d.rate }} ,{{ d.singlemeasure }}{{ d.singlemeasureunit }}/次， 共{{ d.measure }}{{ d.measureunit }}</p>
					</div>
				</div>
				<!--E 成药开方 -->
				<!--S 饮片开方 -->
				<div class="prescribe" v-for="item in info.piece">
					<p style="text-align: center;">&middot; 饮片开方-{{ item.progress }} &middot;</p>
					<div id="drug">
						<p>R:</p>
						<p>
							<span v-for="d in item.drug">{{ d.name }} {{ d.number }}g/剂 {{ d.remark }}，</span>
						</p>
					</div>
					<p style="padding: 10px 0px;">
						共
						<span style="color: #24CDE6;">{{ item.number }}</span>
						剂
					</p>
					<p>每日{{ item.erveryday }}剂，{{ item.rate }}，{{ item.usages }}；{{ item.require }}；</p>
				</div>
				<!--E 饮片开方 -->
				<!--S 输液开方 -->
				<div class="prescribe" v-for="item in info.infusion">
					<p style="text-align: center;">&middot; 输液开方 &middot;</p>
					<div v-for="(g, index) in item.group">
						<p>组{{ index + 1 }}</p>
						<div>
							<p>R:</p>
							<p>
								<span v-for="d in g.drug">{{ d.name }}，单次{{ d.singlemeasure }}{{ d.singlemeasureunit }}， 共{{ d.measure }}{{ d.measureunit }}；</span>
							</p>
						</div>
						<p>{{ g.usages }}，{{ g.rate }}，{{ g.userate }}{{ g.useunit }}</p>
						<p>起始日期: {{ g.startdate }} 连续天数 {{ g.day }}天</p>
					</div>
				</div>
				<!--E 输液开方 -->
				<!-- S 其他 -->
				<div class="prescribe" v-if="info.check ||info.extra">
					<p style="text-align: center;">&middot;  其他  &middot;</p>
					<h5 v-if="info.check">检查项</h5>
					<p v-for=" c in info.check" >
						<expand-collapse pos="right">
							<template #title>
								<span>{{	c.name }}</span>
							</template>
							<template>
								<template v-if="c.check_status !== 2 && c.check_status !== 3">
									<text-wave :text="checkStatusMap[c.check_status]"></text-wave>
								</template>
								<template	v-else>
									<view class="inspection-results">
										<view class="result-title">{{checkType[c.type]}}结果</view>
										<rich-text :nodes="c.result"></rich-text>
									</view>
									<view v-if="c.check_time" class="inspection-time">{{checkType[c.type]}}时间: {{ c.check_time }}</view>
								</template>
							</template>
						</expand-collapse>
					</p>
					<h5 v-if="info.extra">附加费</h5>
					<p v-for=" e in info.extra"><span>{{e.name}}  </span></p>
				</div>
				<!-- E 其他 -->
			</div>
		</mescroll-body>
	</view>
</template>

<script>
import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
export default {
	mixins: [MescrollMixin],
	onLoad: function(option) {
		this.medid = option.medid;
		// console.log(option.medid)
	},
	filters:{
		getunit(unit){
			if(unit==0){
				return '天'
			}else if(unit==1){
				return '周'
			}else if(unit==2){
				return '月'
			}else if(unit==3){
				return '岁'
			}
		},
		getage(birthday){
			//出生时间 毫秒
			var birthDayTime = new Date(birthday).getTime();
			//当前时间 毫秒
			var nowTime = new Date().getTime();
			//一年毫秒数(365 * 86400000 = 31536000000)
			let age = parseInt((nowTime - birthDayTime) / 31536000000);
			age = age == 0 ? 1 : age;
			return age;
		}
	},
	data() {
		return {
			downOption: {
				auto: true //是否在初始化后,自动执行downCallback; 默认true
			},
			upOption:{
				use: false, 
			},
			medid:"",
			info: {},
			checkStatusMap: Object.freeze({
				0: '待检中...',
				1: '报告处理中...',
				2: '报告已生成',
				3: '报告已获取' // 在本项目中暂时没有用
			}),
			checkType: Object.freeze({
				1: '检查',
				2: '检验',
			})
		}
	},
	methods: {
			/*下拉刷新的回调 */
		downCallback() {
				uni.showLoading({
				    title: '加载中',
						mask:true
				});
				this.$api.get("api/patient/medrecord/getdruglist",{ params:{id: this.medid}})
				.then(res=>{
					this.mescroll.endSuccess();
					this.info =Object.assign({},this.info,res);
				})
				.catch(err => {
					this.mescroll.endErr();
				})
				
				this.$api.get("api/patient/medrecord/getetail",{ params:{id: this.medid}})
					.then(res => {
					uni.hideLoading();
					this.mescroll.endSuccess();
					this.info =Object.assign({},this.info,res);
				})
				.catch(err => {
					uni.hideLoading();
					this.mescroll.endErr();
				})
			},
	}
}
</script>

<style lang="scss">
	page{
		background-color: $uni-bg-color-grey;
	}
#medrecord_info {

	color: #8f8f94;
	font-size: 14px;
	padding: 8px 5px;
	width: calc(100% - 10px);
}	
.infocss ,.prescribe{
	background-color: white;
	margin: auto;
	font-size: 14px;
	color: #000000;
	line-height:20px;
	border-radius: 5px;
}
p{color: #000000;padding:5px;}
#drug{
	border-top: 1px dashed #efeff4;
	border-bottom: 1px dashed #efeff4;
	padding-bottom: 10px;
}
.prescribe{margin-top: 10px;padding: 10px;}	
.ageunit{
	margin-left: 1em;
}

.inspection-results{
		.result-title{
			font-weight: bold;
			text-align: center;
		}
}
.inspection-time{
		margin-top: 20upx;
		font-size: 24upx;
		color: #888888;
		text-align: center;
	}

</style>
