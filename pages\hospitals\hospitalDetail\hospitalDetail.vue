<template>
	<view class="base">
		<view class="detials">
			<!-- #ifdef APP-PLUS -->
			<view class="status_bar"></view>
			<!-- #endif -->
			<view class="name">{{ details.name }}</view>
			<view class="infos">
				<view class="headpic">
					<image v-if="details.face" :src="$hisBaseUrl + details.face" mode="widthFix"></image>
					<image v-else :src="$hisBaseUrl + 'Public/face/defaultclinic.jpg'" mode="widthFix"></image>
				</view>
				<view class="hospital_detial">
					<view class="datas">
						<view>近期预约量：{{ details.reservation_num }}人</view>
						<view>近期问诊量：{{ details.remote_num }}人</view>
						<view>评分：{{ details.score }}</view>
					</view>
				</view>
				<view class="rightCon">
					<view class="collection" @click="collection">
						<view v-if="hascollect" class="collected">
							<image class="collect_img" src="../../../static/img/collected.png" mode="widthFix"></image>
							<text>已收藏</text>
						</view>
						<view v-else>
							<image class="collect_img" src="../../../static/img/collection.png" mode="widthFix"></image>
							<text>收藏</text>
						</view>
					</view>
					<view class="navgitor" @click="navgitor">
						<image src="../../../static/img/navigitor.png" mode="widthFix"></image>
						<view>导航</view>
					</view>
				</view>
			</view>
		</view>
		<view class="address_infos">
			<view class="format">
				<view class="img_box">
					<image src="/static/img/address_hos.png" mode="widthFix"></image>
					<text>地址</text>
				</view>
				<view class="msgs">{{ details.address }}</view>
			</view>
			<view class="format">
				<view class="img_box">
					<image class="imgbg" src="/static/img/Introduction.png" mode="widthFix"></image>
					<text>介绍</text>
				</view>
				<text class="msgs">{{ details.description }}</text>
			</view>
		</view>
		<view class="split_line"></view>
		<recommendedList :hospitalList="details.doctorlist" :clinid="details.id"></recommendedList>
		<view class="split_line"></view>
		<servicehistory :clincid="details.id"></servicehistory>
	</view>
</template>

<script>
import recommendedList from './recommendedList';
import servicehistory from './ServiceHistory';
export default {
	data() {
		return {
			details: {},
			hascollect: false,
			// #ifdef MP-WEIXIN
			$hisBaseUrl: ''
			// #endif
		};
	},
	components: {
		recommendedList,
		servicehistory
	},
	created() {
		uni.removeStorageSync('hospitalId');
		// #ifdef APP-PLUS
		plus.runtime.arguments = null;
		// #endif
	},
	onLoad(option) {
		// #ifdef MP-WEIXIN
		(this.$hisBaseUrl = getApp().globalData.$hisBaseUrl),
			// #endif
			// 获取医院信息
			this.$api
				.get('api/patient/hospital/getdetail', {
					params: {
						id: option.id
					}
				})
				.then(res => {
					if (res.success) {
					this.details = res.data;
					this.hascollect = res.data.hascollect;
					// 记录收藏历史

					// 获取本地数据
					let history = uni.getStorageSync('clindHistory');
					// 要添加的数据
					let history_Detial = res.data;
					let timeData = new Date();
					let str = this.$commen.dateTimeFliter(timeData);
					history_Detial.visitTime = str;
					// 判断是否为空数组，非空数组则插入
					if (!history) {
						history = [history_Detial];
					} else {
						history.unshift(history_Detial);
						// 数组查重
						let newarr = [
							...new Set(
								history.map(e => {
									return e.id;
								})
							)
						].map(e => {
							return history.find(a => {
								return e == a.id;
							});
						});
						history = newarr;
						// 限制记录条数，超过300条删除最后一条
						if (history.length == 301) {
							history.splice(300, 1);
						}
					}
					uni.setStorageSync('clindHistory', history);
					} else {
						uni.showModal({
							title: '医院信息不存在或账号已停用',
							content: res.msg,
							showCancel: false,
							success(result) {
								uni.navigateBack();
							}
						});
					}
				});
	},
	onNavigationBarButtonTap(e) {
		this.shareHospital();
	},
	methods: {
		// 收藏医院
		collection() {
			this.$api
				.post('api/patient/collect/collectclinic', {
					clinid: this.details.id
				})
				.then(res => {
					this.hascollect = !this.hascollect;
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
				});
		},
		// 导航
		navgitor() {
			this.bd09togcj02(Number(this.details.lon), Number(this.details.lat));
		},
		shareHospital() {
			let info = this.details;
			uni.share({
				provider: 'weixin',
				scene: 'WXSceneSession',
				type: 0,
				href: this.$baseUrl + 'patient/share.html?hospitalId=' + info.id + '&userid=' + this.$store.state.userInfo.userid + '&type=3',
				title: info.name,
				summary: '简介：' + info.description,
				imageUrl: this.$hisBaseUrl + info.face,
				success: function(res) {
					console.log('success:' + JSON.stringify(res));
				},
				fail: function(err) {
					console.log('fail:' + JSON.stringify(err));
				}
			});
		},
		// bd09=>gcj02坐标系转换
		bd09togcj02(bd_lon, bd_lat) {
			var x_pi = (3.14159265358979324 * 3000.0) / 180.0;
			var x = bd_lon - 0.0065;
			var y = bd_lat - 0.006;
			var z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
			var theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
			var gg_lng = z * Math.cos(theta);
			var gg_lat = z * Math.sin(theta);
			// 打开导航
			uni.openLocation({
				type: 'gcj02',
				latitude: gg_lat,
				longitude: gg_lng,
				name: this.details.name,
				address: this.details.address,
				success(res) {
					console.log('success');
				},
				fail(res) {
					uni.showToast({
						title: '暂未获取到此诊所的位置信息',
						icon: 'none'
					});
				}
			});
		}
	}
};
</script>

<style lang="scss">
.base {
	.split_line {
		height: 24rpx;
		background-color: $uni-bg-color-grey;
	}

	.detials {
		background-color: #5bdcd4;
		background: linear-gradient(to bottom, rgba(91, 220, 212, 1), rgba(88, 201, 238, 1));
		padding: $uni-spacing-col-base $uni-spacing-row-base;
		color: $uni-text-color-inverse;
		font-size: $uni-font-size-base;
		position: relative;

		.name {
			font-size: $uni-font-size-lg;
			font-weight: bold;
		}

		.infos {
			display: flex;
			align-items: center;
			margin-top: 10rpx;

			.headpic {
				width: 160rpx;
				height: 160rpx;
				border-radius: 50%;
				overflow: hidden;
				display: flex;
				justify-content: center;
				align-items: center;

				image {
					width: 100%;
				}
			}

			.hospital_detial {
				margin: 20rpx;
			}

			.rightCon {
				margin-left: auto;
				display: flex;

				.collection {
					background-color: rgba($color: #fff, $alpha: 0.3);
					border-radius: 10%;
					width: 100rpx;
					padding: 15rpx 0;
					text-align: center;
					margin-left: auto;

					.collected {
						color: $jian-bg-color;
					}

					.collect_img {
						width: 40rpx;
						display: block;
						margin: 0 auto;
					}
				}

				.navgitor {
					width: 100rpx;
					background-color: rgba(255, 255, 255, 0.8);
					text-align: center;
					border-radius: 15rpx;
					padding: 10rpx 0;
					margin-left: 10rpx;
					color: $jian-bg-color;

					image {
						width: 40rpx;
						margin: 0 auto;
					}
				}
			}
		}
	}

	.address_infos {
		padding: 0 24rpx 10rpx;
		box-sizing: border-box;
		font-size: $uni-font-size-base;

		.format {
			margin-top: 20rpx;
			font-weight: normal;

			.img_box {
				display: flex;
				align-items: center;

				image {
					width: 60rpx;
				}

				.imgbg {
					width: 50rpx;
					margin: 0 5rpx;
				}
			}

			.msgs {
				margin-top: 10rpx;
				color: $uni-text-color-grey;
				margin-left: 18rpx;
			}
		}
	}
}
</style>
