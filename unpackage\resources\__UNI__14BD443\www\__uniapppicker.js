!function(e){var t={};function n(r){if(t[r])return t[r].exports;var a=t[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,n),a.l=!0,a.exports}n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)n.d(r,a,function(t){return e[t]}.bind(null,a));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=43)}([function(e,t){e.exports={}},function(e,t,n){"use strict";function r(e,t,n,r,a,i,o,s,u,c){var l,f="function"==typeof e?e.options:e;if(u){f.components||(f.components={});var d=Object.prototype.hasOwnProperty;for(var h in u)d.call(u,h)&&!d.call(f.components,h)&&(f.components[h]=u[h])}if(c&&((c.beforeCreate||(c.beforeCreate=[])).unshift((function(){this[c.__module]=this})),(f.mixins||(f.mixins=[])).push(c)),t&&(f.render=t,f.staticRenderFns=n,f._compiled=!0),r&&(f.functional=!0),i&&(f._scopeId="data-v-"+i),o?(l=function(e){(e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext)||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),a&&a.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(o)},f._ssrRegister=l):a&&(l=s?function(){a.call(this,this.$root.$options.shadowRoot)}:a),l)if(f.functional){f._injectStyles=l;var p=f.render;f.render=function(e,t){return l.call(t),p(e,t)}}else{var v=f.beforeCreate;f.beforeCreate=v?[].concat(v,l):[l]}return{exports:e,options:f}}n.d(t,"a",(function(){return r}))},function(e,t,n){Vue.prototype.__$appStyle__={},Vue.prototype.__merge_style&&Vue.prototype.__merge_style(n(3).default,Vue.prototype.__$appStyle__)},function(e,t,n){"use strict";n.r(t);var r=n(0),a=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t.default=a.a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={onLoad:function(){this.initMessage()},methods:{initMessage:function(){var e=this,t=plus.webview.currentWebview().extras||{},n=t.from,r=(t.callback,t.runtime),a=t.data,i=void 0===a?{}:a,o=t.useGlobalEvent;this.__from=n,this.__runtime=r,this.__page=plus.webview.currentWebview().id,this.__useGlobalEvent=o,this.data=JSON.parse(JSON.stringify(i)),plus.key.addEventListener("backbutton",(function(){"function"==typeof e.onClose?e.onClose():plus.webview.currentWebview().close("auto")}));var s=this,u=function(e){var t=e.data&&e.data.__message;t&&s.__onMessageCallback&&s.__onMessageCallback(t.data)};this.__useGlobalEvent?weex.requireModule("globalEvent").addEventListener("plusMessage",u):new BroadcastChannel(this.__page).onmessage=u},postMessage:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=JSON.parse(JSON.stringify({__message:{__page:this.__page,data:e,keep:t}})),r=this.__from;if("v8"===this.__runtime)if(this.__useGlobalEvent)plus.webview.postMessageToUniNView(n,r);else{var a=new BroadcastChannel(r);a.postMessage(n)}else{var i=plus.webview.getWebviewById(r);i&&i.evalJS("__plusMessage&&__plusMessage(".concat(JSON.stringify({data:n}),")"))}},onMessage:function(e){this.__onMessageCallback=e}}};t.default=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={data:function(){return{locale:"en",fallbackLocale:"en",localization:{en:{done:"OK",cancel:"Cancel"},zh:{done:"完成",cancel:"取消"},"zh-hans":{},"zh-hant":{},messages:{}},localizationTemplate:{}}},onLoad:function(){this.initLocale()},created:function(){this.initLocale()},methods:{initLocale:function(){if(!this.__initLocale){this.__initLocale=!0;var e=(plus.webview.currentWebview().extras||{}).data||{};if(e.messages&&(this.localization.messages=e.messages),e.locale)this.locale=e.locale.toLowerCase();else{var t=plus.os.language.toLowerCase().split("/")[0].replace("_","-").split("-"),n=t[1];n&&(t[1]={chs:"hans",cn:"hans",sg:"hans",cht:"hant",tw:"hant",hk:"hant",mo:"hant"}[n]||n),t.length=t.length>2?2:t.length,this.locale=t.join("-")}}},localize:function(e){var t=this,n=this.locale,r=n.split("-")[0],a=this.fallbackLocale,i=function(e){return Object.assign({},t.localization[e],(t.localizationTemplate||{})[e])};return i("messages")[e]||i(n)[e]||i(r)[e]||i(a)[e]||e}}};t.default=r},,,,function(e,t,n){"use strict";var r=n(30),a=n(20),i=n(1);var o=Object(i.a)(a.default,r.b,r.c,!1,null,null,"9abad300",!1,r.a,void 0);(function(e){this.options.style||(this.options.style={}),Vue.prototype.__merge_style&&Vue.prototype.__$appStyle__&&Vue.prototype.__merge_style(Vue.prototype.__$appStyle__,this.options.style),Vue.prototype.__merge_style?Vue.prototype.__merge_style(n(39).default,this.options.style):Object.assign(this.options.style,n(39).default)}).call(o),t.default=o.exports},,,,,,,,,,,function(e,t,n){"use strict";var r=n(21),a=n.n(r);t.default=a.a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n(44));function a(e){return e&&e.__esModule?e:{default:e}}var i={mixins:[a(n(4)).default],components:{picker:r.default},data:function(){return{range:[],rangeKey:"",value:0,mode:"selector",fields:"day",start:"",end:"",disabled:!1,visible:!1}},onLoad:function(){var e=this;null===this.data?this.postMessage({event:"created"},!0):this.showPicker(this.data),this.onMessage((function(t){e.showPicker(t)}))},onReady:function(){var e=this;this.$nextTick((function(){e.visible=!0}))},methods:{showPicker:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.column;for(var n in e)"column"!==n&&("number"==typeof t?this.$set(this.$data[n],t,e[n]):this.$data[n]=e[n])},close:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.value,a=void 0===r?-1:r;this.visible=!1,setTimeout((function(){t.postMessage({event:e,value:a})}),210)},onClose:function(){this.close("cancel")},columnchange:function(e){var t=e.column,n=e.value;this.$set(this.value,t,n),this.postMessage({event:"columnchange",column:t,value:n},!0)}}};t.default=i},function(e,t,n){"use strict";n.r(t);var r=n(23),a=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t.default=a.a},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=n(45),a=s(n(5)),i=s(n(46)),o=s(n(47));function s(e){return e&&e.__esModule?e:{default:e}}function u(e){return function(e){if(Array.isArray(e)){for(var t=0,n=new Array(e.length);t<e.length;t++)n[t]=e[t];return n}}(e)||function(e){if(Symbol.iterator in Object(e)||"[object Arguments]"===Object.prototype.toString.call(e))return Array.from(e)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function c(){if(this.mode===d.TIME)return"00:00";if(this.mode===d.DATE){var e=(new Date).getFullYear()-61;switch(this.fields){case h.YEAR:return e;case h.MONTH:return e+"-01";default:return e+"-01-01"}}return""}function l(){if(this.mode===d.TIME)return"23:59";if(this.mode===d.DATE){var e=(new Date).getFullYear()+61;switch(this.fields){case h.YEAR:return e;case h.MONTH:return e+"-12";default:return e+"-12-31"}}return""}var f=weex.requireModule("animation"),d={SELECTOR:"selector",MULTISELECTOR:"multiSelector",TIME:"time",DATE:"date",REGION:"region"},h={YEAR:"year",MONTH:"month",DAY:"day"},p=!1,v={name:"Picker",mixins:[a.default,o.default,i.default],props:{pageId:{type:Number,default:0},range:{type:Array,default:function(){return[]}},rangeKey:{type:String,default:""},value:{type:[Number,String,Array],default:0},mode:{type:String,default:d.SELECTOR},fields:{type:String,default:h.DAY},start:{type:String,default:c},end:{type:String,default:l},disabled:{type:[Boolean,String],default:!1},visible:{type:Boolean,default:!1}},data:function(){return{valueSync:null,timeArray:[],dateArray:[],valueArray:[],oldValueArray:[],fontSize:16,height:261,android:"android"===weex.config.env.platform.toLowerCase()}},computed:{rangeArray:function(){var e=this.range;switch(this.mode){case d.SELECTOR:return[e];case d.MULTISELECTOR:return e;case d.TIME:return this.timeArray;case d.DATE:var t=this.dateArray;switch(this.fields){case h.YEAR:return[t[0]];case h.MONTH:return[t[0],t[1]];default:return[t[0],t[1],t[2]]}}return[]},startArray:function(){return this._getDateValueArray(this.start,c.bind(this)())},endArray:function(){return this._getDateValueArray(this.end,l.bind(this)())},textMaxLength:function(){return Math.floor(Math.min(weex.config.env.deviceWidth,weex.config.env.deviceHeight)/(this.fontSize*weex.config.env.scale+1)/this.rangeArray.length)},maskStyle:function(){return{opacity:this.visible?1:0,"background-color":this.android?"rgba(0, 0, 0, 0.6)":"rgba(0, 0, 0, 0.4)"}},pickerViewIndicatorStyle:function(){return"height: 34px;border-color:".concat(this.isDark?"rgba(255, 255, 255, 0.05)":"#C8C9C9",";border-top-width:0.5px;border-bottom-width:0.5px;")},pickerViewColumnTextStyle:function(){return{fontSize:this.fontSize+"px","line-height":"34px","text-align":"center",color:this.isDark?"rgba(255, 255, 255, 0.8)":"#000"}},pickerViewMaskTopStyle:function(){return this.isDark?"background-image: linear-gradient(to bottom, rgba(35, 35, 35, 0.95), rgba(35, 35, 35, 0.6));":""},pickerViewMaskBottomStyle:function(){return this.isDark?"background-image: linear-gradient(to top,rgba(35, 35, 35, 0.95), rgba(35, 35, 35, 0.6));":""}},watch:{value:function(){this._setValueSync()},mode:function(){this._setValueSync()},range:function(){this._setValueSync()},valueSync:function(){this._setValueArray(),p=!0},valueArray:function(e){var t=this;if(this.mode===d.TIME||this.mode===d.DATE){var n=this.mode===d.TIME?this._getTimeValue:this._getDateValue,r=this.valueArray,a=this.startArray,i=this.endArray;if(this.mode===d.DATE){var o=this.dateArray,s=o[2].length,u=Number(o[2][r[2]])||1,c=new Date("".concat(o[0][r[0]],"/").concat(o[1][r[1]],"/").concat(u)).getDate();c<u&&(r[2]-=c+s-u)}n(r)<n(a)?this._cloneArray(r,a):n(r)>n(i)&&this._cloneArray(r,i)}e.forEach((function(e,n){e!==t.oldValueArray[n]&&(t.oldValueArray[n]=e,t.mode===d.MULTISELECTOR&&t.$emit("columnchange",{column:n,value:e}))}))},visible:function(e){var t=this;e?setTimeout((function(){f.transition(t.$refs.picker,{styles:{transform:"translateY(0)"},duration:200})}),20):f.transition(this.$refs.picker,{styles:{transform:"translateY(".concat(283+this.safeAreaInsets.bottom,"px)")},duration:200})}},created:function(){this._createTime(),this._createDate(),this._setValueSync()},methods:{getTexts:function(e,t){var n=this,r=this.textMaxLength;return e.map((function(e){var a=String("object"==typeof e?e[n.rangeKey]||"":n._l10nItem(e,t));if(r>0&&a.length>r)for(var i=0,o=0,s=0;s<a.length;s++){var u=a.charCodeAt(s);if((i+=u>127||94===u?1:.65)<=r-1&&(o=s),i>=r)return s===a.length-1?a:a.substr(0,o+1)+"…"}return a||" "})).join("\n")},_createTime:function(){var e=[],t=[];e.splice(0,e.length);for(var n=0;n<24;n++)e.push((n<10?"0":"")+n);t.splice(0,t.length);for(var r=0;r<60;r++)t.push((r<10?"0":"")+r);this.timeArray.push(e,t)},_createDate:function(){for(var e=[],t=function(e){var t=(new Date).getFullYear(),n=t-61,r=t+61;if(e.start){var a=new Date(e.start).getFullYear();!isNaN(a)&&a<n&&(n=a)}if(e.end){var i=new Date(e.start).getFullYear();!isNaN(i)&&i>r&&(r=i)}return{start:n,end:r}}(this),n=t.start,r=t.end;n<=r;n++)e.push(String(n));for(var a=[],i=1;i<=12;i++)a.push((i<10?"0":"")+i);for(var o=[],s=1;s<=31;s++)o.push((s<10?"0":"")+s);this.dateArray.push(e,a,o)},_getTimeValue:function(e){return 60*e[0]+e[1]},_getDateValue:function(e){return 31*e[0]*12+31*(e[1]||0)+(e[2]||0)},_cloneArray:function(e,t){for(var n=0;n<e.length&&n<t.length;n++)e[n]=t[n]},_setValueSync:function(){var e=this,t=this.value;switch(p=!1,this.mode){case d.MULTISELECTOR:Array.isArray(t)||(t=[]),Array.isArray(this.valueSync)||(this.valueSync=[]);for(var n=this.valueSync.length=Math.max(t.length,this.range.length),r=0;r<n;r++){var a=Number(t[r]),i=Number(this.valueSync[r]),o=isNaN(a)?isNaN(i)?0:i:a,s=this.range[r]?this.range[r].length-1:0;this.valueSync.splice(r,1,o<0||o>s?0:o)}break;case d.TIME:case d.DATE:this.valueSync=String(t);break;default:var u=Number(t);this.valueSync=u<0?0:u}this.$nextTick((function(){!p&&e._setValueArray()}))},_setValueArray:function(){p=!0;var e,t=this.valueSync;switch(this.mode){case d.MULTISELECTOR:e=u(t);break;case d.TIME:e=this._getDateValueArray(t,(0,r.formatDateTime)({mode:d.TIME}));break;case d.DATE:e=this._getDateValueArray(t,(0,r.formatDateTime)({mode:d.DATE}));break;default:e=[t]}this.oldValueArray=u(e),this.valueArray=u(e)},_getValue:function(){var e=this,t=this.valueArray;switch(this.mode){case d.SELECTOR:return t[0];case d.MULTISELECTOR:return t.map((function(e){return e}));case d.TIME:return this.valueArray.map((function(t,n){return e.timeArray[n][t]})).join(":");case d.DATE:return this.valueArray.map((function(t,n){return e.dateArray[n][t]})).join("-")}},_getDateValueArray:function(e,t){var n=this.mode===d.DATE?"-":":",r=this.mode===d.DATE?this.dateArray:this.timeArray,a=3;switch(this.fields){case h.YEAR:a=1;break;case h.MONTH:a=2}for(var i=String(e).split(n),o=[],s=0;s<a;s++){var u=i[s];o.push(r[s].indexOf(u))}return o.indexOf(-1)>=0&&(o=t?this._getDateValueArray(t):o.map((function(){return 0}))),o},_change:function(){this.$emit("change",{value:this._getValue()})},_cancel:function(){this.$emit("cancel")},_pickerViewChange:function(e){this.valueArray=this._l10nColumn(e.detail.value,!0)},_l10nColumn:function(e,t){if(this.mode===d.DATE){var n=this.locale;if(!n.startsWith("zh"))switch(this.fields){case h.YEAR:return e;case h.MONTH:return[e[1],e[0]];default:switch(n){case"es":case"fr":return[e[2],e[1],e[0]];default:return t?[e[2],e[0],e[1]]:[e[1],e[2],e[0]]}}}return e},_l10nItem:function(e,t){if(this.mode===d.DATE){var n=this.locale;if(n.startsWith("zh")){return e+["年","月","日"][t]}if(this.fields!==h.YEAR&&t===(this.fields===h.MONTH||"es"!==n&&"fr"!==n?0:1)){var r;switch(n){case"es":r=["enero","febrero","marzo","abril","mayo","junio","​​julio","agosto","septiembre","octubre","noviembre","diciembre"];break;case"fr":r=["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"];break;default:r=["January","February","March","April","May","June","July","August","September","October","November","December"]}return r[Number(e)-1]}}return e}}};t.default=v},function(e,t){e.exports={content:{position:"absolute",top:0,left:0,bottom:0,right:0},"uni-mask":{position:"absolute",top:0,left:0,bottom:0,right:0,backgroundColor:"rgba(0,0,0,0.4)",opacity:0,transitionProperty:"opacity",transitionDuration:200,transitionTimingFunction:"linear"},"@TRANSITION":{"uni-mask":{property:"opacity",duration:200,timingFunction:"linear"}},"uni-mask-visible":{opacity:1},"uni-picker":{position:"absolute",left:0,bottom:0,right:0,backgroundColor:"#ffffff",color:"#000000",flexDirection:"column",transform:"translateY(295px)"},"uni-picker-header":{height:"45",borderBottomWidth:"0.5",borderBottomColor:"#C8C9C9",backgroundColor:"#FFFFFF",fontSize:"20"},"uni-picker-action":{position:"absolute",textAlign:"center",top:0,height:"45",paddingTop:0,paddingRight:"14",paddingBottom:0,paddingLeft:"14",fontSize:"17",lineHeight:"45"},"uni-picker-action-cancel":{left:0,color:"#888888"},"uni-picker-action-confirm":{right:0,color:"#007aff"},"uni-picker-content":{flex:1},"uni-picker-dark":{backgroundColor:"#232323"},"uni-picker-header-dark":{backgroundColor:"#232323",borderBottomColor:"rgba(255,255,255,0.05)"},"uni-picker-action-cancel-dark":{color:"rgba(255,255,255,0.8)"}}},function(e,t){e.exports={page:{flex:1}}},,,,,function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("scroll-view",{staticStyle:{flexDirection:"column"},attrs:{scrollY:!0,enableBackToTop:!0,bubble:"true"}},[n("view",{staticClass:["page"]},[n("picker",{attrs:{range:e.range,rangeKey:e.rangeKey,value:e.value,mode:e.mode,fields:e.fields,start:e.start,end:e.end,disabled:e.disabled,visible:e.visible},on:{change:function(t){e.close("change",t)},cancel:function(t){e.close("cancel",t)},columnchange:e.columnchange}})],1)])},a=[];n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}))},,,function(e,t,n){"use strict";var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:["content"],class:{dark:e.isDark}},[n("div",{ref:"mask",staticClass:["uni-mask"],style:e.maskStyle,on:{click:e._cancel}}),n("div",{ref:"picker",staticClass:["uni-picker"],class:{"uni-picker-dark":e.isDark},style:"padding-bottom:"+e.safeAreaInsets.bottom+"px;height:"+(e.height+e.safeAreaInsets.bottom)+"px;"},[n("div",{staticClass:["uni-picker-header"],class:{"uni-picker-header-dark":e.isDark}},[n("u-text",{staticClass:["uni-picker-action","uni-picker-action-cancel"],class:{"uni-picker-action-cancel-dark":e.isDark},style:"left:"+e.safeAreaInsets.left+"px",on:{click:e._cancel}},[e._v(e._s(e.localize("cancel")))]),n("u-text",{staticClass:["uni-picker-action","uni-picker-action-confirm"],style:"right:"+e.safeAreaInsets.right+"px",on:{click:e._change}},[e._v(e._s(e.localize("done")))])]),e.visible?n("picker-view",{staticClass:["uni-picker-content"],style:"margin-left:"+e.safeAreaInsets.left+"px",attrs:{height:"216",indicatorStyle:e.pickerViewIndicatorStyle,maskTopStyle:e.pickerViewMaskTopStyle,maskBottomStyle:e.pickerViewMaskBottomStyle,value:e._l10nColumn(e.valueArray)},on:{change:e._pickerViewChange}},e._l(e._l10nColumn(e.rangeArray),(function(t,r){return n("picker-view-column",{key:r,attrs:{length:t.length}},[n("u-text",{staticClass:["uni-picker-item"],style:e.pickerViewColumnTextStyle},[e._v(e._s(e.getTexts(t,r)))])])})),1):e._e()],1)])},a=[];n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){}))},,,,,function(e,t,n){"use strict";n.r(t);var r=n(24),a=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t.default=a.a},function(e,t,n){"use strict";n.r(t);var r=n(25),a=n.n(r);for(var i in r)"default"!==i&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t.default=a.a},,,,function(e,t,n){"use strict";n.r(t);n(2);var r=n(9);r.default.mpType="page",r.default.route="template/__uniapppicker",r.default.el="#root",new Vue(r.default)},function(e,t,n){"use strict";n.r(t);var r=n(33),a=n(22);for(var i in a)"default"!==i&&function(e){n.d(t,e,(function(){return a[e]}))}(i);var o=n(1);var s=Object(o.a)(a.default,r.b,r.c,!1,null,null,"1f8b5a47",!1,r.a,void 0);(function(e){this.options.style||(this.options.style={}),Vue.prototype.__merge_style&&Vue.prototype.__$appStyle__&&Vue.prototype.__merge_style(Vue.prototype.__$appStyle__,this.options.style),Vue.prototype.__merge_style?Vue.prototype.__merge_style(n(38).default,this.options.style):Object.assign(this.options.style,n(38).default)}).call(s),t.default=s.exports},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.formatDateTime=function(e){var t=e.date,n=void 0===t?new Date:t,a=e.mode;return"time"===(void 0===a?"date":a)?r(n.getHours())+":"+r(n.getMinutes()):n.getFullYear()+"-"+r(n.getMonth()+1)+"-"+r(n.getDate())};var r=function(e){return e>9?e:"0"+e}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={data:function(){return{darkmode:!1,theme:"light"}},onLoad:function(){this.initDarkmode()},created:function(){this.initDarkmode()},computed:{isDark:function(){return"dark"===this.theme}},methods:{initDarkmode:function(){if(!this.__init){this.__init=!0;var e=(plus.webview.currentWebview().extras||{}).data||{};this.darkmode=e.darkmode||!1,this.darkmode&&(this.theme=e.theme||"light")}}}};t.default=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r={data:function(){return{safeAreaInsets:{left:0,right:0,top:0,bottom:0}}},onLoad:function(){this.initSafeAreaInsets()},created:function(){this.initSafeAreaInsets()},methods:{initSafeAreaInsets:function(){var e=this;if(!this.__initSafeAreaInsets){this.__initSafeAreaInsets=!0;var t=plus.webview.currentWebview();t.addEventListener("resize",(function(){setTimeout((function(){e.updateSafeAreaInsets(t)}),20)})),this.updateSafeAreaInsets(t)}},updateSafeAreaInsets:function(e){var t=e.getSafeAreaInsets(),n=this.safeAreaInsets;Object.keys(n).forEach((function(e){n[e]=t[e]}))}}};t.default=r}]);