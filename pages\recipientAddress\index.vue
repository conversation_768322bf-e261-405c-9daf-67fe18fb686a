<template>
<view>
	<mescroll-body ref="mescrollRef" @init="mescrollInit" :down="downOption" @down="downCallback" @up="upCallback">
			<view class="list b-b" v-for="(item, index) in dataList" :key="index">
				<view class="wrapper">
					<view class="address-box">
						<text v-if="item.status==1" class="tag">默认</text>
						<text class="address">{{item.address}}</text>
					</view>
					<view class="u-box">
						<text class="name">{{item.receiver}}</text>
						<text class="mobile">{{item.telephone}}</text>
					</view>
				</view>
				<text class="bianji" @click.stop="addAddress('edit', item)"></text>
			</view>
	</mescroll-body>
	<button class="add-btn" @click="addAddress('add')">新增地址</button>
</view>
</template>

<script>
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				downOption: {
					auto: true //是否在初始化后,自动执行downCallback; 默认true
				},
				dataList: []
			}
		},
		onNavigationBarButtonTap(e) {
			if (e.index == 0) {
				uni.navigateTo({
					url: './editAddress'
				});
			}
		},
		onLoad() {
			uni.$on('updateAddress', this.downCallback)
		},
		methods: {
			/*下拉刷新的回调 */
			downCallback() {
				this.dataList=[]
				this.mescroll.resetUpScroll()
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				this.$api.get('api/patient/user/getAddresslist',{
					params:{
						pager: page.num
					}
				}).then(res =>{
					this.mescroll.endSuccess(res.length);
					//设置列表数据
					this.dataList=this.dataList.concat(res);
				}).catch(err =>{
					this.mescroll.endErr();
				})
			},
			selectTap(id) {
				console.log("tap item id:" + JSON.stringify(id));
			},
			editAddess(id) {
				console.log("edit item id:" + id);
			},
			addAddress(type, item){
				uni.navigateTo({
					url: `./editAddress?type=${type}&data=${JSON.stringify(item)}`
				})
			},
			delAddress(val){
				this.$api.post('api/patient/user/delAddress',{
					id:val
				}).then(res =>{
					uni.showToast({
						title:res.msg,
						icon:"none"
					})
				})
			}
		}
	}
</script>

<style lang="scss">
	page{
		.list{
			display: flex;
			align-items: center;
			padding: 20upx 30upx;;
			background: #fff;
			position: relative;
		}
		.wrapper{
			display: flex;
			flex-direction: column;
			flex: 1;
		}
		.address-box{
			display: flex;
			align-items: center;
			.tag{
				font-size: 24upx;
				color: #fa436a;
				margin-right: 10upx;
				background: #fffafb;
				border: 1px solid #ffb4c7;
				border-radius: 4upx;
				padding: 4upx 10upx;
				line-height: 1;
				white-space: nowrap
			}
			.address{
				font-size: 30upx;
				color:#303133;
			}
		}
		.u-box{
			font-size: 28upx;
			color: #909399;
			margin-top: 16upx;
			.name{
				margin-right: 30upx;
			}
		}
		.bianji{
			width: 109upx;
			height: 100%;
			padding: 50upx 0 50upx 58upx;
			box-sizing: border-box;
			background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAAAXNSR0IArs4c6QAABPhJREFUeAHtnE1oE0EUx80HQW39SCvYgBVBqhUU+l1FD1ZFPIgnQdDiRfGmh168eBDR4k1B8OLHSVTQg6gnUQ+CaNLWQGu8lSqtrZQKgk1jkzb1/zSzvKyTIO0mM7uZgc28eftm571fZnaTzGR8y4qkRCJRPTMzswsmG3HULiwsBIqYO3bK5/PNoa1oR0fHG8cuWuBCIyMjyycnJy+hzW6YIPPdr6uru1BfX5+iKj57PTjm6+vrOwz9WchdyIN2mzKWL3R2dl4pVXujo6MrJiYmniHO/bwNQLqFN+cM6fIA9ff3b85ms7dRYS+voFCehqNr4HDWaR8KwaF20F4mHA6vamhomLV6RzQabZufn3+B82GnnVnC9RaWULdg1WJwcpWCYBGC/BcQ4GxB4RWO1TkDLTK8k71O957/gEOxv25sbPxJQhDDyR+Lxe5ALgRnEE4OwG7Y7/enqVIZ0hzaiLW3t791si2CMz4+/hzX3FfkumPBYPCUOB/EDfkgCnuEQuSAMoSjB06+FDo356LnIIaicAKBwN6WlpYvIlbqQSdFQeQAk4C+CzfI70Ln5lzAQUx5TytbTGMEp62tbZjrg4DRiopctwxD6RQMKx4OQfEDDn0ItBKAzba2tsYshYuFpfQcEbYfwnJRyOUpQMrvUjYDNxSdgENxEiDPJafgEBjPAXISjucAOQ3HU4BKAcczgEoFxxOASgnH9YBKDcfVgMoBx7WAygWHAFk/mFHBDYng/M9PFrIvnouJz1UfFMsNh4C6BpAKOK4BpAqOKwCphKM9INVwtAakAxxtAekCR0tAOsHRDpBucLQCpCMcbQDpCocAKf8upgOcoaGh9VgHdRrzgTSRehcTpqMEh5LSrxq0eKmcXzz/hpz/Go/H1wJOP7SXsfTnIvI41irUCSulgKampsihfcIZSS6dDpbYLVqVyWRosdgGcQH0oFrMCx4VZaWA4Mxx4YgkLzmcXJu19rbRk9YJnVJAcKLQDG654AgOBXOlgNCD7kk80wYO+aYUUE1NzSU8OW7Aj18Y97QO8QV+CdxtX4JCjqpKSh/ztEgSgZ9DT+oZGBgIAcyMKhCF2lUKSDiF3kNL7ujQLikdYtrRkDhkAEmgcJUBxGlIZANIAoWrDCBOQyIbQBIoXGUAcRoS2QCSQOEqA4jTkMgGkAQKVxlAnIZENoAkULjKAOI0JLIBJIHCVQYQpyGRDSAJFK4ygDgNiWwASaBwlQHEaUhkA0gChav8uekWS4cZhoqChngDVvA5gTMhGN9sBqsHBwd12p7C5p6zRcDI+1Nz7uo/RCv0r+e8/4nTiVQq1S0MKiDfbo8RTCaEjobYU1FgeW9uPw+m8p6IXSe2IiraAigvgcl7ofCHQqEHUNAMJ0/VKDzBdjmNXOklGZtHhdBTruOw33M/5S2gampq+orAr0mC34YtYj6gJ53nC4okdq5T0eiYnp6mjZUO2Z3HWoGbXPdngyXaiiuZTL5DhX/GozBGL0tCzoiyi3PqOStl/iPG4UgkskNsz0U21g5UWIq2KZ1O09hbL6vsdR3g0Jt/AMPrDY/VGn/Nzc2fcT/aCcOP3KBC5Hn0qhN2OBS7BYgKBKmqqmoXIF1F8c8ucKT3cqJhhWM/NpN7JIvTGmL2k3iCRUD1GNbrHcG5BhwRHP986rTX070MGBnENQ4/E7ghP8SWgI/5Pcfu/2//c4lF4CDN7wAAAABJRU5ErkJggg==) no-repeat 43upx center;
			background-size: 35upx auto;
		}
		
		.add-btn{
			position: fixed;
			left: 30upx;
			right: 30upx;
			bottom: 16upx;
			z-index: 95;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 690upx;
			height: 80upx;
			font-size: 32upx;
			color: #fff;
			background-color:#fa436a;
			border-radius: 10upx;
			box-shadow: 1px 2px 5px rgba(219, 63, 96, 0.4);		
		}
		/*边框*/
		.b-b:after,
		.b-t:after {
			position: absolute;
			z-index: 3;
			left: 0;
			right: 0;
			height: 0;
			content: '';
			transform: scaleY(.5);
			border-bottom: 1px solid #E4E7ED;
		}
		
		.b-b:after {
			bottom: 0;
		}
		
		.b-t:after {
			top: 0;
		}
		
	}
</style>
