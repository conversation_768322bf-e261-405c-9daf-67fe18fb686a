<template>
	<view>
		<view class="form">
			<view class="border_box">
				<view class="input_box">
					<view class="label">身高:</view>
					<view class="inputs"><input type="number" v-model="height" maxlength="5" @input="height = height.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" placeholder="请输入身高" />CM
					</view>
				</view>
				<view class="input_box">
					<view class="label">体重:</view>
					<view class="inputs"><input type="number" v-model="weight" maxlength="5" @input="weight = weight.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" placeholder="请输入体重" />KG
					</view>
				</view>
				<view class="input_box">
					<view class="label">腰围:</view>
					<view class="inputs"><input type="number" v-model="waist" @input="waist = waist.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')" maxlength="5" placeholder="请输入腰围" />CM
					</view>
				</view>
			</view>
			<!-- S 吸烟饮酒史 -->
			<view class="border_box">
				<view class="input_box">
					<view class="label">吸烟史:</view>
					<picker mode="multiSelector" @columnchange="smokePickerChange" :value="smokeIndex"
						:range="smokeArray" @change="changeSmoke">
						<view class="multi_text">
							{{smoke_chose}}
						</view>
					</picker>
				</view>
				<view class="input_box">
					<view class="label">饮酒史:</view>
					<picker mode="multiSelector" @columnchange="drinkPickerChange" :value="drinkIndex"
						:range="drinkArray" @change="changeDrink">
						<view class="multi_text">
							{{drink_chose}}
						</view>
					</picker>
				</view>
			</view>
			<!-- E 吸烟饮酒史 -->
			<!-- S 肝、肾功能 婚姻、生育状态-->
			<view class="border_box">
				<view class="select_box">
					<view class="label">肝功能:</view>
					<view class="select_list">
						<view class="select_item" v-for="item in liver_list" @click="liver = item"
							:class="{'selected':liver==item}">
							{{item}}
						</view>
					</view>
				</view>
				<view class="select_box">
					<view class="label">肾功能:</view>
					<view class="select_list">
						<view class="select_item" v-for="item in renal_list" @click="renal = item"
							:class="{'selected':renal==item}">
							{{item}}
						</view>
					</view>
				</view>
				<view class="select_box">
					<view class="label">婚姻状况:</view>
					<view class="select_list">
						<view class="select_item" v-for="item in marital_status_list" @click="marital_status = item"
							:class="{'selected':marital_status==item}">
							{{item}}
						</view>
					</view>
				</view>
				<view class="select_box">
					<view class="label">生育状态:</view>
					<view class="select_list">
						<view class="select_item" v-for="item in fertility_status_list" @click="fertility_status = item"
							:class="{'selected':fertility_status==item}">
							{{item}}
						</view>
					</view>
				</view>
			</view>
			<!-- E 肝、肾功能 婚姻、生育状态-->
			<!-- S 疾病史 -->
			<view class="border_box">
				<view class="select_box">
					<view class="label">疾病史:</view>
					<view class="select_list">
						<view class="select_item" @click="select_sickness_history(1)"
							:class="{'selected':getSelect('暂无',chose_sickness_history)}">
							暂无
						</view>
						<view class="select_item" v-for="item in sickness_history_list"
							@click="select_sickness_history(2,item)"
							:class="{'selected':getSelect(item,chose_sickness_history)}">
							{{item}}
						</view>
						<view class="select_item" :class="{'selected':other_diseases_disabled==false}"
							@click="select_sickness_history(3)">
							其他
						</view>
					</view>
					<textarea class="supplement" type="text" placeholder="请输入补充内容" maxlength="100" :disabled="other_diseases_disabled"
						v-model="other_sickness_history" auto-height>
					</textarea>
				</view>
			</view>
			<!-- E 疾病史 -->
			<!-- S 家族病史 -->
			<view class="border_box">
				<view class="select_box">
					<view class="label">家族病史:</view>
					<view class="select_list">
						<view class="select_item" @click="select_family_sickness_history(1)"
							:class="{'selected':getSelect('暂无',chose_family_sickness_history)}">
							暂无
						</view>
						<view class="select_item" v-for="item in family_sickness_history_list"
							@click="select_family_sickness_history(2,item)"
							:class="{'selected':getSelect(item,chose_family_sickness_history)}">
							{{item}}
						</view>
						<view class="select_item" @click="select_family_sickness_history(3)"
							:class="{'selected':other_family_sickness_history_disabled==false}">
							其他
						</view>
					</view>
					<textarea class="supplement" type="text" placeholder="请输入补充内容"
						:disabled="other_family_sickness_history_disabled" v-model="other_family_sickness_history" maxlength="100" auto-height>
					</textarea>	
				</view>
			</view>
			<!-- E 家族病史 -->
			<!-- S 药物过敏 -->
			<view class="border_box">
				<view class="select_box">
					<view class="label">药物过敏:</view>
					<view class="select_list">
						<view class="select_item" @click="select_allergy(1)"
							:class="{'selected':getSelect('暂无',chose_allergy)}">
							暂无
						</view>
						<view class="select_item" v-for="item in allergy_list" @click="select_allergy(2,item)"
							:class="{'selected':getSelect(item,chose_allergy)}">
							{{item}}
						</view>
						<view class="select_item" @click="select_allergy(3)"
							:class="{'selected':other_allergy_disabled==false}">
							其他
						</view>
					</view>
					<textarea class="supplement" type="text" placeholder="请输入补充内容" :disabled="other_allergy_disabled"
						v-model="other_allergy" maxlength="100" auto-height>
					</textarea>
				</view>
			</view>
			<!-- E 药物过敏 -->
			<!-- S 其他过敏 -->
			<view class="border_box">
				<view class="select_box">
					<view class="label">食物/接触物过敏:</view>
					<view class="select_list">
						<view class="select_item" @click="select_food_allergy(1)"
							:class="{'selected':getSelect('暂无',chose_food_allergy)}">
							暂无
						</view>
						<view class="select_item" v-for="item in food_allergy_list" @click="select_food_allergy(2,item)"
							:class="{'selected':getSelect(item,chose_food_allergy)}">
							{{item}}
						</view>
						<view class="select_item" @click="select_food_allergy(3)"
							:class="{'selected':other_food_allergy_disabled==false}">
							其他
						</view>
					</view>
					<textarea class="supplement" type="text" placeholder="请输入补充内容" :disabled="other_food_allergy_disabled"
						v-model="other_food_allergy" maxlength="100" auto-height>
					</textarea>
				</view>
			</view>
			<!-- E 其他过敏 -->
			<!-- S 个人习惯 -->
			<view class="border_box">
				<view class="select_box">
					<view class="label">个人习惯:</view>
					<view class="select_list">
						<view class="select_item" @click="select_personal_habit(1)"
							:class="{'selected':getSelect('暂无',chose_personal_habit)}">
							暂无
						</view>
						<view class="select_item" v-for="item in personal_habit" @click="select_personal_habit(2,item)"
							:class="{'selected':getSelect(item,chose_personal_habit)}">
							{{item}}
						</view>
						<view class="select_item" @click="select_personal_habit(3)"
							:class="{'selected':other_personal_habit_disabled==false}">
							其他
						</view>
					</view>
					<textarea class="supplement" type="text" placeholder="请输入补充内容" v-model="other_personal_habit"
						:disabled="other_personal_habit_disabled" maxlength="100" auto-height>
					</textarea>
				</view>
			</view>
			<!-- E 个人习惯 -->
		</view>


	</view>
</template>

<script>
	export default {
		data() {
			return {
				height: '', //身高
				weight: '', //体重
				waist: '', //腰围 
				liver_list: [], //肝功能选择
				liver: '', //肝功能
				renal_list: [], //肾功能选择
				renal: '', //肾功能
				marital_status_list: [], //婚姻状态选择
				marital_status: '', //婚姻状态
				fertility_status_list: [], //生育状态选择
				fertility_status: '', //生育状态
				sickness_history_list: [], //疾病史选择
				chose_sickness_history: [], //疾病史已选项
				other_sickness_history: '', //疾病史补充
				other_diseases_disabled: true, //疾病史是否选择其他补充
				family_sickness_history_list: [], //家族病史选择
				chose_family_sickness_history: [], //家族病史已选项
				other_family_sickness_history: '', //家族病史补充
				other_family_sickness_history_disabled: true, //家族病史是否选择其他补充
				allergy_list: [], //药物过敏选项
				chose_allergy: [], //药物过敏已选项
				other_allergy: '', //药物过敏补充
				other_allergy_disabled: true, //药物过敏是否选择其他补充
				food_allergy_list: [], //食物/接触物过敏选项
				chose_food_allergy: [], //食物/接触物过敏已选项
				other_food_allergy: '', //食物/接触物过敏补充
				other_food_allergy_disabled: true, //食物/接触物过敏是否选择其他选项
				personal_habit: [], //个人习惯选项
				chose_personal_habit: [], //个人习惯已选项
				other_personal_habit: '', //个人习惯补充
				other_personal_habit_disabled: true, //个人习惯是否选择其他
				smokeArray: [
					['是', '否'],
					['1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年以上'],
					['1根', '2根', '3根', '4根', '5-10根', '10-15根', '15-20根', '20根以上']
				], //吸烟历史选项
				smokeIndex: [0, 0, 0],
				drinkArray: [
					['是', '否'],
					['1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年以上'],
					['白酒', '啤酒', '红酒', '各种酒类均有'],
					['少量', '适量', '大量']
				], //饮酒史选项
				drinkIndex: [0, 0, 0, 0],
				smoke_chose: '请选择吸烟史',
				drink_chose: '请选择饮酒史',
			};

		},
		created() {
			this.getconfiglist()
		},
		methods: {
			// 吸烟史选择
			smokePickerChange(e) {
				console.log(e)
				if (e.detail.column == 0) {
					if (e.detail.value == 0) {
						this.smokeArray[1] = ['1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年以上']
						this.smokeArray[2] = ['1根', '2根', '3根', '4根', '5-10根', '10-15根', '15-20根', '20根以上']
					} else {
						this.smokeArray[1] = ['无']
						this.smokeArray[2] = ['无']
					}
					this.$forceUpdate();
				}

			},
			//吸烟史选择保存
			changeSmoke(e) {
				console.log(e)
				if (e.target.value[0] == 1) {
					this.smoke_chose = '无吸烟史',
						this.smokeArray[1] = ['1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年以上']
					this.smokeArray[2] = ['1根', '2根', '3根', '4根', '5-10根', '10-15根', '15-20根', '20根以上']
				} else {
					this.smoke_chose = '吸烟;' + this.smokeArray[1][e.target.value[1]] + ';' + this.smokeArray[2][e.target
						.value[2]
					]
				}
			},
			// 饮酒史选择
			drinkPickerChange(e) {
				if (e.detail.column == 0) {
					if (e.detail.value == 0) {
						this.drinkArray[1] = ['1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年以上']
						this.drinkArray[2] = ['白酒', '啤酒', '红酒', '以上均有']
						this.drinkArray[3] = ['少量', '适量', '大量']
					} else {
						this.drinkArray[1] = ['无']
						this.drinkArray[2] = ['无']
						this.drinkArray[3] = ['无']
					}
					this.$forceUpdate();
				}
			},
			// 饮酒史选择保存
			changeDrink(e) {
				if (e.target.value[0] == 1) {
					this.drink_chose = '无饮酒史',
						this.drinkArray[1] = ['1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年以上']
					this.drinkArray[2] = ['白酒', '啤酒', '红酒', '各种酒类均有']
					this.drinkArray[3] = ['少量', '适量', '大量']
				} else {
					this.drink_chose = '饮酒;' + this.drinkArray[1][e.target.value[1]] + ';' + this.drinkArray[2][e.target
						.value[2]
					] + ';' + this.drinkArray[3][e.target.value[3]]
				}
			},
			// 选项是否被选中
			getSelect(item, list) {
				if (list.indexOf(item) == -1) {
					return false
				} else {
					return true
				}
			},
			//选择个人病史
			select_sickness_history(type, data) {
				if (type == 1) {
					if (this.chose_sickness_history.length == 0 && this.other_diseases_disabled == true) {
						this.chose_sickness_history.push('暂无')
						this.other_diseases_disabled = true
					} else if (this.chose_sickness_history.indexOf('暂无') != -1) {
						this.chose_sickness_history = []
					}
				} else if (type == 2) {
					if (this.chose_sickness_history.indexOf('暂无')!=-1) {
						this.chose_sickness_history.splice(this.chose_sickness_history.indexOf('暂无'),1)
					}
					if (this.chose_sickness_history.indexOf(data) == -1) {
						this.chose_sickness_history.push(data)
					} else {
						this.chose_sickness_history.splice(this.chose_sickness_history.indexOf(data), 1)
					}
				} else if (type == 3) {
					if (this.chose_sickness_history.indexOf('暂无') != -1) {
						this.chose_sickness_history.splice(this.chose_sickness_history.indexOf('暂无'),1)
					}
					if (this.other_diseases_disabled == true) {
						this.other_diseases_disabled = false
					} else {
						this.other_diseases_disabled = true
						this.other_sickness_history = ''
					}
				}
			},
			//选择家族病史
			select_family_sickness_history(type, data) {
				if (type == 1) {
					if (this.chose_family_sickness_history.length == 0 && this.other_family_sickness_history_disabled ==
						true) {
						this.chose_family_sickness_history.push('暂无')
						this.other_family_sickness_history_disabled = true
					} else if (this.chose_family_sickness_history.indexOf('暂无') != -1) {
						this.chose_family_sickness_history = []
					}
				} else if (type == 2) {
					if (this.chose_family_sickness_history.indexOf('暂无') != -1) {
						this.chose_family_sickness_history.splice(this.chose_family_sickness_history.indexOf('暂无'),1)
					}
					if (this.chose_family_sickness_history.indexOf(data) == -1) {
						this.chose_family_sickness_history.push(data)
					} else {
						this.chose_family_sickness_history.splice(this.chose_family_sickness_history.indexOf(data), 1)
					}
				} else if (type == 3) {
					if (this.chose_family_sickness_history.indexOf('暂无') != -1) {
						this.chose_family_sickness_history.splice(this.chose_family_sickness_history.indexOf('暂无'),1)
					}
					if (this.other_family_sickness_history_disabled == true) {
						this.other_family_sickness_history_disabled = false
					} else {
						this.other_family_sickness_history_disabled = true
						this.other_family_sickness_history = ''
					}
				}
			},
			//选择药物过敏史
			select_allergy(type, data) {
				if (type == 1) {
					if (this.chose_allergy.length == 0 && this.other_allergy_disabled == true) {
						this.chose_allergy.push('暂无')
						this.other_allergy_disabled = true
					} else if (this.chose_allergy.indexOf('暂无') != -1) {
						this.chose_allergy = []
					}
				} else if (type == 2) {
					if (this.chose_allergy.indexOf('暂无') != -1) {
						this.chose_allergy.splice(this.chose_allergy.indexOf('暂无'),1)
					}
					if (this.chose_allergy.indexOf(data) == -1) {
						this.chose_allergy.push(data)
					} else {
						this.chose_allergy.splice(this.chose_allergy.indexOf(data), 1)
					}
				} else if (type == 3) {
					if (this.chose_allergy.indexOf('暂无') != -1) {
						this.chose_allergy.splice(this.chose_allergy.indexOf('暂无'),1)
					}
					if (this.other_allergy_disabled == true) {
						this.other_allergy_disabled = false
					} else {
						this.other_allergy_disabled = true
						this.other_allergy = ''
					}
				}
			},
			//选择食物/接触物过敏史
			select_food_allergy(type, data) {
				if (type == 1) {
					if (this.chose_food_allergy.length == 0 && this.other_food_allergy_disabled == true) {
						this.chose_food_allergy.push('暂无')
						this.other_food_allergy_disabled = true
					} else if (this.chose_food_allergy.indexOf('暂无') != -1) {
						this.chose_food_allergy = []
					}
				} else if (type == 2) {
					if (this.chose_food_allergy.indexOf('暂无') != -1) {
						this.chose_food_allergy.splice(this.chose_food_allergy.indexOf('暂无'),1)
					}
					if (this.chose_food_allergy.indexOf(data) == -1) {
						this.chose_food_allergy.push(data)
					} else {
						this.chose_food_allergy.splice(this.chose_food_allergy.indexOf(data), 1)
					}
				} else if (type == 3) {
					if (this.chose_food_allergy.indexOf('暂无') != -1) {
						this.chose_food_allergy.splice(this.chose_food_allergy.indexOf('暂无'),1)
					}
					if (this.other_food_allergy_disabled == true) {
						this.other_food_allergy_disabled = false
					} else {
						this.other_food_allergy_disabled = true
						this.other_food_allergy = ''
					}
				}
			},
			//选择个人习惯
			select_personal_habit(type, data) {
				if (type == 1) {
					if (this.chose_personal_habit.length == 0 && this.other_personal_habit_disabled == true) {
						this.chose_personal_habit.push('暂无')
						this.other_personal_habit_disabled = true
					} else if (this.chose_personal_habit.indexOf('暂无') != -1) {
						this.chose_personal_habit = []
					}
				} else if (type == 2) {
					if (this.chose_personal_habit.indexOf('暂无') != -1) {
						this.chose_personal_habit.splice(this.chose_personal_habit.indexOf('暂无'),1)
					}
					if (this.chose_personal_habit.indexOf(data) == -1) {
						this.chose_personal_habit.push(data)
					} else {
						this.chose_personal_habit.splice(this.chose_personal_habit.indexOf(data), 1)
					}
				} else if (type == 3) {
					if (this.chose_personal_habit.indexOf('暂无') != -1) {
						this.chose_personal_habit.splice(this.chose_personal_habit.indexOf('暂无'),1)
					}
					if (this.other_personal_habit_disabled == true) {
						this.other_personal_habit_disabled = false
					} else {
						this.other_personal_habit_disabled = true
						this.other_personal_habit = ''
					}
				}
			},
			//保存
			onNavigationBarButtonTap(e) {
				if (e.index == 0) {
					let smoke_history = {
						is_smoke: '',
						smoke_age: '',
						smoke_num: '',
					}
					if (this.smoke_chose.indexOf(';') != -1) {
						smoke_history = {
							is_smoke: this.smoke_chose.split(';')[0],
							smoke_age: this.smoke_chose.split(';')[1],
							smoke_num: this.smoke_chose.split(';')[2]
						}
					} else if (this.smoke_chose == '无吸烟史') {
						smoke_history = {
							is_smoke: '否',
							smoke_age: '无',
							smoke_num: '无',
						}
					}
					let drinking_history = {
						is_drinking: '',
						drinking_age: '',
						drinking_type: '',
						drinking_capacity: ''
					}
					if (this.drink_chose.indexOf(';') != -1) {
						drinking_history = {
							is_drinking: this.drink_chose.split(';')[0],
							drinking_age: this.drink_chose.split(';')[1],
							drinking_type: this.drink_chose.split(';')[2],
							drinking_capacity: this.drink_chose.split(';')[3]
						}
					} else if (this.drink_chose == '无饮酒史') {
						drinking_history = {
							is_drinking: '否',
							drinking_age: '无',
							drinking_type: '无',
							drinking_capacity: '无',
						}
					}
					let params = {
						height: this.height,
						weight: this.weight,
						waist: this.waist,
						smoke_history: smoke_history,
						drinking_history: drinking_history,
						liver: this.liver,
						renal: this.renal,
						marital_status: this.marital_status,
						fertility_status: this.fertility_status,
						sickness_history: this.getJoint(this.chose_sickness_history, this.other_sickness_history),
						family_sickness_history: this.getJoint(this.chose_family_sickness_history, this
							.other_family_sickness_history),
						drug_allergy: this.getJoint(this.chose_allergy, this.other_allergy),
						other_allergy: this.getJoint(this.chose_food_allergy, this.other_food_allergy),
						habit: this.getJoint(this.chose_personal_habit, this.other_personal_habit)

					}
					this.$api.post('api/patient/records/setinfo', params, {
						header: {
							'content-type': 'application/json'
						}
					}).then(res => {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					})

				}
			},
			//选项拼接
			getJoint(list, other) {
				let index = list.indexOf(other)
				if(index!=-1){
					list.splice(index,1)
				}
				let string = ''
				list.map(item => {
					string = string + item + ','
				})
				string = string + other
				if (string.charAt(0) == ',') {
					string = string.slice(1)
				}
				if (string.charAt(string.length - 1) == ',') {
					string = string.slice(0, -1)
				}
				return string
			},
			getCaseInfo() {
				this.$api.get('api/patient/records/getinfo').then(res => {
					if (res.success) {
						this.height = res.data.height
						this.weight = res.data.weight
						this.waist = res.data.waist
						this.liver = res.data.liver
						this.renal = res.data.renal
						this.marital_status = res.data.marital_status
						this.fertility_status = res.data.fertility_status
						if (res.data.smoke_history.is_smoke) {
							if(res.data.smoke_history.is_smoke=='否'){
								this.smoke_chose = "无吸烟史"
							}else{
								this.smoke_chose = res.data.smoke_history.is_smoke + ';' + res.data.smoke_history
									.smoke_age + ';' + res.data.smoke_history.smoke_num
							}
						}
						if (res.data.drinking_history.is_drinking) {
							if(res.data.drinking_history.is_drinking=='否'){
								this.drink_chose = '无饮酒史'
							}else{
								this.drink_chose = res.data.drinking_history.is_drinking + ';' + res.data
									.drinking_history.drinking_age + ';' + res.data.drinking_history.drinking_type +
									';' + res.data.drinking_history.drinking_capacity
							}
						}
						
						if (res.data.sickness_history) {
							if (res.data.sickness_history == '暂无') {
								this.chose_sickness_history.push('暂无')
							} else {
								let data = this.reductionlist(res.data.sickness_history, this.sickness_history_list)
								this.chose_sickness_history = data.list
								this.other_sickness_history =data.other
								if(data.other){
									this.other_diseases_disabled = false
								}
							}
						}
						if(res.data.family_sickness_history){
							if(res.data.family_sickness_history=='暂无'){
								this.chose_family_sickness_history.push('暂无')
							}else{
								let data = this.reductionlist(res.data.family_sickness_history,this.family_sickness_history_list)

								this.chose_family_sickness_history = data.list
								this.other_family_sickness_history = data.other
								if(data.other){
									this.other_family_sickness_history_disabled = false
								}
							}
						}
						if(res.data.drug_allergy){
							if(res.data.drug_allergy=='暂无'){
								this.chose_allergy.push('暂无')
							}else{
								let data = this.reductionlist(res.data.drug_allergy,this.allergy_list)
								this.chose_allergy = data.list
								this.other_allergy = data.other
								if(data.other){
									this.other_allergy_disabled = false
								}
							}
						}
						if(res.data.other_allergy){
							if(res.data.other_allergy=='暂无'){
								this.chose_food_allergy.push('暂无')
							}else{
								let data = this.reductionlist(res.data.other_allergy,this.food_allergy_list)
								this.chose_food_allergy = data.list
								this.other_food_allergy = data.other
								if(data.other){
									this.other_food_allergy_disabled = false
								}
							}
						}
						if(res.data.habit){
							if(res.data.habit=='暂无'){
								this.chose_food_allergy.push('暂无')
							}else{
								let data = this.reductionlist(res.data.habit,this.personal_habit)
								this.chose_personal_habit = data.list
								this.other_personal_habit = data.other
								if(data.other){
									this.other_personal_habit_disabled = false
								}
							}
						}
					}
				})
			},
			reductionlist(string, list) {
				let splitlist = string.split(',')
				let other = ''
				splitlist.map(item => {
					if (list.indexOf(item) == -1) {
						splitlist.splice(splitlist.indexOf(item), 1)
						other = item
					}
				})
				return {
					list: splitlist,
					other: other
				}
			},
			// 获取选项
			getconfiglist(){
				this.$api.get('api/patient/records/getconfiglist').then(res=>{
					if(res.success){
						this.liver_list = res.data.liver_list
						this.renal_list = res.data.renal_list
						this.marital_status_list = res.data.marital_status_list
						this.fertility_status_list = res.data.fertility_status_list
						this.sickness_history_list = res.data.sickness_history_list
						this.allergy_list = res.data.allergy_list
						this.food_allergy_list = res.data.food_allergy_list
						this.personal_habit = res.data.personal_habit
						this.getCaseInfo()
					}else{
						uni.showToast({
							title:res.msg,
							icon:'none'
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: #f7f7f7;
	}

	.form {
		padding-top: 36rpx;
		width: 690rpx;
		margin: 0 auto;

		.border_box {
			border-radius: 16rpx;
			background-color: #fff;
			box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.05);
			padding: 30rpx 20rpx;
			box-sizing: border-box;
			margin-bottom: 20rpx;
		}

		.input_box {
			height: 80rpx;
			margin: 0 auto 36rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			border-bottom: solid 1rpx #e1e1e1;

			.label {
				width: 150rpx;
				font-size: 28rpx;
				color: #999999;
			}

			.inputs {
				width: calc(100% - 150rpx);
				border: none;
				display: flex;
				align-items: center;

				input {
					width: 100%;
					height: 80rpx;
					font-size: 28rpx;
					padding: 0 20rpx;
					box-sizing: border-box;
				}

				.inputchecks {
					position: relative;

					.checklist {
						position: absolute;
						width: 100%;
						left: -10rpx;
						top: 90rpx;
						background-color: #fff;
						box-shadow: 0px 0px 18px 0px rgba(0, 0, 0, 0.1);
						border-radius: 9rpx;
						z-index: 99;
						height: 500rpx;
						overflow-y: scroll;

						.check_item {
							width: 100%;
							padding: 20rpx;
							border-bottom: solid 1rpx #e1e1e1;
							font-size: 28rpx;
							color: #333333;
							box-sizing: border-box;
						}
					}
				}
			}
		}

		.select_box {
			margin: 0 auto 36rpx;
			border-bottom: solid 1rpx #e1e1e1;
			padding-bottom: 20rpx;

			.label {
				font-size: 28rpx;
				color: #999999;
			}

			.select_list {
				display: flex;
				flex-wrap: wrap;

				.select_item {
					padding: 0 20rpx;
					line-height: 60rpx;
					border-radius: 35rpx;
					font-size: 28rpx;
					margin-right: 20rpx;
					margin-top: 15rpx;
					background-color: #e1e1e1;
					color: #888888;
				}

				.selected {
					background-color: $jian-bg-color;
					color: #fff;
				}
			}

			.supplement {
				font-size: 28rpx;
				margin-top: 20rpx;
			}
		}

		.multi_text {
			font-size: 28rpx;
		}
	}
</style>