.content {
	display: flex;
	flex-direction: column;
	justify-content:center;
	/* margin-top: 128upx; */
}

/* 头部 logo */ 
.header {
	width:161upx;
	height:161upx;
	box-shadow:0upx 0upx 60upx 0upx rgba(0,0,0,0.1);
	border-radius:50%;
	background-color: #000000; 
	margin-top: 128upx;
	margin-bottom: 72upx;
	margin-left: auto;
	margin-right: auto;
}
.header image{
	width:161upx;
	height:161upx;
	border-radius:50%;
}

/* 主体 */
.main {
	display: flex;
	flex-direction: column;
	padding-left: 70upx;
	padding-right: 70upx;
	justify-content: center;
}
.tips {
	color: #999999;
	font-size: 28upx;
	margin-top: 64upx;
	margin-left: 48upx;
}
.clink {
	color: blue;
}
.textActive {
	text-align: center;
}
.mainTop {
	margin-top: 24upx;
	
}
/* 登录按钮 */
.wbutton{
	margin-top: 28upx;
}

/* 其他登录方式 */
.other_login{
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	margin-top: 256upx;
	text-align: center;
}
.login_icon{
	border: none;
	font-size: 64upx;
	margin: 0 64upx 0 64upx;
	color: rgba(0,0,0,0.7)
}
.wechat_color{
	color: #83DC42;
}
.weibo_color{
	color: #F9221D;
}
.github_color{
	color: #24292E;
}

/* 底部 */
.footer{
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	font-size: 26upx;
	margin-top: 64upx;
	color: rgba(0,0,0,0.7);
	text-align: center;
	height: 40upx;
	line-height: 40upx;
}
.agreement {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	font-size: 28upx;
	margin-top: 64upx;
	color: rgba(0,0,0,0.7);
	text-align: center;
	height: 40upx;
	line-height: 40upx;
	position: absolute;
	bottom: 40upx;
	left: 50%;
	width:100%;
	transform: translate(-50%);
}
.footer text{
	font-size: 24upx;
	margin-left: 15upx;
	margin-right: 15upx;
}
