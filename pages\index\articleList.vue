<template>
	<view class="article">
		<view class="header space-between">
			<view class="space-between">
				<text class="rectangle"></text>
				<text style="color: #333333;font-size: 16px;">健康百科</text>
			</view>
			<text style="color: #999999;font-size: 13px;">查看更多>></text>
		</view>
		<view v-for="n in article" class="flex-start list">
			<image :src="n.img" mode=""></image>
			<view class="desc">
				<text class="title">{{n.title}}</text>
				<text class="gai">{{n.desc}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				article:[
					{
						img:"/static/img/avatar.png",
						title:"简单4招轻松应对焦虑问题",
						desc:"根据你对自觉症状及患病原因的表述，多考虑是神经性头痛引起的。神经性头痛不是脑内问题"
					},
					{
						img:"/static/img/avatar.png",
						title:"简单4招轻松应对焦虑问题",
						desc:"根据你对自觉症状及患病原因的表述，多考虑是神经性头痛引起的。神经性头痛不是脑内问题"
					},
					{
						img:"/static/img/avatar.png",
						title:"简单4招轻松应对焦虑问题",
						desc:"根据你对自觉症状及患病原因的表述，多考虑是神经性头痛引起的。神经性头痛不是脑内问题"
					}
				]
			};
		}
	}
</script>

<style lang="scss">
.article{
	padding:0px $uni-spacing-row-sm;
}
.rectangle {
	display: inline-block;
	width: 4px;
	height: 18px;
	background-color: #16cc9f;
	margin-right: 20rpx;
}
.list{
	height: $jian-list-height-sm;
	border-bottom: 1px solid $uni-border-color;
	margin: $uni-spacing-col-base 0px;
	padding: $uni-spacing-col-base 0px;
}
.flex-start{
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
}
.header{
	height: 60px;
}
.space-between {
	display: flex;
	justify-content: space-between;
	align-items: center;
	color:$uni-text-color;
}
.desc {
	width: calc(100% - 100px);
	position: relative;
	height: 70px;
	.title{
		color: $uni-color-title;
		font-size: $uni-font-size-lg;
	}
}
.gai{
	font-size: $uni-font-size-sm;
	color: $uni-text-color-grey;
	font-size: 12px;
	line-height: 18px;
	text-align: left;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	margin-top: $uni-spacing-col-sm
}
image{
	width: 85px;
	height: 70px;
	margin-right: $uni-spacing-row-sm;
	border-radius: $uni-border-radius-lg;
}
</style>
