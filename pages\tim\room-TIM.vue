<template>
	<view>
		<view class="content" @touchstart="hideDrawer">
			<scroll-view
				class="msg-list"
				:class="{'listheight':currInq.status==2}"
				scroll-y="true"
				:scroll-with-animation="scrollAnimation"
				:scroll-top="scrollTop"
				:scroll-into-view="scrollToView"
				@scrolltoupper="loadHistory"
				upper-threshold="50"
			>
				<!-- 加载历史数据waitingUI -->
				<view class="loading" v-show="isHistoryLoading">
					<view class="spinner">
						<view class="rect1"></view>
						<view class="rect2"></view>
						<view class="rect3"></view>
						<view class="rect4"></view>
						<view class="rect5"></view>
					</view>
				</view>
				<view class="row" v-for="(item, index) in msgList" :key="index" :id="item.ID">
					<!-- 用户消息 -->
					<block>
						<!-- 自己发出的消息 -->
						<view class="my" v-if="item.flow == 'out'">
							<!-- 左-消息 -->
							<view class="left">
								<!-- 文字消息 -->
								<view v-if="item.type == TIM.TYPES.MSG_TEXT" class="bubble">
								<text-element :payload="item.payload"></text-element>
								</view>
								<!-- 语音消息 -->
								<view v-if="item.payload.extension=='voice'" class="bubble voice" @tap="playVoice(item)" :class="playMsgid == item.ID?'play':''">
									<view class="icon other-voice"></view>
									<view class="length">{{item.payload.description}}</view>
								</view>
								<!-- 图片消息 -->
								<view v-if="item.type == TIM.TYPES.MSG_IMAGE" class="bubble img">
									<image :src="item.payload.imageInfoArray[0].imageUrl"  mode="" @tap="showPic(item.payload.imageInfoArray[0].imageUrl)"></image>
								</view>
								<!-- 健康档案消息 -->
								<view v-if="item.payload.extension=='health_file'" class="bubble custom" style="background-color: #f8f8f8;">
									<image class="health" v-if="item.payload.extension=='health_file'" src="../inquiry/img/health.png"  mode="" @tap="toHealth(item.payload)"></image>
								</view>
							</view>
							<!-- 右-头像 -->
							<view class="right"><image :src="userInfo.img"></image></view>
						</view>
						
						<!-- 别人发出的消息 -->
						<view class="other" v-else>
							<!-- 左-头像 -->
							<view class="left"><image :src="toUserInfo.img"></image></view>
							<!-- 右-用户名称-时间-消息 -->
							<view class="right">
								<view class="username">
									<view class="name">{{ toUserInfo.user }}</view>
									<view class="time">{{ timeFliter(item.time) }}</view>
								</view>

								<!-- 文字消息 -->
								<view v-if="item.type == TIM.TYPES.MSG_TEXT" class="bubble"><text-element :payload="item.payload"></text-element></view>
								<!-- 语音消息 -->
								<view v-if="item.payload.extension=='voice'" class="bubble voice" @tap="playVoice(item)" :class="playMsgid == item.ID?'play':''">
									<view class="icon other-voice"></view>
									<view class="length">{{item.payload.description}}</view>
								</view>
								<!-- 图片消息 -->
								<view v-if="item.type == TIM.TYPES.MSG_IMAGE" class="bubble img">
									<image :src="item.payload.imageInfoArray[0].imageUrl"  mode="" @tap="showPic(item.payload.imageInfoArray[0].imageUrl)"></image>
								</view>
								<!-- 健康档案消息 -->
								<view v-if="item.payload.extension=='health_file'" class="bubble custom" style="background-color: #f8f8f8;">
									<image class="health" v-if="item.payload.extension=='health_file'" src="../inquiry/img/health.png"  mode="" @tap="toHealth(item.payload)"></image>
								</view>
							</view>
						</view>
					</block>
				</view>
			</scroll-view>
		</view>
		<!-- 抽屉栏 -->
		<view class="popup-layer" :class="popupLayerClass" @touchmove.stop.prevent="discard">
			<!-- 表情 -->
			<swiper class="emoji-swiper" :class="{ hidden: hideEmoji }" indicator-dots="true" duration="150">
				<swiper-item v-for="(page, pid) in emojiNamepage" :key="pid">
					<view v-for="(em, eid) in page" :key="eid" @tap="addEmoji(em)"><image mode="widthFix" :src="emojiUrl + emojiMap[em]"></image></view>
				</swiper-item>
			</swiper>
			<!-- 更多功能 相册-拍照 -->
			<view class="more-layer" :class="{ hidden: hideMore }">
				<view class="list">
					<view class="box" @tap="chooseImage">
						<view class="imagesText">
							<view class="img_box">
								<view>
									<image src="../../static/img/chat-icon/icon2.png" mode="widthFix" class="imagesSize"></image>
								</view>
							</view>
							<text>打开图库</text>
						</view>
					</view>
					<view class="box" @tap="camera">
						<view class="imagesText">
							<view class="img_box">
								<view>
									<image src="../../static/img/chat-icon/icon3.png" mode="widthFix" class="imagesSize"></image>
								</view>
							</view>
							<text>打开相机</text>
						</view>
					</view>
					<view class="box" @tap="toHealth">
						<view class="imagesText">
							<view class="img_box">
								<view>
									<image src="../../static/img/chat-icon/icon6.png" mode="widthFix" class="imagesSize"></image>
								</view>
							</view>
							<text>发送档案</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 快捷消息 -->
			<view class="quickMsg" :class="{ hidden: hideQuick }">
				<scroll-view scroll-y="true" class="msg_list">
					<view class="msg_item" v-for="n in quickList" @tap="sendQuick(n)">
						{{n.text}}
					</view>
				</scroll-view>
			</view>
		</view>
		<!-- 底部输入栏 -->
		<view v-show="currInq.status==1||toUserId == 'xxjk_admin'" class="input-box" :class="popupLayerClass" @touchmove.stop.prevent="discard">
			<!-- H5下不能录音，输入栏布局改动一下 -->
			<!-- #ifndef H5 -->
			<view class="voice"><view class="icon" :class="isVoice ? 'jianpan' : 'yuyin'" @tap="switchVoice"></view></view>
			<!-- #endif -->
			<!-- #ifdef H5 -->
			<view class="more" @tap="showMore"><view class="icon add"></view></view>
			<!-- #endif -->
			<view class="textbox">
				<view
					class="voice-mode"
					:class="[isVoice ? '' : 'hidden', recording ? 'recording' : '']"
					@touchstart="voiceBegin"
					@touchmove.stop.prevent="voiceIng"
					@touchend="voiceEnd"
					@touchcancel="voiceCancel"
				>
					{{ voiceTis }}
				</view>
				<view class="text-mode" :class="isVoice ? 'hidden' : ''">
					<view class="box"><textarea auto-height="true" fixed="true" cursor-spacing="20" v-model="textMsg" @focus="textareaFocus" /></view>
					<view class="em" @tap="chooseEmoji"><view class="icon biaoqing"></view></view>
				</view>
			</view>
			<!-- #ifndef H5 -->
			<view class="more" @tap="showMore"><view class="icon add"></view></view>
			<!-- #endif -->
			<view class="send" :class="isVoice ? 'hidden' : ''" @tap="sendText"><view class="btn">发送</view></view>
		</view>
		<view v-show="currInq.status==2" class="endtip">
			<view class="tip">温馨提示，问诊时间已结束，如需继续联系与该医生，请再次购买</view>
			<view class="flex-end-center">
				<!-- <navigator :url="'../doctors/doctorsDetails?id='+currInq.doctid">
					<button type="default" class="btn">立即评价</button>
				</navigator> -->
				<navigator :url="'../doctors/doctorsDetails?id='+currInq.doctid">
				<button type="default" class="btn">再次购买</button>
				</navigator>
			</view>
		</view>
		<!-- 录音UI效果 -->
		<view class="record" :class="recording ? '' : 'hidden'">
			<view class="ing" :class="willStop ? 'hidden' : ''"><view class="icon luyin2"></view></view>
			<view class="cancel" :class="willStop ? '' : 'hidden'"><view class="icon chehui"></view></view>
			<view class="tis" :class="willStop ? 'change' : ''">{{ recordTis }}</view>
		</view>
		<PSM v-if = 'showpsm' :tipwords='tipwords' :storagename = 'storagename'></PSM>
	</view>
</template>
<script>
import { mapState } from 'vuex';
import moment  from 'moment'
import {emojiName, emojiMap, emojiUrl}  from '../../common/commen.js'
import TextElement from './text-element.vue'
import PSM from "../../components/prompt-save-msg/prompt-save-msg.vue"
export default {
	components:{
		TextElement,
		PSM
	},
	data() {
		return {
			//TIM变量
			conversationActive: null,
			toUserId: '',
			toUserInfo: null,
			userInfo: null,
			nextReqMessageID: '',
			count: 15,
			isCompleted: '',
			msgList: [],
			TIM: null,
	 		currInq:null,//当前问诊
			//文字消息
			textMsg: '',
			//消息列表
			isHistoryLoading: false,
			scrollAnimation: false,
			scrollTop: 0,
			scrollToView: '',

			msgImgList: [],
			myuid: 0,

			//录音相关参数
			// #ifndef H5
			//H5不能录音
			RECORDER:uni.getRecorderManager(),
			// #endif
			isVoice: false,
			voiceTis: '按住 说话',
			recordTis: '手指上滑 取消发送',
			recording: false,
			willStop: false,
			initPoint: { identifier: 0, Y: 0 },
			recordTimer: null,
			recordLength: 0,

			//播放语音相关参数
			AUDIO: uni.createInnerAudioContext(),
			// 录音部分参数
			recordOptions:{
			  duration: 60000, // 录音的时长，单位 ms，最大值 600000（10 分钟）
			  sampleRate: 44100, // 采样率
			  numberOfChannels: 1, // 录音通道数
			  encodeBitRate: 192000, // 编码码率
			  format: 'mp3' // 音频格式，选择此格式创建的音频消息，可以在即时通信 IM 全平台（Android、iOS、微信小程序和Web）互通
			},
			playMsgid: null,
			VoiceTimer: null,
			// 抽屉参数
			popupLayerClass: '',
			// more参数
			hideMore: true,
			//表情定义
			hideEmoji: true,
			emojiList: this.$commen.emojiList,
			emojiUrl:emojiUrl,
			emojiMap: emojiMap,
			emojiName:emojiName,
			emojiNamepage: this.$commen.getemojilist(),
			// 提示语
			tipwords:'您的聊天消息记录只能在服务器保存七天，请将重要数据保存于病历或本地！！！',
			// 存储名称
			storagename:'message_save_7days_promot',
			showpsm:true
		};
	},
	onNavigationBarButtonTap(e) {
		if (e.index == 0) {
			uni.navigateTo({
				url: './set'
			});
		}
	},
	mounted() {
		this.scrollTop = this.msgList.length*100
	},
	computed: {
		...mapState({
			currentMessageList: state => state.currentMessageList
		})
	},
	watch: {
		currentMessageList(newVal, oldVal) {
			this.msgList = newVal;
			this.msgImgList = []
			this.msgList.forEach(n =>{
				if(n.type == this.TIM.TYPES.MSG_IMAGE){
					this.msgImgList.push(n.payload.imageInfoArray[0].imageUrl)
				}
			})
			
			// console.log(this.msgImgList)
			this.screenMsg(newVal, oldVal);
		}
	},
	created(){
		let showpop = uni.getStorageSync(this.storagename)
		if(showpop){
			this.showpsm = false
		}
	},
	onLoad(option) {
		// this.userInfo = JSON.parse(uni.getStorageSync('userInfo'));
		let globaluserInfo= this.$store.state.userInfo
		let obj = {
			user:globaluserInfo.patname,
			userId:"user_"+globaluserInfo.username,
			img:globaluserInfo.face,
			userSig:"",
		}
		this.userInfo = obj
		this.toUserId = this.$store.state.toUserId;
		this.conversationActive = this.$store.state.conversationActive;
		this.TIM = this.$TIM;
		//获取聊天对象的用户信息
		// console.log(option)
		let tmp = JSON.parse(decodeURIComponent(option.toUserInfo))
		// console.log(tmp)
		this.currInq = tmp
		let obj2 = {
			user:tmp.doctname,
			userId: tmp.username == 'xxjk_admin' ? 'xxjk_admin' : 'user_' + tmp.username,
			img: tmp.face?this.$baseUrl+tmp.face:"/static/img/default.png"
		}
		if(obj2.userId =='xxjk_admin'){
			uni.setNavigationBarTitle({
			    title: '小欣客服'
			});
		}else{
			uni.setNavigationBarTitle({
			    title: obj2.user
			});
		}
		this.toUserInfo =obj2
		this.getMsgList();
		//语音自然播放结束
		this.AUDIO.onEnded(res => {
			this.playMsgid = null;
		});
		// #ifndef H5
		//录音开始事件
		this.RECORDER.onStart(e => {
			this.recordBegin(e);
		});
		//录音结束事件
		this.RECORDER.onStop(e => {
			this.recordEnd(e);
		});
		// #endif
		// console.log(this.toUserId)
	},
	onShow() {
		uni.getStorage({
			key: 'healthData',
			success:  (res)=>{
				// console.log(res)
				uni.removeStorage({key: 'healthData'})
				this.sendMsg({data:res.data},"custom")
			}
		});
		this.scrollTop = this.msgList.length*100
	},
	onUnload() {
		//退出页面 将所有的会话内的消息设置为已读
		let promise = this.tim.setMessageRead({ conversationID: this.conversationActive.conversationID });
		promise&&promise
			.then(function(imResponse) {
				console.log('全部已读');
				// 已读上报成功
			})
			.catch(function(imError) {
				// 已读上报失败
				console.warn('setMessageRead error:', imError);
			});
	},
	methods: {
		//时间过滤
		timeFliter(timer) {
			let timeData = new Date(timer * 1000);
			let str = this.$commen.dateTimeFliter(timeData);
			return str;
		},
		// 接受消息(定位消息)
		screenMsg(newVal, oldVal) {
			if (newVal.length>0 && oldVal.length>0) {
				if (newVal[0].ID != oldVal[0].ID && newVal.length >= this.count) {
					this.$nextTick(() => {
						this.scrollToView = oldVal[0].ID;
					});
				} else {
					this.$nextTick(() => {
						// console.log(newVal)
						this.scrollToView = newVal[newVal.length - 1].ID;
					});
				}
			} else {
				this.$nextTick(() => {
					if(newVal.length>0){
						this.scrollToView = newVal[newVal.length - 1].ID;
					}
				});
			}
		},
		//触发滑动到顶部(加载历史信息记录)
		loadHistory(e) {
			// 更多消息列表
			if (this.isHistoryLoading) {
				return;
			}
			this.isHistoryLoading = true;//参数作为进入请求标识，防止重复请求
			this.scrollAnimation = false;//关闭滑动动画
			let conversationID = this.conversationActive.conversationID;
			let promise = this.tim.getMessageList({ conversationID: conversationID, nextReqMessageID: this.nextReqMessageID, count: this.count });
			promise&&promise.then(res => {
				res.data.messageList=res.data.messageList.map(n =>{
					n.ID=n.ID.replace(/@+/g,"")
					return n
				})
				this.$store.commit('unshiftCurrentMessageList', res.data.messageList);
				this.nextReqMessageID = res.data.nextReqMessageID; // 用于续拉，分页续拉时需传入该字段。
				this.isCompleted = res.data.isCompleted;
				this.isHistoryLoading = false;
			}).catch(err =>{
				this.isHistoryLoading = false;
			});
			//这段代码很重要，不然每次加载历史数据都会跳到顶部
			this.$nextTick(function() {
				this.scrollToView = this.nextReqMessageID; //跳转上次的第一行信息位置
				this.$nextTick(function() {
					this.scrollAnimation = true; //恢复滚动动画
				});
			});
			
		},
		// 加载初始页面消息
		getMsgList() {
			// 历史消息列表
			let conversationID = this.conversationActive.conversationID;
			console.log(conversationID)
			let promise = this.tim.getMessageList({ conversationID: conversationID, count: this.count });
			promise&&promise.then(res => {	
				console.log(res)
				res.data.messageList=res.data.messageList.map(n =>{
					console.log(n)
					n.ID=n.ID.replace(/@+/g,"")
					return n
				})
				this.$store.commit('pushCurrentMessageList', res.data.messageList);
				this.nextReqMessageID = res.data.nextReqMessageID; // 用于续拉，分页续拉时需传入该字段。
				this.isCompleted = res.data.isCompleted;
				this.scrollToView = res.data.messageList[res.data.messageList.length - 1].ID;
				// console.log(this.nextReqMessageID);
				// 滚动到底部
				this.$nextTick(function() {
					//进入页面滚动到底部
					this.scrollTop = this.msgList.length*100;
					this.$nextTick(function() {
						this.scrollAnimation = true;
					});
				});
			}).catch(err=>{
				console.log(err)
			});			
		},
		//处理图片尺寸，如果不处理宽高，新进入页面加载图片时候会闪
		setPicSize(content) {
			// 让图片最长边等于设置的最大长度，短边等比例缩小，图片控件真实改变，区别于aspectFit方式。
			let maxW = uni.upx2px(350); //350是定义消息图片最大宽度
			let maxH = uni.upx2px(350); //350是定义消息图片最大高度
			if (content.w > maxW || content.h > maxH) {
				let scale = content.w / content.h;
				content.w = scale > 1 ? maxW : maxH * scale;
				content.h = scale > 1 ? maxW / scale : maxH;
			}
			return content;
		},
		//更多功能(点击+弹出)
		showMore() {
			this.isVoice = false;
			this.hideEmoji = true;
			if (this.hideMore) {
				this.hideMore = false;
				this.openDrawer();
			} else {
				this.hideDrawer();
			}
		},
		// 打开抽屉
		openDrawer() {
			this.popupLayerClass = 'showLayer';
		},
		// 隐藏抽屉
		hideDrawer() {
			this.popupLayerClass = '';
			setTimeout(() => {
				this.hideMore = true;
				this.hideEmoji = true;
			}, 150);
		},
		// 选择图片发送
		chooseImage() {
			getApp().requestAndroidPermission('android.permission.WRITE_EXTERNAL_STORAGE', '存储权限');
			console.log('++++++++++');
			this.getImage('album');
		},
		//拍照发送
		camera() {
			this.getImage('camera');
		},
		//选照片 or 拍照
		
		getImage(type){
			this.hideDrawer();
			uni.chooseImage({
				count:1,
				sourceType:[type],
				sizeType: [ 'compressed'], //可以指定是原图还是压缩图，默认二者都有
				success: (res)=>{
					this.sendMsg({file:res},'img');
				}
			});
		},
		// 选择表情
		chooseEmoji() {
			this.hideMore = true;
			if (this.hideEmoji) {
				this.hideEmoji = false;
				this.openDrawer();
			} else {
				this.hideDrawer();
			}
		},
		//添加表情
		addEmoji(em) {
			this.textMsg += em;
		},

		//获取焦点，如果不是选表情ing,则关闭抽屉
		textareaFocus() {
			uni.onKeyboardHeightChange(res => {
			  console.log(res.height)
			})
			if (this.popupLayerClass == 'showLayer' && this.hideMore == false) {
				this.hideDrawer();
			}
		},
		// 发送文字消息
		sendText() {
			this.hideDrawer(); //隐藏抽屉
			if (!this.textMsg) {
				return;
			}
			let msg = { text: this.textMsg };
			this.textMsg = ''; //清空输入框
			this.sendMsg(msg, 'text');
			
			
		},
		// 发送消息
		sendMsg(content,type){
			let message
			if(type=="text"){
				message= this.tim.createTextMessage({
				  to: this.toUserId,
				  conversationType: 'C2C',
				  payload: {
					text: content.text
				  }
				});	
			}else if(type=="img"){
				message = this.tim.createImageMessage({
				  to: this.toUserId,
				  conversationType: 'C2C',
				  payload: {
					file: content.file
				  },
				  onProgress: function(event) { console.log('file uploading:', event) }
				});
			}else if(type=="voice"){
				message = this.tim.createCustomMessage({
				  to: this.toUserId,
				  conversationType: 'C2C',
				  payload: {
						  data: content.file, 
							description: content.length,
							extension: 'voice'
				  }
				});
			}else if(type=="custom"){
				message = this.tim.createCustomMessage({
				  to: this.toUserId,
				  conversationType: 'C2C',
				  payload: {
						  data: content.data, // 用于标识该消息是骰子类型消息
							description: "健康档案", // 获取骰子点数
							extension: 'health_file'
				  }
				});
			}
			let pomise = this.tim.sendMessage(message)
			pomise.then(res=>{
				console.log(res)
				res.data.message.ID=res.data.message.ID.replace(/@+/g,"")
				console.log(res)
				this.$store.commit('pushCurrentMessageList', res.data.message)
				setTimeout(() =>{
					this.$nextTick(function() {
						//进入页面滚动到底部
						this.scrollTop = this.msgList.length*100;
						this.$nextTick(function() {
							this.scrollAnimation = true;
						});
					});
				})
			})
		},
		// 添加文字消息到列表
		addTextMsg(msg) {
			this.msgList.push(msg);
		},
		// 添加语音消息到列表
		addVoiceMsg(msg) {
			this.msgList.push(msg);
		},
		// 添加图片消息到列表
		addImgMsg(img) {
			// msg.msg.content = this.setPicSize(msg.msg.content);
			this.msgImgList.push(img);
			// this.msgList.push(msg);
		},
		addRedEnvelopeMsg(msg) {
			this.msgList.push(msg);
		},
		// 添加系统文字消息到列表
		addSystemTextMsg(msg) {
			this.msgList.push(msg);
		},
		sendSystemMsg(content, type) {
			let lastid = this.msgList[this.msgList.length - 1].msg.id;
			lastid++;
			let row = { type: 'system', msg: { id: lastid, type: type, content: content } };
			this.screenMsg(row);
		},
		// 预览图片
		showPic(url) {
			console.log(url)
			console.log(this.msgImgList)
			uni.previewImage({
				indicator: 'none',
				current: url,
				urls: this.msgImgList
			});
		},
		// 播放语音
		playVoice(msg) {
			// console.log(msg)
			this.playMsgid = msg.ID;
			this.AUDIO.src = msg.payload.data;
			this.$nextTick(function() {
				this.AUDIO.play();
			});
		},
		// 录音开始
		voiceBegin(e) {
			if (e.touches.length > 1) {
				return;
			}
			if(this.recording){//正在录音中或者录音未完成时不执行
				return;
			}
			this.initPoint.Y = e.touches[0].clientY;
			this.initPoint.identifier = e.touches[0].identifier;
			this.RECORDER.start(this.recordOptions); //录音开始,
		},
		//录音开始UI效果
		recordBegin(e) {
			this.recording = true;
			this.voiceTis = '松开 结束';
			this.recordLength = 0;
			if(!this.recordTimer){
				this.recordTimer = setInterval(() => {
					this.recordLength++;
				}, 1000);
			}
		},
		// 录音被打断
		voiceCancel() {
			this.recording = false;
			this.voiceTis = '按住 说话';
			this.recordTis = '手指上滑 取消发送';
			this.willStop = true; //不发送录音
			this.RECORDER.stop(); //录音结束
		},
		// 录音中(判断是否触发上滑取消发送)
		voiceIng(e) {
			if (!this.recording) {
				return;
			}
			let touche = e.touches[0];
			//上滑一个导航栏的高度触发上滑取消发送
			if (this.initPoint.Y - touche.clientY >= uni.upx2px(100)) {
				this.willStop = true;
				this.recordTis = '松开手指 取消发送';
			} else {
				this.willStop = false;
				this.recordTis = '手指上滑 取消发送';
			}
		},
		// 结束录音
		voiceEnd(e) {
			if (!this.recording) {
				return;
			}
			// this.recording = false;
			this.voiceTis = '按住 说话';
			this.recordTis = '手指上滑 取消发送';
			this.RECORDER.stop(); //录音结束
		},
		//录音结束(回调文件)
		recordEnd(e) {
			if(this.recordTimer){
				clearInterval(this.recordTimer);
				this.recordTimer = 0;
			}
			if (!this.willStop) {
				console.log('e: ' + JSON.stringify(e));
				uniCloud.uploadFile({
					cloudPath:e.tempFilePath,
					filePath:e.tempFilePath,
				}).then(res=>{
					console.log(res)
					let msg = {
						file: res.fileID,
						length:0
					};
					let min = parseInt(this.recordLength / 60);
					let sec = this.recordLength % 60;
					min = min < 10 ? '0' + min : min;
					sec = sec < 10 ? '0' + sec : sec;
					msg.length = min + ':' + sec;					
					this.sendMsg(msg, 'voice');
					this.recording = false;
				}).catch(err =>{
					console.log(err)
					this.recording = false;
				})
			} else {
				console.log('取消发送录音');
				this.recording = false;
			}
			this.willStop = false;
		},
		// 切换语音/文字输入
		switchVoice() {
			this.hideDrawer();
			this.isVoice = this.isVoice ? false : true;
		},
		discard() {
			return;
		},
		toHealth(val){
			let tmp
			if(val){
				tmp='?userInfo='+encodeURIComponent(val.data)
			}
			uni.navigateTo({
				url: '/pages/inquiry/health'+tmp
			});
		}
	}
};
</script>
<style lang="scss">
@import '@/static/HM-chat/css/style.scss';
.health{
	width: 50px;
	height: 60px;
}
.btn{
	color: $uni-text-color;
	font-size: $uni-font-size-base;
	margin-right: $uni-spacing-row-lg;
}
.endtip{
	position: fixed;
	z-index: 20;
	bottom:-2upx;
	opacity: 1;
	background-color: #FFFFFF;
	padding: $uni-spacing-col-lg $uni-spacing-row-base;
	.tip{
		font-size: $uni-font-size-base;
		color: $uni-text-color-grey;
	}
}
.box{
	overflow: hidden;
}
.imagesText {
	display: flex;
	flex-direction: column;
	// font-size: 26upx;
	text-align: center;
	.img_box{
		width: 90upx;
		height: 90upx;
		margin: 0 auto 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		.imagesSize {
			transform: scale(.1);
			transform-origin: center center;
			display: block;
			height: auto;
		}
	}
}
</style>
