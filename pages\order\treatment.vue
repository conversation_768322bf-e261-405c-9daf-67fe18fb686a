<template>
	<view id="order">
		<view class="registration">
			<view class="space-between">
				<view class="flex-start left">
					<image class="tab" src="/static/img/order_img/treatment.png" mode=""></image>
					<text class="typetext">检查治疗项订单</text>
				</view>
				<text class="status">待付款</text>
			</view>
			<view>
				<view>
					<text class="label">医生姓名:</text>
					<text class="ctx"></text>
				</view>
				<view>
					<text class="label">科室:</text>
					<text class="ctx"></text>
				</view>
				<view>
					<text class="label">诊所名:</text>
					<text class="ctx"></text>
				</view>
				<view>
					<text class="label">诊所地址:</text>
					<text></text>
				</view>
				<view>
					<text class="label">治疗名称:</text>
					<text class="ctx"></text>
				</view>
				<view>
					<text class="label">创建订单时间:</text>
					<text class="ctx"></text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {};
	}
};
</script>

<style lang="scss">
#order {
	margin: $uni-spacing-col-lg $uni-spacing-row-lg 0px;
	background-color: $uni-bg-color;
		border-radius: $uni-border-radius-lg;
	.registration {
		margin:0px $uni-spacing-row-lg ;
		padding: $uni-spacing-col-lg 0px;
		.left {
			align-items: center;
			image {
				width: $uni-img-size-base;
				height: $uni-img-size-base;
			}
			.typetext {
				color: $uni-text-color;
				font-size: $uni-font-size-base;
				font-weight: bold;
			}
		}
		.status {
			color: #ec4040;
			font-size: $uni-font-size-base;
			font-weight: bold;
		}
		.label {
			display: inline-block;
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
			width: 100px;
		}
		.ctx {
			color: $uni-text-color;
			font-size: $uni-font-size-base;
		}
	}
}
</style>
