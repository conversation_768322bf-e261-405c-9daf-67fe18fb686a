<template>
	<mescroll-body ref="mescrollRef" @init="mescrollInit" :down="downOption" :up="upOption" @down="downCallback"
		@up="upCallback">
		<view class="news-li" v-for="(n, index) in dataList" :key="n.adddate" @tap="toMsgDetails(n)">
			<view class="space-between">
				<view class="title">
					<text class="circle"></text>
					<text>通知</text>
				</view>
				<!-- <text class="title">{{n.isread==0?'未读':'已读'}}</text> -->
			</view>
			<view class="time">
				<text>{{ n.adddate }}</text>
			</view>
			<view class="msg">{{ n.msg }}</view>
			<view class="flex-end-center btns" v-if="n.type == 13 && (n.isread == 0 || n.isread == 1)">
				<button type="default" size="mini" class="agree" @click.stop="acceptInquiry(n, index, -2)">拒绝</button>
				<button type="default" size="mini" class="agree confirm"
					@click.stop="acceptInquiry(n, index, 1)">同意</button>
			</view>
			<view class="flex-end-center btns" v-if="n.type == 14 && (n.isread == 0 || n.isread == 1)">
				<button type="default" size="mini" class="agree" @click.stop="acceptJoin(n, index, 3)">拒绝</button>
				<button type="default" size="mini" class="agree confirm"
					@click.stop="acceptJoin(n, index, 2)">同意</button>
			</view>
		</view>
	</mescroll-body>
</template>

<script>
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	import moment from 'moment';
	let that;
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				downOption: {
					auto: false //
				},
				upOption: {
					auto: true,
					empty: {
						use: true, // 是否显示空布局
						icon: '/static/img/msg-empty.png', // 图标路径 (建议放入static目录, 如 /static/img/mescroll-empty.png )
						tip: '~ 空空如也 ~' // 提示
					}
				},
				dataList: [],
				unread: [] //未读消息
			};
		},
		onShow() {
			uni.removeTabBarBadge({
				index: 1
			});
			this.$store.commit('updateUnreadtotal', 0);
			this.setRead();
		},
		onLoad() {
			that = this;
			uni.$on('updateMsg', function() {
				that.dataList = [];
				that.mescroll.resetUpScroll();
			});
			// #ifdef APP-PLUS
			getApp().requestAndroidPermission('android.permission.READ_PHONE_STATE', '电话权限');
			// #endif
		},
		methods: {
			/*下拉刷新的回调 */
			downCallback() {
				that.dataList = [];
				that.mescroll.resetUpScroll();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				this.unread = [];
				this.$api
					.get('api/patient/message/getlist', {
						params: {
							pager: page.num
						}
					})
					.then(res => {
						// console.log(res)
						this.mescroll.endSuccess(res.length);
						//设置列表数据
						let arr = res.map(n => {
							n.adddate = moment(n.adddate).format('YYYY/MM/DD HH:mm:ss');
							if (n.isread == 0) {
								that.unread.push(n.id);
								n.isread = 1;
							}
							return n;
						});
						this.dataList = this.dataList.concat(arr);
						let pages = getCurrentPages();
						if (pages[pages.length - 1].route == 'pages/msg/msg') {
							// console.log(that.unread)
							this.setRead();
						}
					})
					.catch(err => {
						this.mescroll.endErr();
					});
			},
			toMsgDetails(data) {
				let type = JSON.parse(data.data).type;
				let args = JSON.parse(data.data).data;
				// console.log(JSON.stringify(args))
				switch (type) {
					case 1:
						break;
					case 2:
						uni.navigateTo({
							url: ''
						});
						break;
					case 3:
						// if (args.ordernumber) {
						// 	uni.navigateTo({
						// 			url:"../order/index?tabIndex=1"
						// 	})
						// }
						break;
					case 4:
						uni.navigateTo({
							url: '../order/index?tabIndex=0'
						});
						break;
					case 5:
						break;
					case 7:
						uni.navigateTo({
							url: '../order/index?tabIndex=0'
						});
						break;
					case 9:
						// uni.navigateTo({
						// 		url:'/pages/medrecord/medrecordDeatils?medid=' + args.medid
						// })
						break;
					default:
						break;
				}
			},
			acceptInquiry(val, index, agree) {
				let data = JSON.parse(val.data).data;

				let tip = agree == 1 ? `是否同意${data.doctorname}的复诊问诊邀请` : `是否拒绝${data.doctorname}的复诊问诊邀请`;

				uni.showModal({
					title: '温馨提示',
					content: tip,
					success: function(res) {
						if (res.confirm) {
							that.$api
								.post('api/patient/medReview', {
									id: data.id, //复诊id
									status: agree //1 同意 -2 拒绝
								})
								.then(res => {
									if (!res.errno) {
										that.dataList[index].isread = agree == 1 ? 2 : 3;
										if (agree == 1) {
											uni.showToast({
												icon: 'none',
												title: `您已同意了${data.doctorname}的邀请，请在待付款订单页面完成付款`
											});
											//跳转到待付款页面
											uni.navigateTo('../order/index?tabIndex=1');
										} else {
											uni.showToast({
												icon: 'none',
												title: `您已拒绝了${data.doctorname}的邀请`
											});
										}
									} else {
										if (res.errno > 300 && res.errno < 400) {
											uni.showToast({
												icon: 'none',
												title: res.msg
											});
										} else {
											uni.showToast({
												icon: 'none',
												title: '操作出错，请稍后重试'
											});
										}
									}
								});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			acceptJoin(val, index, agree) {
				let data = JSON.parse(val.data)
				let tip = agree == 2 ? `是否同意${data.clinicName}的会员邀请` : `是否拒绝${data.clinicName}的会员邀请`;

				uni.showModal({
					title: '温馨提示',
					content: tip,
					success: function(res) {
						if (res.confirm) {
							that.$api
								.post('api/patient/vip/addvip', {
									id: data.clinicId, //诊所id
									status: agree, //2 同意 3 拒绝
									msgId: val.id //消息id
								})
								.then(res => {
									if (res.success) {
										uni.showToast({
											icon: 'none',
											title: agree == 2 ? `您已同意了${data.clinicName}的邀请` :
												`您已拒绝了${data.clinicName}的邀请`
										});
										that.dataList[index].isread = agree == 2 ? 2 : 3;
									} else {
										that.dataList[index].isread = 3
										uni.showToast({
											icon: 'none',
											title: res.msg
										});
									}
								});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			setRead() {
				//未读设置已读
				if (that.unread.length == 0) {
					return;
				}
				this.$api
					.post(
						'api/patient/message/sethasread', {
							unread: that.unread
						}, {
							header: {
								'content-type': 'application/json'
							}
						}
					)
					.then(res => {
						// console.log(res)
						this.$store.state.unreadtotal = 0;
					})
					.catch(err => {
						// console.log(err)
					});
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;
		padding: $uni-spacing-col-base 0px;
	}

	.news-li {
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-base $uni-spacing-row-base;
		margin: $uni-spacing-col-base $uni-spacing-row-lg;
		border-radius: $uni-border-radius-lg;

		.title {
			color: $uni-text-color;
			font-size: $uni-font-size-base;
			font-weight: 700;
		}

		.time {
			color: $uni-text-color-grey;
			font-size: $uni-font-size-sm;
			margin: $uni-spacing-col-sm 0px;
		}

		.msg {
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
		}

		.circle {
			display: inline-block;
			height: 10px;
			width: 10px;
			border-radius: $uni-border-radius-circle;
			background-color: #5fe7c2;
			margin-right: $uni-spacing-col-base;
		}
	}

	.btns {
		margin-top: 20rpx;

		.confirm {
			background-color: $jian-bg-color;
			color: #fff;
		}
	}
</style>