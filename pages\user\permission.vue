<template>
	<view>
		<view class="permission">
			<view class="left" @click="openSpecification(1)">
				<view class="text">
					设置通知权限
				</view>
				<view class="desc">
					包含通知权限及"横幅"、"锁屏"通知选择。如无权限则无法正常收到音视频通话的呼入通知
				</view>
				<view class="btn" >
					点击查看设置说明
				</view>
			</view>
			<view class="right">
				<view class="goset" @click="toopensetting()">
					<view>前往设置权限</view>
					<uni-icons :size="20" class="uni-icon-wrapper" color="#bbb" type="arrowright" />
				</view>
			</view>
		</view>
		<view class="permission" v-if="platform=='android'">
			<view class="left" @click="openSpecification(2)">
				<view class="text">
					设置悬浮窗权限
				</view>
				<view class="desc">
					如无权限则音视频通话时的视频小窗口会有异常
				</view>
				<view class="btn" >
					点击查看设置说明
				</view>
			</view>
			<view class="right">
				
				<view class="goset" @click="openwindow()">
					<view>前往设置权限</view>
					<uni-icons :size="20" class="uni-icon-wrapper" color="#bbb" type="arrowright" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				platform:uni.getSystemInfoSync().platform
			};
		},
		methods:{
			toopensetting() { //弹窗按钮绑定方法
				if(this.nomoretip){
					uni.setStorageSync('nomorepushtip','true')
				}
				let platform = uni.getSystemInfoSync().platform; //获取安卓还是ios
				if (platform == "ios") { //如果机型是ios，ios由于权限问题，可能需要手动开启
					var UIApplication = plus.ios.import("UIApplication");
					var application2 = UIApplication.sharedApplication();
					var NSURL2 = plus.ios.import("NSURL");
					// var setting2 = NSURL2.URLWithString("prefs:root=LOCATION_SERVICES");		
					var setting2 = NSURL2.URLWithString("app-settings:");
					application2.openURL(setting2);
					
					plus.ios.deleteObject(setting2);
					plus.ios.deleteObject(NSURL2);
					plus.ios.deleteObject(application2);
				} else if (platform == "android") { //如果机型是安卓
					var main = plus.android.runtimeMainActivity();
					var pkName = main.getPackageName();
					var uid = main.getApplicationInfo().plusGetAttribute("uid");
					var Intent = plus.android.importClass("android.content.Intent");
					var Build = plus.android.importClass("android.os.Build");
					//android 8.0引导
					if (Build.VERSION.SDK_INT >= 26) { //判断安卓系统版本
						var intent = new Intent("android.settings.APP_NOTIFICATION_SETTINGS");
						intent.putExtra("android.provider.extra.APP_PACKAGE", pkName);
					} else if (Build.VERSION.SDK_INT >= 21) { //判断安卓系统版本
						//android 5.0-7.0
						var intent = new Intent("android.settings.APP_NOTIFICATION_SETTINGS");
						intent.putExtra("app_package", pkName);
						intent.putExtra("app_uid", uid);
					} else {
						//(<21)其他--跳转到该应用管理的详情页
						intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
						var uri = Uri.fromParts(
							"package",
							mainActivity.getPackageName(),
							null
						);
						intent.setData(uri);
					}
					// 跳转到该应用的系统通知设置页
					main.startActivity(intent);
				}
			},
			openSpecification(val){
				let url
				if(val==1){
					url = this.$store.state.helper.notification_pr
				}else if(val==2){
					url = this.$store.state.helper.smallwindow_pr
				}
				plus.runtime.openURL(url)
			},
			openwindow(){
				var main = plus.android.runtimeMainActivity()  
				var pkName = main.getPackageName()  
				var Settings = plus.android.importClass('android.provider.Settings')  
				var Uri = plus.android.importClass('android.net.Uri')  
				var Build = plus.android.importClass('android.os.Build')  
				var Intent = plus.android.importClass('android.content.Intent')  
				var intent = new Intent(  
				  'android.settings.action.MANAGE_OVERLAY_PERMISSION',  
				  Uri.parse('package:' + pkName)  
				)  
				 main.startActivityForResult(intent, 5004) 
			},
		}
	}
</script>

<style lang="scss">
page{
	background-color: $uni-bg-color-grey;
}
.permission{
	background-color: #fff;
	border-bottom: solid 1rpx $uni-bg-color-grey;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 32rpx;
	.left{
		width:70%;
		.text{
			font-size: 28rpx;
		}
		.desc{
			font-size: 22rpx;
			color: $uni-text-color-grey;
		}
		.btn{
			font-size: 28rpx;
			color: $jian-bg-color;
			line-height: 40rpx;
		}
	}
	
	.right{
		width:30%;
		display: flex;
		
		.goset{
			color: $uni-text-color-grey;
			font-size: $uni-font-size-sm;
			display: flex;
			align-items: center;
			margin-left: 20rpx;
		}
	}
	
}
</style>
