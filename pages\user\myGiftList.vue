<template>
	<view class="mescroll-body">
		<view class="head">
			<view class="Integral_box">
				<view class="a_box">
					<view class="integral-top">
						<text>我的礼品</text>
					</view>

					<view class="giftList">
						<view class="giftListChild" @click="getGiving(item)">赠送礼品</view>
					</view>
				</view>
			</view>

			<mescroll-body ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback"
				:down="downOption" :up="upOption">
				<!-- 积分列表 :showArrow="false" -->
				<view class="uni-list-item" v-for="(item,index) in getMyGift" :key="index" >
					<view class="uni-list-item-left">
						<image :src="item.img" mode="" class="images"></image>
					</view>
				
					<view class="uni-list-item-center">
						<!-- <text>{{item.origin}}</text> -->
						<view class="name">{{item.presents_name}}</view>
						<view class="num">
							<view class="textCreated">购买数量：{{item.num}}</view>
							<view class="textCreated">剩余数量数量：{{item.leave_num}}</view>
						</view>
						<view class="textCreated">购买时间：{{item.created_at}}</view>
						<view class="textCreated">过期时间：{{item.expire_time}}</view>
					</view>
					<view class="uni-list-item-right green" v-if="item.finins_status==0">
						已使用
					</view>
					<view class="uni-list-item-right" v-if="item.expire_status==1&&item.finins_status==1">
						已过期
					</view>
				</view>
			</mescroll-body>
		</view>
	</view>
</template>

<script>
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				downOption: {
					use: true, // 是否启用下拉刷新; 默认true
				},
				upOption: {
					auto: true, // 是否在初始化完毕之后自动执行上拉加载的回调; 默认true
					empty: {
						use: true, // 是否显示空布局
						icon: "/static/img/msg-empty.png", // 图标路径 (建议放入static目录, 如 /static/img/mescroll-empty.png )
						tip: '~ 空空如也 ~' // 提示
					}
				},
				params: {
					page: 0,
				}, // 页码长度 
				// 是否显示
				downState: false,
				last_page: '',
				getMyGift:[]
			}
		},
		onShow() {
			
		},
		onLoad() {

		},
		methods: {
			// 积分列表
			getgetMyGift() {
				if (this.getMyGift.length == 0) {
					uni.showLoading({
						title: '加载中',
						mask: true
					});
				}
				// 判断如果用户是上拉刷新那么就 拉去第一页的 否则就是第二页
				let params = {
					page: 1
				}
				this.$api.get('api/patient/present/getpresentlist', params = params).then(res => {
					console.log(res)
					this.getMyGift = res.data.data
					this.last_page = res.data.last_page
					this.mescroll.endBySize(res.data.per_page, res.data.total);
					uni.hideLoading()
				}).catch(err => {
					
				})
			},
			/*下拉刷新的回调 */
			downCallback() {
				this.params.page = 0
				this.getMyGift = []
				// this.getgetMyGift()
				// 下拉刷新的回调,默认重置上拉加载列表为第一页 (自动执行 page.num=1, 再触发upCallback方法 )
				this.mescroll.resetUpScroll()
				// this.mescroll.endErr();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				this.params.page++
				let params = {
					params: this.params
				}
				this.$api.get('api/patient/present/getpresentlist', params = params).then(res => {
					// 调用此方法则 已经没有数据的时候就不会在显示加载中 而是 END
					this.mescroll.endBySize(res.data.per_page, res.data.total);
					this.getMyGift = this.getMyGift.concat(res.data.data) //将数据拼接在一起
					this.last_page = res.data.last_page

					// this.mescroll.endErr();
				}).catch(err => {
					this.mescroll.endErr();
				})
			},
			// 点击回到顶部按钮的回调
			topClick() {
				console.log('点击了回到顶部按钮');
			},
			getGiving(item) {
				uni.navigateTo({
					url: '../doctors/doctorsList?type=2'
				});
			}
		},

	}
</script>

<style lang="scss">
	page {
		background-color: rgb(240, 240, 240);
	}

	.head {
		width: 90%;
		margin-left: 5%;
		padding-top: 200upx;

		.Integral_box {
			height: 200upx;
			top: 0;
			left: 0;
			width: 100%;
			background-color: rgb(240, 240, 240);
			position: fixed;
			z-index: 999;

			.a_box {
				width: 90%;
				height: 102upx;
				background-color: rgb(255, 255, 255);
				text-align: center;
				display: flex;
				align-items: center;
				border-radius: 20rpx;
				margin: 50upx 5% 50upx 5%;
				justify-content: space-around;
				.integral-top {
				}

				.giftList {

					.giftListChild {
						outline: none; //消除默认点击蓝色边框效果
						font-size: 26upx;
						text-align: center;
						width: 160upx;
						height: 60upx;
						padding: 0;
						line-height: 60upx;
						color: $jian-bg-color;
						background-color: #fff;
						border: solid 1rpx $jian-bg-color;
						border-radius: 30upx;
					}
				}
			}
		}

	}

	.mescroll-body {
		background-color: rgb(240, 240, 240);
		padding: 20upx 0upx;
		// background-color: #FF5053;

	}

	.uni-list-item {
		width: 96%;
		padding: 15rpx ;
		margin-bottom: 25upx;
		margin-left: 2%;
		box-sizing: border-box;
		background-color: rgb(255, 255, 255);
		border-radius: 20rpx;
		display: flex;
		justify-content: flex-start;
		align-items: center;
		.uni-list-item-left {
			display: flex;
			justify-content: center;
			align-items: center;
	
			.images {
				width: 80upx;
				height: 80upx;
			}
		}
	
		.uni-list-item-center {
			margin-left: 30rpx;
			.name {
				color: $jian-bg-color;
			}
			.num{
				display: flex;
			}
			.textCreated {
				font-size: 24upx;
				color: rgb(137, 137, 137);
				margin-right: 20rpx;
			}
		}
	
		.uni-list-item-right {
			color: #ff0000;
			font-size: 32rpx;
			margin-left: auto;
		}
		.green{
			color: $jian-bg-color;
		}
	}

	.upwarp-nodata {
		text-align: center;
		font-size: 30upx;
		color: rgb(137, 137, 137);
	}
</style>
