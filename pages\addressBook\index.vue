<template>
	<view>
		<!-- 菜单 -->
		<view class="top-warp">
			<app-tabs v-model="tabIndex" :tabs="tabs"></app-tabs>
		</view>
		<!-- 子组件 (i: 每个tab页的专属下标;  index: 当前tab的下标) -->
		<!-- 如果每个子组件布局不一样, 可拆开写 (注意ref不可重复) : -->
		<friend ref="mescrollItem0" :i="0" :index="tabIndex"></friend>
		<twoItem ref="mescrollItem1" :i="1" :index="tabIndex"></twoItem>
	</view>
</template>

<script>
	import Friend from "./friend.vue";
	import TwoItem from "./twoItem.vue";
	import AppTabs from "@/components/other/app-tabs.vue";
	export default {
		components: {
			Friend,
			TwoItem,
			AppTabs
		},
		data() {
			return {
				tabs: ['我的医生', '最近联系人'],
				tabIndex: 0, // 当前tab下标
			}
		},
		onLoad(options){
			if(options.tab==1){
				this.tabIndex = 1
			}
		}
	}
</script>

<style>

</style>
