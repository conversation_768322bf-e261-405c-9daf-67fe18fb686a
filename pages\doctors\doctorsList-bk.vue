<template>
	<view>
		<!--S 刷选 -->
		<view class="space-around nav">
			<view class="flex-start-center" @click="getDoctorByPrice">
				<text class="label" :id="isclick == 0 ? 'active' : ''">价格</text>
				<div>
					<text :class="pariceSort == 1 && isclick == 0 ? 'top topactive' : 'top'"></text>
					<text :class="pariceSort == 0 && isclick == 0 ? 'down downactive' : 'down'"></text>
				</div>
			</view>
			<view class="flex-start-center" @click="getDoctorByService">
				<text class="label" :id="isclick == 1 ? 'active' : ''">服务</text>
				<div>
					<text :class="serviceSort == 1 && isclick == 1 ? 'top topactive' : 'top'"></text>
					<text :class="serviceSort == 0 && isclick == 1 ? 'down downactive' : 'down'"></text>
				</div>
			</view>
			<view class="flex-start-center" @click="getDoctorByRate">
				<text class="label" :id="isclick == 2 ? 'active' : ''">评分</text>
				<div>
					<text :class="rateSort == 1 && isclick == 2 ? 'top topactive' : 'top'"></text>
					<text :class="rateSort == 0 && isclick == 2 ? 'down downactive' : 'down'"></text>
				</div>
			</view>
		</view>
		<!--E 刷选 -->
		<mescroll-body id="base" top="80" ref="mescrollRef" @init="mescrollInit" :down="downOption" :up="upOption"
			@down="downCallback" @up="upCallback">
			<view class="item" v-for="n in arr" @click="toDoctorInfo(n.id)">
				<view class="flex-start">
					<view class="">
						<image class="avatar" v-if="!n.face && n.gender == 0" src="../../static/img/defaultgirl.png"
							mode=""></image>
						<image class="avatar" v-if="!n.face && n.gender == 1" src="../../static/img/defaultman.png"
							mode=""></image>
						<image class="avatar" v-if="n.face" :src="$baseUrl + n.face"
							onerror="this.src='/static/img/default.png'" mode=""></image>
					</view>
					<view class="rightctx">
						<view class="flex-start-center">
							<text class="name">{{ n.realname }}</text>
						</view>
						<view class="flex-start-center box">
							<text v-if="n.cliname">{{n.cliname}}</text>
							<text v-if="n.professional">{{ n.professional }}</text>
						</view>
					</view>
				</view>
				<view class="">
					<view class="flex-start-center">
						<text class="brief" v-if="n.brief">擅长：{{ n.brief }}</text>
					</view>
					<view class="flex-between-align">
						<view class="">
					<view class="flex-start-center">
						<view class="">
							<image class="star" src="/static/img/doctors_img/star1.png" mode=""></image>
							<text class="score">{{ n.score }}</text>
						</view>
								<text>服务人次：{{ n.number }}</text>
					</view>
					<view class="space-between-center">
						<text v-if='n.lastlogin'>最近上线：{{lastLogin(n.lastlogin)}}</text>
					</view>
						</view>
						<button v-if='opentype==2' type="button" class="btnGiving" @click.stop="givingDoctor(n)">赠送礼品</button>
					</view>
					<view class="flex-start-center">
						<text class="tab" v-for="label in n.postcard" :key="label">{{ label }}</text>
					</view>
					<view class="service flex-start-center">
						<view v-if="n.isreservation==1 && n.clinid>0" class="flex-start-center">
							<image class="inquiry" src="/static/img/doctors_img/reserve.png" mode=""></image>
							<text class="money">预约挂号￥{{ n.registerfee }}</text>
						</view>
						<view v-if="n.isremote==1" class="flex-start-center">
							<image class="reserve" src="/static/img/doctors_img/inquiry.png" mode=""></image>
							<text class="money">图文咨询￥{{ n.inquiryfee }}</text>
						</view>
						<view v-if="n.is_family_doctor==1" class="flex-start-center">
							<image class="reserve" src="/static/img/doctors_img/family.png" mode=""></image>
							<text class="money">私人医生￥{{ n.family_min_price }}起</text>
					</view>
						<view v-if="n.isvideocall==1" class="flex-start-center">
							<image class="reserve" src="/static/img/doctors_img/videoCall.png" mode=""></image>
							<text class="money">视频通话￥{{ n.video_price }}</text>
						</view>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	import uniIcons from '@/components/uni-icons/uni-icons.vue';
//	import uniDataSelect from '@/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue'
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		components: {
			uniIcons
//			uniDataSelect
		},
		onNavigationBarSearchInputChanged(e) {
			this.args.keywords = e.text;
		},
		onNavigationBarSearchInputConfirmed() {
			//在这里执行搜索操作
			if (this.args.keywords.length > 0) {
				this.arr = [];
				this.mescroll.resetUpScroll();
				this.mescroll.scrollTo( 0, 0 )
			} else {
				uni.showToast({
					icon: 'none',
					title: '请输入关键字后再搜索'
				});
			}
		},
		onNavigationBarButtonTap(e) {
			if (this.args.keywords.length > 0) {
				if (e.index == 0) {
					this.arr = [];
					this.mescroll.resetUpScroll();
					this.mescroll.scrollTo( 0, 0 )
				}
			} else {
				uni.showToast({
					icon: 'none',
					title: '请输入关键字后再搜索'
				});
			}
		},
		data() {
			return {
				upOption: {
					auto: true,
					page: {
						num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
						size: 7 // 每页数据的数量,默认10
					}
				},
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
				args: {
					pager: 1, //当前页
					keywords: '', //关键字
					search_type: 0, //0 价格 1 服务数
					search_method: 1, //0 降序 1 升序
					clind_id: 0, //医院id
				},
				isclick: 0,
				pariceSort: 1, //价格的排序
				serviceSort: 1, //服务的排序
				rateSort: 1, //评分排序
				arr: [],
				isPullDown: false,
				isPullUp: false,
				pulltext: '',
				opentype: 1

			};
		},
		onLoad: function(option) {
			this.opentype = option.type
			if (option.id) {
				this.args.clind_id = option.id
			}
		},
		created() {},
		watch: {
			pariceSort: function(val) {
				this.args.search_method = val;
				this.arr = [];
				this.args.pager = 1;
				this.mescroll.resetUpScroll();
			},
			serviceSort: function(val) {
				this.args.search_method = val;
				this.arr = [];
				this.args.pager = 1;
				this.mescroll.resetUpScroll();
			},
			rateSort: function(val) {
				this.args.search_method = val;
				this.arr = [];
				this.args.pager = 1;
				this.mescroll.resetUpScroll();
			},
		},
		methods: {
			// 赠送医生
			givingDoctor(item) {
				this.doctorId = item.id
				uni.navigateTo({
					url: 'confirmPresent?doctorId=' + this.doctorId
				})
			},
			/*下拉刷新的回调 */
			downCallback() {
				this.arr = [];
				// 下拉刷新的回调,默认重置上拉加载列表为第一页 (自动执行 page.num=1, 再触发upCallback方法 )
				this.mescroll.resetUpScroll();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				this.args.pager = page.num;
				this.getdoctorlist();
			},
			getdoctorlist(args) {
				//获取医生列表
				var tmp = {
					pager: this.args.pager,
					keywords: this.args.keywords,
					search_type: this.args.search_type,
					search_method: this.args.search_method
				};
				console.log(tmp)
				if (tmp.keywords == '') {
					delete tmp.keywords
				}
				if (this.args.clind_id) {
					console.log(this.args.clind_id)
					tmp.clinid = this.args.clind_id;
				}
				let that = this;
				this.$api
					.get('api/patient/doctor/getlist', {
						params: tmp
					})
					.then(res => {
						// console.log(res)
						that.arr = this.arr.concat(res);
						let hasNext = res.length < 7 ? false : true;
						this.mescroll.endSuccess(res.length, hasNext)
					})
					.catch(res => {
						that.mescroll.endErr();
					});
			},
			toDoctorInfo(id) {
				uni.navigateTo({
					url: '/pages/doctors/doctorsDetails?id=' + id
				});
			},
			getDoctorByPrice() {
				//根据价格排序
				this.isclick = 0;
				if (this.args.search_type == 0) {
					if (this.pariceSort == 1) {
						this.pariceSort = 0;
					} else {
						this.pariceSort = 1;
					}
				} else {
					this.args.search_type = 0;
					this.args.pager = 1;
					this.args.search_method = this.pariceSort;
					this.arr = [];
					var tmp = {
						pager: this.args.pager,
						search_type: this.args.search_type,
						search_method: this.args.search_method
					};
					this.mescroll.resetUpScroll();
				}
			},
			getDoctorByService() {
				//根据服务排序
				this.isclick = 1;
				if (this.args.search_type == 1) {
					if (this.serviceSort == 1) {
						this.serviceSort = 0;
					} else {
						this.serviceSort = 1;
					}
				} else {
					this.args.search_type = 1;
					this.args.pager = 1;
					this.args.search_method = this.serviceSort;
					this.arr = [];
					var tmp = {
						pager: this.args.pager,
						search_type: this.args.search_type,
						search_method: this.args.search_method
					};
					this.mescroll.resetUpScroll();
				}
			},
			getDoctorByRate() {
				//根据评分排序
				this.isclick = 2;
				if (this.args.search_type == 2) {
					if (this.rateSort == 1) {
						this.rateSort = 0;
					} else {
						this.rateSort = 1;
					}
				} else {
					this.args.search_type = 2;
					this.args.pager = 1;
					this.args.search_method = this.rateSort;
					this.arr = [];
					var tmp = {
						pager: this.args.pager,
						search_type: this.args.search_type,
						search_method: this.args.search_method
					};
					this.mescroll.resetUpScroll();
				}
			},
			closeKeyboard() {
				if (event.keyCode == 13) {
					event.currentTarget.blur();
				}
			},
			// 最后活跃时间

			lastLogin(val) {
				let time = val.split(' ')[0]
				return time
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;
	}

	.btnGiving {
		width: 130rpx;
		height: 60upx;
		background-color: #15CB9F;
		font-size: 30upx;
		text-align: center;
		line-height: 60upx;
		color: rgb(255, 255, 255);
		margin-right: 0;
		margin-top: 5rpx;
	}

	#active {
		color: #5fe7c2;
	}

	.flex-end {
		margin-left: auto;
	}

	.nav {
		position: fixed;
		z-index: 9999;
		width: 100%;
		background-color: $uni-bg-color;
		flex-wrap: nowrap;
		padding: $uni-spacing-row-sm 0px;

		&>view {
			.label {
				font-size: $uni-font-size-lg;
				color: $uni-text-color;
				font-weight: 500;
				margin-right: $uni-spacing-row-sm;
			}

			.down {
				display: block;
				width: 0;
				height: 0;
				border-right: 6px solid transparent;
				border-left: 6px solid transparent;
				border-top: 6px solid #9a9a9a;
				border-radius: 3px;
			}

			.top {
				display: block;
				width: 0;
				height: 0;
				border-right: 6px solid transparent;
				border-left: 6px solid transparent;
				border-bottom: 6px solid #9a9a9a;
				border-radius: 3px;
				margin-bottom: 1px;
			}

			.topactive {
				border-bottom: 6px solid #60e7c2;
			}

			.downactive {
				border-top: 6px solid #60e7c2;
			}
		}
	}

	.item {
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;
		margin: 0px $uni-spacing-row-lg;
		margin-top: $uni-spacing-col-lg;
		background-color: $uni-bg-color;
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
		align-items: center;

		.name {
			color: $uni-text-color;
			font-size: $uni-font-size-lg;
			font-weight: bold;
			margin-right: $uni-spacing-row-sm;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}

		.avatar {
			width: 50px;
			height: 50px;
			margin-right: $uni-spacing-row-base;
			border-radius: $uni-border-radius-circle;
		}

		.rightctx {
			width: calc(100% - 60px);

			.titles {
				display: flex;
				justify-content: space-between;
			}

			.flex-start-center {
				flex-wrap: wrap;
			}

			.box {
			margin-top: 5rpx;
				text {
					margin-right: 20rpx;
				}
			}
		}

		.star {
			width: 12px;
			height: 12px;
			margin-right: $uni-spacing-col-sm;
		}

		.brief {
			margin: 10rpx;
			color: $uni-text-color;
			font-size: $uni-font-size-base;
		}

		.score {
			color: #fcac00;
			font-weight: bold;
			font-size: $uni-font-size-base;
			margin-right: $uni-spacing-row-lg;
		}

		.tab {
			margin-top: $uni-spacing-col-sm;
			margin-bottom: $uni-spacing-col-sm;
			margin-right: $uni-spacing-row-sm;
			background: rgba(247, 247, 247, 1);
			font-size: $uni-font-size-base;
			color: #9a9a9a;
			padding: $uni-spacing-col-sm $uni-spacing-row-sm;
			font-size: $uni-font-size-sm;
			border-radius: $uni-border-radius-lg;
		}

		.departmentname {
			margin-right: $uni-spacing-col-sm;
		}

		.inquiry,
		.reserve {
			width: 12px;
			height: 12px;
			margin-right: $uni-spacing-col-sm;
		}

		.money {
			font-size: $uni-font-size-base;
			color: $uni-text-color;
			margin-right: $uni-spacing-row-lg;
		}
	}
	.flex-between-align{
		display: flex;
		justify-content: space-between;		
	}
	.service{
		flex-wrap: wrap;
	}
</style>