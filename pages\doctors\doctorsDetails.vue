<template>
	<view>
		<view class="details ">
			<view class="status_bar"></view>
			<!-- 分享按钮 -->
			<view class="flex-start">
				<image class="left" v-if="details.face" :src="$baseUrl + details.face" mode=""></image>
				<image class="left" v-if="!details.face && details.gender == 1" src="../../static/img/defaultman.png"
					mode=""></image>
				<image class="left" v-if="!details.face && details.gender == 0" src="../../static/img/defaultgirl.png"
					mode=""></image>
				<view class="rigth">
					<view class="flex-start">
						<view class="name">
							<view class="realname">{{ details.realname }}</view>
						</view>
						<text v-if="details.departmentname">{{ details.departmentname }}</text>
						<text v-if="details.professional">{{ details.professional }}</text>

					</view>
					<view class="space-between">
						<text>{{ details.cliname }}</text>

					</view>
					<view class="flex-start">
						<text>问诊服务人次:{{ details.number }}</text>
						<text>评分:{{ details.score }}分</text>
					</view>
					<view>
						<text class="label" v-for="n in details.postcard">{{ n }}</text>
					</view>
					<view class="flex-start">
						<text class="btn" v-if="!hasFriend" @click="addDoctor()">关注</text>
						<text class="btn" v-else>已关注</text>
						<text class="btn" @click="sendpresent()">赠礼</text>
						<text class="btn" @click="ShareFriend()">分享</text>
					</view>
				</view>
			</view>
		</view>

		<view class="navlist">
			<navigator v-if="details.isremote"
				:url="'./inquiryNewOrder?details=' + encodeURIComponent(JSON.stringify(details))"
				class="nav flex-start">
				<image class="left" src="/static/img/doctors_img/nav1.png" mode=""></image>
				<view class="right">
					<view class="item1 space-between">
						<text class="title">图文咨询</text>
						<text class="price">￥{{ details.inquiryfee }}元/次</text>
					</view>
					<view class="item2"><text>通过文字、图片、语音和医生交流，进行线上咨询和问诊</text></view>
				</view>
			</navigator>
			<!-- <view class="nav flex-start" @click="tmptip()"> -->
			<navigator v-if="details.clinid && details.isreservation"
				:url="'./reservationNewOrder?details=' + encodeURIComponent(JSON.stringify(details))"
				class="nav flex-start">
				<image class="left" src="/static/img/doctors_img/nav2.png" mode=""></image>
				<view class="right">
					<view class="item1 space-between">
						<text class="title">预约挂号</text>
						<text class="price">￥{{ details.registerfee }}元/次</text>
					</view>
					<view class="item2"><text>预约近一周内的挂号</text></view>
				</view>
			</navigator>
			<!-- </view> -->
			<navigator v-if="details.is_family_doctor"
				:url="'./homeDoctor?details='+ encodeURIComponent(JSON.stringify(details))" class="nav flex-start">
				<image class="left" src="/static/img/doctors_img/nav1.png" mode=""></image>
				<view class="right">
					<view class="item1 space-between">
						<text class="title">私人医生</text>
						<text class="price">选购套餐</text>
					</view>
					<view class="item2">
						<text>建立长时间的医生远程咨询服务</text>
					</view>
				</view>
			</navigator>
			<navigator v-if="details.isvideocall"
				:url="'./videoCall?details='+ encodeURIComponent(JSON.stringify(details))" class="nav flex-start">
				<image class="left" src="/static/img/doctors_img/nav1.png" mode=""></image>
				<view class="right">
					<view class="item1 space-between">
						<text class="title">视频咨询</text>
						<text class="price">{{details.video_price.price}}元/{{details.video_price.duration}}分钟</text>
					</view>
					<view class="item2">
						<text>与医生面对面视频咨询</text>
					</view>
				</view>
			</navigator>
		</view>
		<view class="brief">
			<view class="title flex-start">
				<image src="/static/img/doctors_img/brief.png" mode=""></image>
				<text>擅长疾病</text>
			</view>
			<view class="label flex-start">
				<text v-for="te in details.brief">{{ te }}</text>
			</view>
		</view>
		<view class="desc">
			<view class="title flex-start">
				<image src="/static/img/doctors_img/desc.png" mode=""></image>
				<text>个人简介</text>
			</view>
			<view class="flex-start">
				<text class="ctx" v-if="details.description">{{ details.description }}</text>
			</view>
		</view>
		<view class="desc" v-if="details.present.length > 0">
			<view class="title flex-start">
				<image src="/static/img/doctors_img/gift.png" mode=""></image>
				<text>收到礼品</text>
			</view>
			<view class="flex-start received">
				<view class="gift" v-for="item in details.present">
					<view class="icon">
						<image :src="item.img" mode="widthFix"></image>
					</view>
					<text>×{{ item.total_num }}</text>
				</view>
			</view>
		</view>
		<view class="evaluation" v-if="evaluation.length">
			<view class="title flex-start">
				<image src="/static/img/doctors_img/evaluation.png" mode=""></image>
				<text>患者评价</text>
			</view>
			<view class="list flex-start" v-for="n in evaluation">
				<image class="left" :src="$baseUrl + n.face" mode=""></image>
				<view class="right">
					<view class="space-between">
						<view>
							<text class="name">{{ n.username }}</text>
							<text v-if="n.type == 0" class="type1">预约挂号</text>
							<text v-if="n.type == 1" class="type2">图文咨询</text>
							<text v-if="n.type == 2" class="type3">药品开方</text>
							<text v-if="n.type==3" class="type1">私人医生</text>
							<text v-if="n.type==4" class="type2">视频通话</text>
						</view>
					</view>
					<view>
						<image v-for="s in n.score" class="star" src="/static/img/doctors_img/star.png" mode=""></image>
					</view>
					<view>
						<text class="time">{{ n.time }}</text>
					</view>
					<view>
						<text class="ctx">{{ n.content }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: '',
				evaluation: [],
				details: {
					id: ''
				},
				hasFriend: 0,
				// #ifdef MP-WEIXIN
				$baseUrl: ''
				// #endif
			};
		},
		onLoad(option) {
			this.id = option.id;
			this.details.id = option.id;
			console.log(option);
			this.getDeateils();
			this.getevaluationlist();
		},
		created() {
			uni.removeStorageSync('doctor_id');
			// #ifdef MP-WEIXIN
			this.$baseUrl = getApp().globalData.$baseUrl;
			// #endif
			// #ifdef APP-PLUS
			plus.runtime.arguments = null;
			// #endif
		},
		methods: {
			//wx分享
			ShareFriend() {
				let info = this.details;
				console.log(info);
				let shareTitle = '';
				if (info.cliname) {
					shareTitle = shareTitle + info.cliname + '-';
				}
				if (info.departmentname) {
					shareTitle = shareTitle + info.departmentname + '-';
				}
				shareTitle = shareTitle + info.realname;
				uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					type: 0,
					// href: 'https://hs.xxjk99.com/weixinpat/?doctorId='+info.id,
					href: this.$baseUrl + 'patient/share.html?drId=' + info.id + '&userid=' + this.$store.state
						.userInfo.userid + '&type=2',
					title: shareTitle,
					summary: '主治：' + info.brief,
					imageUrl: this.$baseUrl + info.face,
					success: function(res) {
						console.log('success:' + JSON.stringify(res));
					},
					fail: function(err) {
						console.log('fail:' + JSON.stringify(err));
					}
				});
			},
			getDeateils() {
				uni.showLoading({
					title: '加载中',
					mask: true
				});
				this.$api
					.get('api/patient/doctor/getdetail', {
						params: {
							id: this.id
						}
					})
					.then(res => {
						uni.hideLoading();
						if (Object.prototype.toString.call(res) === '[object Array]') {
							uni.showModal({
								showCancel: false,
								title: '账号异常',
								content: '此医生账号已停用或注销！',
								success(res) {
									if (res.confirm) {
										uni.navigateBack();
									}
								}
							});
						} else {
							this.details = Object.assign({}, this.details, res);
							this.details.brief = this.details.brief.split(',');
							this.details.id = this.id;
							this.hasFriend = res.hasFriend;
						}
					})
					.catch(res => {
						uni.hideLoading();
					});
			},
			getevaluationlist() {
				this.$api
					.get('api/patient/doctor/getevaluationlist', {
						params: {
							id: this.id
						}
					}).then(res => {
						this.evaluation = res;
					}).catch(res => {});
			},
			addDoctor() {
				this.$api
					.post('api/patient/doctor/AddDoctor', {
						doctorId: this.id
					})
					.then(res => {
						if (res.success) {
							if (res.data.hasFriend) {
								uni.showToast({
									icon: 'none',
									title: '添加成功,在通讯录中可查看该医生'
								});
								this.hasFriend = res.data.hasFriend;
							}
						}
					}).catch(err => {
						console.log(err);
					});
			},
			tmptip() {
				uni.showToast({
					icon: "none",
					title: "本功能正在开发中....."
				})
			},
			sendpresent() {
				uni.navigateTo({
					url: 'confirmPresent?doctorId=' + this.id
				})
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;
	}

	.share_btn {
		position: absolute;
		right: 40rpx;
	}

	.details {
		background-color: $jian-bg-color;
		background: linear-gradient(-8deg, rgba(95, 231, 194, 1), rgba(88, 201, 238, 1));

		image {
			width: 80px;
			height: 60px;
			border-radius: $uni-border-radius-circle * 2;
		}

		color: $uni-text-color-inverse;
		font-size: $uni-font-size-base;

		.left {
			margin: $uni-spacing-col-lg 0px;
			margin-left: $uni-spacing-row-lg;
			margin-top: 20px;
		}

		.rigth {
			width: 100%;
			margin: $uni-spacing-col-lg $uni-spacing-row-sm;

			.flex-start {
				flex-wrap: wrap;
			}

			.btn {

				right: $uni-spacing-row-sm;
				border: 1px solid #ffffff;
				padding: $uni-spacing-col-sm $uni-spacing-row-sm;
				border-radius: $uni-border-radius-lg * 2;
				font-weight: 500;
			}
		}

		text {
			margin: $uni-spacing-col-sm $uni-spacing-row-sm;
		}

		.name {
			width: 100%;
			font-size: $uni-font-size-base;
			font-weight: bold;

			.realname {
				margin: $uni-spacing-col-sm $uni-spacing-row-sm;
				width: 70%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}

		.label {
			display: inline-block;
			border-radius: $uni-border-radius-lg;
			background-color: $uni-bg-color;
			color: #5fe7c2;
			padding: 2px 6px;
			font-size: $uni-font-size-sm;
		}
	}

	.received {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		width: 94%;
		background: #fff;
		box-sizing: border-box;
		margin: 3% auto;

		.gift {
			display: flex;
			align-items: center;
			margin-right: 20rpx;
			margin-top: 24rpx;

			.icon {
				width: 60rpx;
				height: 60rpx;
				overflow: hidden;
				margin-right: 10rpx;

				image {
					width: 100%;
				}
			}

			text {
				font-size: 28rpx;
				color: $jian-bg-color;
			}
		}
	}

	.navlist {
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-sm 0px;

		image {
			height: $uni-img-size-lg;
			width: $uni-img-size-lg;
		}

		.right {
			width: 100%;
		}

		.nav {
			font-size: $uni-font-size-sm;
			// color: $uni-text-color-grey;
			margin: $uni-spacing-col-lg $uni-spacing-row-lg;
			padding: $uni-spacing-col-lg $uni-spacing-row-lg;
			background: rgba(255, 255, 255, 1);
			box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.09);
			border-radius: $uni-border-radius-lg;

			text {
				margin: $uni-spacing-col-sm $uni-spacing-row-sm;
			}

			.title {
				font-size: $uni-font-size-base;
				color: $uni-text-color;
			}

			.price {
				font-size: $uni-font-size-base;
				color: #ffc02a;
			}
		}
	}

	.brief {
		background-color: $uni-bg-color;
		margin-top: $uni-spacing-col-lg;
		padding: $uni-spacing-row-lg 0px;

		.title {
			margin: 0px $uni-spacing-row-lg;

			image {
				height: $uni-img-size-sm;
				width: $uni-img-size-sm;
				margin-right: $uni-spacing-row-base;
			}

			text {
				font-size: $uni-font-size-base;
				color: $uni-text-color;
			}
		}

		.label {
			margin: 0px $uni-spacing-row-lg;
			margin-top: $uni-spacing-col-lg;
			flex-flow: row wrap;
			border-bottom: 1px solid $uni-border-color;

			text {
				display: inline-block;
				text-align: center;
				border: 1px solid $uni-border-color;
				// color: $uni-text-color-grey;
				font-size: $uni-font-size-base;
				padding: 4px 8px;
				border-radius: $uni-border-radius-lg * 2;
				margin: $uni-spacing-col-base 0px;
				margin-right: $uni-spacing-row-sm;
			}
		}
	}

	.desc {
		background-color: $uni-bg-color;
		padding: $uni-spacing-row-lg 0px;

		.title {
			margin: 0px $uni-spacing-row-lg;

			image {
				height: $uni-img-size-sm;
				width: $uni-img-size-sm;
				margin-right: $uni-spacing-row-base;
			}

			text {
				font-size: $uni-font-size-base;
				color: $uni-text-color;
			}
		}

		.ctx {
			margin: 0px $uni-spacing-row-lg;
			margin-top: $uni-spacing-col-lg;
			// color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
			border-bottom: 1px solid $uni-border-color;
		}
	}

	.evaluation {
		background-color: $uni-bg-color;
		padding: $uni-spacing-row-lg 0px;

		.title {
			margin: 0px $uni-spacing-row-lg;

			image {
				height: $uni-img-size-sm;
				width: $uni-img-size-sm;
				margin-right: $uni-spacing-row-base;
			}

			text {
				font-size: $uni-font-size-base;
				color: $uni-text-color;
			}
		}

		.list {
			// margin: 0px $uni-spacing-row-lg;
			margin-top: $uni-spacing-col-lg;

			.left {
				height: 50px;
				width: 50px;
				margin: 0px $uni-spacing-row-sm;
			}

			.right {
				width: calc(100% - 60px);
				border-bottom: 1px solid $uni-border-color;
				padding-bottom: $uni-spacing-col-base;
				margin-right: $uni-spacing-row-lg;

				.space-between {
					flex-wrap: wrap;
				}

				.name {
					color: #1e1e1e;
					font-size: $uni-font-size-base;
					font-weight: bold;
				}

				.type1 {
					background-color: #dcfdfe;
					font-size: $uni-font-size-sm;
					color: #48d3de;
					padding: 2px 4px;
					margin: 0px $uni-spacing-row-sm;
				}

				.type2 {
					background-color: #dcfbf2;
					font-size: $uni-font-size-sm;
					color: #4ce1a5;
					padding: 2px 4px;
					margin: 0px $uni-spacing-row-sm;
				}

				.type3 {
					background-color: #fcf5ea;
					font-size: $uni-font-size-sm;
					color: #f8c578;
					padding: 2px 4px;
					margin: 0px $uni-spacing-row-sm;
				}

				.star {
					height: 10px;
					width: 10px;
					margin-right: 5px;
				}

				.time {
					color: $uni-text-color-grey;
					font-size: $uni-font-size-sm;
				}

				.ctx {
					color: #595959;
					font-size: $uni-font-size-sm;
					word-break: break-all;
				}
			}
		}
	}

	.status_bar {
		height: 70px;
		width: 100%;
	}
</style>