<script>
	// #ifdef APP-PLUS
	import APPUpdate from '@/common/appUpdate.js';
	// #endif
	// #ifdef MP-WEIXIN
	import Vue from 'vue';
	// #endif
	import TIM from 'tim-wx-sdk';
	import COS from 'cos-wx-sdk-v5';
	import permision from '@/js_sdk/wa-permission/permission.js';
	let timer
	export default {
		data() {
			return {
				flag: false,
				updateinfo: {},
				loginfailnum: 0,
				isOpenImInit: false,
				isWebsoket: false, //websoket是否成功链接
				ordertimeout: false, //订单时间是否结束
				offlinecallinfo: '' //后台运行收到离线推送时保存的信息
			};
		},

		mounted() {
			/**官网有很多关于关于sdk 其他的监听方法（比如：有新的消息，用户资料更新等等）
			 * 详情可对照： https://imsdk-**********.file.myqcloud.com/IM_DOC/Web/SDK.html
			 * 监听的含义：服务端发生了数据变更---前端全局可以接收到变更通知--前端就可以自动触发某个事件来更新相应数据
			 * */
			let that = this;
			this.$api.get('patient/update.json').then(res => {
				this.updateinfo = res
				if (res.yunstore != undefined) {
					this.$store.commit('updateyunstore', {
						"yunstore": res.yunstore,
						"yunstorebk": res.yunstorebk
					})
				}
				if (res.helper != undefined) {
					this.$store.commit('updatehelper', res.helper)
				}
				let token = uni.getStorageSync('token')
				if (token) {
					this.$api.post("api/patient/user/isConnection", {
							newToken: true,
							cid: plus.push.getClientInfo().clientid
						})
						.then(res => {
							if (res.success) {
								uni.setStorageSync('token', "Bearer " + res.data.token)
								this.fileInfo();
							} else {
								uni.showModal({
									title: '服务器异常',
									content: '服务器连接异常，请稍后重试',
									showCancel: false
								})
							}
						})
						.catch(err => {
							console.log(err);
						});
				} else {
					uni.reLaunch({
						url: '/pages/login/login'
					})
				}
			});
			if (uni.getSystemInfoSync().platform == 'android') {
				this.getAndriodPushSound()
			}
		},
		methods: {
			//清理缓存
			clearCache(day) {
				let clearCacheTime = uni.getStorageSync('clearCacheTime');
				if (clearCacheTime) {
					let date = new Date();
					let DayInterval = (date.getTime() - clearCacheTime.getTime()) / (1000 * 60 * 60 * 24);
					if (DayInterval >= day) {
						plus.cache.clear();
						clearCacheTime = date;
						uni.setStorageSync('clearCacheTime', clearCacheTime);
					}
				} else {
					clearCacheTime = new Date();
					uni.setStorageSync('clearCacheTime', clearCacheTime);
				}
			},
			onReadyStateUpdate({
				name
			}) {
				console.log(name);
				const isSDKReady = name === this.$TIM.EVENT.SDK_READY ? true : false;
				//自动监听并更新 sdk 的ready 状态 （未登录是 notReady  登录后是ready）
				this.$store.commit('toggleIsSDKReady', isSDKReady);
				//sdk ready 后  肯定完成了登录操作    这里可以获取用户存储在im的基础信息/离线消息/黑名单列表
			},

			onReceiveMessage({
				data: messageList
			}) {
				// this.handleAt(messageList);
				this.$store.commit('pushCurrentMessageList', messageList);
			},
			async requestIosPermission(permisionID, showText) {
				var result = await permision.judgeIosPermission(permisionID);
				if (result) {
					console.log('已获取权限');
				} else {
					let content = '未获取到' + showText + '，可能会影响到您的正常使用，请前往设置';
					uni.showModal({
						title: '提示',
						content: content,
						success: function(res) {
							if (res.confirm) {
								permision.gotoAppPermissionSetting();
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}
				return result;
			},
			async requestAndroidPermission(permisionID, showText) {
				var result = await permision.requestAndroidPermission(permisionID);
				if (result == 1) {
					console.log('已获取权限');
				} else {
					let content = '未获取到' + showText + '，可能会影响到您的正常使用，请前往设置';
					uni.showModal({
						title: '提示',
						content: content,
						success: function(res) {
							if (res.confirm) {
								permision.gotoAppPermissionSetting();
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}
				return result;
			},
			navtopages(isGoHome) {
				let that = this
				// 判断登录状态
				// #ifdef APP-PLUS||MP-WEIXIN
				const isLogin = uni.getStorageSync('token');
				// #endif
				// #ifdef H5
				const isLogin = getApp().globalData.token;
				// #endif
				//跳转至app某页
				if (isGoHome && isLogin) {
					uni.switchTab({
						url: '/pages/index/index'
					})
				}
				setTimeout(function() {
					// #ifdef APP-PLUS
					let args = plus.runtime.arguments;
					if (typeof args != "string" || args.indexOf('?') == -1) {
						return
					}
					let navUrl;
					if (args) {
						let plusinfo = that.getUrlParams(args)
						if (plusinfo.drId) {
							navUrl = '/pages/doctors/doctorsDetails?id=' + plusinfo.drId;
						} else if (plusinfo.hospitalId) {
							if (plusinfo.type == 'join') {
								uni.showModal({
									title: '会员邀请',
									content: '是否成为' + decodeURIComponent(plusinfo.cliname) + "的会员",
									success(res) {
										plus.runtime.arguments = ''
										if (res.confirm) {
											that.$api
												.post('api/patient/vip/addvip', {
													id: plusinfo.hospitalId, //诊所id
													status: 2, //2 同意 3 拒绝
												})
												.then(res => {
													if (res.success) {
														uni.showToast({
															icon: 'none',
															title: `您已成为${plusinfo.cliname}的的会员`
														});

													} else {
														uni.showToast({
															icon: 'none',
															title: res.msg
														});
													}
													uni.switchTab({
														url: '/pages/index/index'
													})
												});
										} else {
											uni.switchTab({
												url: '/pages/index/index'
											})
										}
									}
								})
							} else {
								navUrl = '/pages/hospitals/hospitalDetail/hospitalDetail?id=' + plusinfo
									.hospitalId;
							}
						}
						if (isLogin && navUrl) {
							uni.navigateTo({
								url: navUrl
							});
						}
					}
					// #endif
				}, 500);
			},
			getUrlParams(url) {
				// 通过 ? 分割获取后面的参数字符串
				let urlStr = url.split("?")[1];
				// 创建空对象存储参数
				let obj = {};
				// 再通过 & 将每一个参数单独分割出来
				let paramsArr = urlStr.split("&");
				for (let i = 0, len = paramsArr.length; i < len; i++) {
					// 再通过 = 将每一个参数分割为 key:value 的形式
					let arr = paramsArr[i].split("=");
					obj[arr[0]] = arr[1];
				}
				return obj;
			},
			// 初始化目录
			fileInfo() {
				let _this = this;
				plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function(fs) {
					fs.root.getDirectory(
						"user", {
							create: true,
						},
						(entry) => {
							_this.initOpenIMSDK(entry.fullPath);
						},
						(error) => {
							console.log(error);
						}
					);
				});
			},
			// 初始化SDK
			initOpenIMSDK(dbDir) {
				let that = this
				const platformID = uni.getSystemInfoSync().platform == 'ios' ? 1 : 2
				const options = {
					"apiAddr": this.updateinfo.open_ipApi, //IM的API接口地址
					"wsAddr": this.updateinfo.open_ipWs, //IM的websocket地址
					"platformID": platformID, //平台类型
					"dataDir": dbDir, //数据存储路径
					"logLevel": 6, //日志打印级别
					"logFilePath": dbDir, //日志存储的目录
					"isLogStandardOutput": true //是否输出到控制台
				};
				// 初始化IM
				if (that.isOpenImInit) {
					that.getloginstatus()
				} else {
					// that.getloginstatus()
					that.$IMSDK.asyncApi("initSDK", Date.now().toString(), options).then(res => {
						if (res) {
							that.getloginstatus()
						} else {

						}
					}).catch(err => {
						console.log(err)
					})
				}
			},

			//监听回调
			setInitListener() {
				let that = this
				// 监听链接状态
				this.$IMSDK.subscribe(this.$IMSDK.IMEvents.OnConnectFailed, ({
					errCode,
					errMsg
				}) => {
					console.log('链接失败：', errMsg)
				});
				this.$IMSDK.subscribe(this.$IMSDK.IMEvents.OnConnectSuccess, () => {
					// console.log('链接成功：')
				});
				this.$IMSDK.subscribe(this.$IMSDK.IMEvents.OnKickedOffine, () => {
					console.log('被踢下线：')
					uni.showToast({
						title: '您的帐号已在另一台设备登录',
						icon: 'none'
					});
					uni.clearStorageSync();
					uni.reLaunch({
						url: '/pages/login/login'
					});
				});
				// 会话列表
				this.$IMSDK.subscribe(
					this.$IMSDK.IMEvents.OnNewConversation,
					({
						data
					}) => {
						console.log('新的会话', data);
					}
				);

				// 未读消息数量发生改变
				this.$IMSDK.subscribe(this.$IMSDK.IMEvents.OnTotalUnreadMessageCountChanged, (
					params
				) => {
					// data 消息未读数
					if (params.data) {
						that.getunread()
					} else {
						uni.removeTabBarBadge({
							index: 2
						})
					}
				});
			},
			// 获取登录状态，避免重复登录的bug
			async getloginstatus() {
				let that = this
				const islogin = await this.$IMSDK.asyncApi('getLoginStatus', Date.now().toString())
				if (islogin == 3) {
					uni.switchTab({
						url: '/pages/index/index'
					})
				} else {
					that.sdkLogin()
				}
			},
			// sdk登录
			sdkLogin() {
				let that = this
				that.$api.post('api/patient/user/loginim', {
					platform: uni.getSystemInfoSync().platform == 'ios' ? 1 : 2
					// dev: 1
				}).then(data => {
					if (data.success) {
						uni.showLoading({
							title: '加载中'
						})
						// openIM登录
						that.$IMSDK.asyncApi('login', Date.now().toString(), {
							userID: data.data.userID,
							token: data.data.token,
						}).then(res => {
							console.log('登录成功', res)
							uni.hideLoading()
							that.setInitListener()
							that.loginfailnum = 0
							that.isOpenImInit = true;
							if (!plus.runtime.arguments) {
								uni.switchTab({
									url: '/pages/index/index'
								})
							} else {
								if (typeof plus.runtime.arguments != "string" || plus.runtime.arguments
									.indexOf('?') ==
									-1) {
									uni.switchTab({
										url: '/pages/index/index'
									})
								} else {
									that.navtopages(1)
								}
							}
							that.getunread()
						}).catch(err => {
							uni.hideLoading()
							console.log(err)
							if (that.loginfailnum <= 2) {
								that.loginfailnum = that.loginfailnum + 1
								that.getloginstatus()
							} else {
								uni.showModal({
									title: '维护中2',
									content: '服务器维护中，暂时无法使用，请耐心等待',
									showCancel: false,
									success() {
										plus.runtime.quit();
									}
								})
							}
						})

					} else {
						uni.showModal({
							title: '维护中',
							content: '服务器维护中，暂时无法使用，请耐心等待',
							showCancel: false,
							success() {
								plus.runtime.restart();
							}
						});
					}
				})

			},
			getunread() {
				let that = this
				that.$IMSDK.asyncApi('getTotalUnreadMsgCount', Date.now().toString())
					.then((
						res
					) => {
						// 调用成功
						let num = res.data
						that.$IMSDK.asyncApi('getOneConversation', Date.now().toString(), {
								sourceID: 'xxjk_serv',
								sessionType: 1
							})
							.then((
								data
							) => {
								// 调用成功
								let kfnum = data.data.unreadCount
								num = (num - kfnum) > 0 ? (num - kfnum) : 0
								num = num.toString()
								if (num != '0') {
									uni.setTabBarBadge({
										index: 2,
										text: num,
									})
								}
							})
							.catch(({
								errCode,
								errMsg
							}) => {
								// 调用失败
							});
					})
					.catch(({
						errCode,
						errMsg
					}) => {
						// 调用失败
					});
			},
			getAndriodPushSound() {
				const plugin = uni.requireNativePlugin("DCloud-PushSound");
				plugin.setCustomPushChannel({
					soundName: 'ring',
					channelId: '112752',
					channelDesc: '音视频通话离线推送',
					enableLights: true,
					enableVibration: true,
					importance: 4,
					lockscreenVisibility: 0
				}, err => {
					console.log(err)
				});
				plugin.getAllChannels((p) => {
					console.log("channels :", p); //返回数组
				})
				plugin.testNotification({
					channelId: "111346" //渠道id
				});
			},
			async onVideoCall(data) {
				if (uni.getSystemInfoSync().platform == 'ios') {
					let result = await getApp().requestIosPermission('camera', '摄像头权限')
					if (!result) {
						return
					}
				} else {
					let result = await getApp().requestAndroidPermission('android.permission.CAMERA', '摄像头权限')
					if (result != 1) {
						return
					}
				}
				let that = this
				let nowTime = new Date().getTime();
				if (Math.abs(nowTime - data.time) > 60000) {
					uni.showToast({
						title: '您有一条来自' + data.name + '的未接通视频通话',
						icon: 'none',
						duration: 3000
					})
					return
				}
				this.$api.post('api/patient/videocall/gettoken', {
					doctor: data.id,
					issingle: 0
				}).then(res => {
					if (res.success) {
						// 建立连接
						let soketOn = this.connectSocket(res.url.ws, res.data.token.token.ws)
						that.$YeIMCallKit.answerC2CVideoCall({
							// 先固定后面替换为接口返回
							wsURL: res.url.livekiturl,
							token: res.data.token.token.livekit,
							userInfo: {
								nickname: that.$store.state.userInfo.patname,
								avatar: that.$store.state.userInfo.face,
								identity: res.data.token.to,
								userId: res.data.token.to
							},
							callerUserInfo: {
								nickname: data.name,
								avatar: that.$baseUrl + data.avatar,
								identity: res.data.token.from,
								userId: res.data.token.from
							},
							videoOptions: {
								encoding: 'HD'
							},
							callRing: '/static/audio/ring.mp3',
							answerRing: '/static/audio/ring.mp3'
						}, (e) => {
							console.log('callstatus', e)
							if (e.code == 20001) {
								// 接听后循环发送ws消息
								that.sendWsMsg()
							} else {
								that.hangup(res.data.token.to, res.data.token.from, res.data.inquiry, e
									.code)
							}
						});
					}
				})
			},
			hangup(patient, doctor, inquiry, code) {
				let that = this
				uni.sendSocketMessage({
					data: '2',
					complete: () => {
						console.log(123456)
						that.isWebsoket = false
						uni.closeSocket()
						clearInterval(timer)
					}
				})
				this.$api.post('api/patient/videocall/hangup', {
					patient: patient,
					doctor: doctor,
					inquiry: inquiry
				}).then(res => {
					let title
					if (code == 10003) {
						title = "通话链接异常"
					} else if (code == 10005) {
						title = "通话结束"
					} else if (code == 10006) {
						if (that.ordertimeout) {
							title = "您的通话时长已结束"
							that.ordertimeout = false
							that.downCallback()
						} else {
							title = "通话断开"
						}
					} else if (code == 20002) {
						title = "通话结束"
					} else if (code == 20003) {
						title = "对方无应答"
					}
					if (title) {
						uni.showToast({
							title: title,
							icon: 'none',
							duration: 3000
						})
					}
				})
			},
			async connectSocket(url, token) {
				let that = this
				uni.connectSocket({
					url: url + '?Token=' + token,
					// url: url,
					header: {
						'content-type': 'application/json'
					},
					method: 'GET',
					success: (res) => {},
					fail: (err) => {
						console.log(err)
					}
				});
				uni.onSocketOpen(function(res) {
					console.log('WebSocket连接已打开！');

				});
				uni.onSocketError(function(res) {
					console.log('WebSocket连接打开失败，请检查！');
				})
				uni.onSocketMessage(function(res) {
					console.log('收到服务器内容：', JSON.parse(res.data));
					let data = JSON.parse(res.data)
					if (data.code == 1000) {
						console.log("ws连接成功")
					} else if (data.code == 2000) {
						console.log(JSON.parse(res.data))
					} else if (data.code == 2001) {
						that.ordertimeout = true
					} else {
						clearInterval(timer)
						uni.closeSocket()
					}
				});

			},
			sendWsMsg() {
				let that = this
				that.isWebsoket = true
				timer = setInterval(() => {
					uni.sendSocketMessage({
						data: '1',
						success(res) {
							console.log(res)
							if (!that.isWebsoket) {
								uni.closeSocket()
								clearInterval(timer)
							}
						},
						fail(err) {
							console.log(err)
						}
					})
				}, 2000);
			},
			initPush() {
				let that = this
				uni.onPushMessage((res) => {
					console.log("收到推送消息：", res)
					let newstr = res.data.payload.replace(/\\{3}/g, "\\")
					console.log(newstr)
					if (res.type == 'receive') {
						let payloadInfo = JSON.parse(newstr)
						if (payloadInfo.type == 20) {
							if (that.$store.state.apptabstatus == 2) {
								that.offlinecallinfo = {
									id: payloadInfo.id,
									name: payloadInfo.name,
									avatar: payloadInfo.avatar,
									time: payloadInfo.time * 1000
								}
								uni.createPushMessage({
									title: res.data.title,
									content: res.data.content,
									success(res) {

									}
								})
							} else {
								that.onVideoCall({
									id: payloadInfo.id,
									name: payloadInfo.name,
									avatar: payloadInfo.avatar,
									time: payloadInfo.time * 1000
								})
							}
						} else {
							uni.createPushMessage({
								title: res.data.title,
								content: res.data.content,
								success(res) {

								}
							})
							that.$store.state.unreadtotal++;
							if (that.$store.state.unreadtotal) {
								uni.setTabBarBadge({
									index: 1,
									text: that.$store.state.unreadtotal.toString()
								});
								uni.$emit('updateMsg');
								uni.$emit('updateInquiry');
								uni.$emit('updateCommlist');
							}
						}
					} else if (res.type == 'click') {
						console.log('离线推送', res)
						let payloadInfo
						if (uni.getSystemInfoSync().platform == 'ios') {
							payloadInfo = JSON.parse(JSON.parse(res.data.payload))
						} else {
							payloadInfo = JSON.parse(JSON.parse('"' + newstr + '"'))
						}

						if (payloadInfo.type == 20) {
							that.onVideoCall({
								id: payloadInfo.id,
								name: payloadInfo.name,
								avatar: payloadInfo.avatar,
								time: payloadInfo.time * 1000
							})
						} else {
							uni.switchTab({
								url: '/pages/msg/msg'
							})
							uni.$emit('updateMsg');
						}
					}

				})
			}
		},
		onLaunch: function() {
			const that = this;
			uni.getPushClientId({
				success: (res) => {
					console.log('客户端推送标识:', res)
				},
				fail(err) {
					console.log(err)
				}
			})
			//清理内存单位天
			//this.clearCache(7)
			// #ifdef APP-PLUS
			APPUpdate();
			// #endif

			console.log('App Launch');
			uni.getSystemInfo({
				success: function(e) {
					// #ifndef MP
					Vue.prototype.StatusBar = e.statusBarHeight;
					if (e.platform == 'android') {
						Vue.prototype.CustomBar = e.statusBarHeight + 50;
					} else {
						Vue.prototype.CustomBar = e.statusBarHeight + 45;
					}
					// #endif

					// #ifdef MP-WEIXIN
					Vue.prototype.StatusBar = e.statusBarHeight;
					let custom = wx.getMenuButtonBoundingClientRect();
					Vue.prototype.Custom = custom;
					Vue.prototype.CustomBar = custom.bottom + custom.top - e.statusBarHeight;
					// #endif

					// #ifdef MP-ALIPAY
					Vue.prototype.StatusBar = e.statusBarHeight;
					Vue.prototype.CustomBar = e.statusBarHeight + e.titleBarHeight;
					// #endif
				}
			});
		},
		onShow: function() {
			console.log('App Show');
			this.$store.commit('updateapptabstatus', 1)
			let that = this
			plus.push.clear()
			this.navtopages(0);
			if (that.offlinecallinfo) {
				that.onVideoCall(that.offlinecallinfo)
				that.offlinecallinfo = ''
			}
			that.$api.get('api/patient/user/getUnreadMessage').then(res => {
				console.log(res)
				if (res.success) {
					if (res.data.count) {
						var pages = getCurrentPages();
						if (pages[pages.length - 1].route != 'pages/msg/msg') {
							that.$store.commit('updateUnreadtotal', res.data.count);
							uni.setTabBarBadge({
								index: 1,
								text: res.data.count.toString()
							});
						} else {
							uni.$emit('updateMsg');
						}
					}
				}
			})
		},
		onHide: function() {
			this.$store.commit('updateapptabstatus', 2)
			console.log('App Hide');
		},
		onError: function(err) {
			console.log(err);
			var pages = getCurrentPages();
			let pageRoute = '';
			if (pages.length > 0) {
				var page = pages[pages.length - 1];
				// #ifdef APP-PLUS
				var currentWebview = page.$getAppWebview();
				pageRoute = currentWebview.__uniapp_route;
				// #endif
			}
			let log = {
				message: err.message,
				stack: err.stack,
				route: pageRoute
			};
			// #ifdef APP-NVUE
			let crashTool = uni.requireNativePlugin('CL-CrashTool');
			if (crashTool) {
				crashTool.sendReportException(JSON.stringify(log));
			}
			// #endif
		}
	};
</script>

<style>
	/*每个页面公共css */
	/*每个页面公共css */
	@import '@/static/css/iconfont.css';

	page {
		background-color: #ffffff;
	}

	.space-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: $uni-text-color;
	}

	.space-between-start {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
	}

	.space-between-center {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.flex-start {
		display: flex;
		justify-content: flex-start;
		align-items: flex-start;
	}

	.flex-start-center {
		display: flex;
		justify-content: flex-start;
		align-items: center;
	}

	.flex-end-center {
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}

	.space-around {
		display: flex;
		justify-content: space-around;
		align-items: flex-start;
	}
</style>