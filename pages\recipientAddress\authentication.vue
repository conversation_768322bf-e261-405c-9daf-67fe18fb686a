
<!-- 实名认证提交页面 -->
<template>
	<view>
		<uni-list class="tabs" id="tabs2">
			<view>
				<uni-list-item title="姓名" :showArrow="false">
					<template v-slot:right="">
					  <input class="getleft" :disabled="status==0" type="text" v-model="info.realname" value=""  placeholder="请输入您的真实姓名" />
					</template>
				</uni-list-item>
			</view>
			<view>
			<uni-list-item title="身份证" :showArrow="false">
					<template v-slot:right="">
						<input class="getleft" :disabled="status==0" type="text" v-model="info.id_card_num" value=""  placeholder="请输入您的身份证号码" />
					</template>
				</uni-list-item>
			</view>
			<view>
				<navigator url="./verifyexample">
					<uni-list-item title="上传身份证照片" rightText="查看示例"></uni-list-item>
				</navigator>				
			</view>
		</uni-list>
		<view class="flex-start">
			<view class="border">
				<image class="idCard" @click="updateimg('id_card_img')" :src="info.id_card_img" mode=""></image>
				<view class="tip"><text class="tip">身份证头像面</text></view>
			</view>
			<view class="border">
				<image class="idCard" @click="updateimg('id_card_img_back')" :src="info.id_card_img_back" mode=""></image>
				<view class="tip"><text>身份证国徽面</text></view>
			</view>
		</view>
		<view >
			<view ><button v-show="status==-1" class="btn" @click="checkAuth" type="default">提交审核</button></view>
		</view>
		<view v-show="status==0">
			<view class="await"><text>审核中</text></view>
		</view>
		<view v-show="status==-1">
			<view class="prompt"><text>1.请确保证件照片上的姓名、头像、身份证号码、有效期限清晰可见；</text></view>
			<view class="prompt"><text>2.上传证件信息仅用于身份认证，医生和第三方不可见；</text></view>
			<view v-show="'undefined' != typeof reason" class="await"><text>{{reason}}</text></view>
		</view>
		<view v-show="status==1">
			<view class="await"><text>审核已通过</text></view>
		</view>
	</view>
</template>

<script>
import uniList from '@/components/uni-list/uni-list.vue';
import uniListItem from '@/components/uni-list-item/uni-list-item.vue';
import { pathToBase64, base64ToPath } from 'image-tools';
export default {
	components: { uniList, uniListItem },
	data() {
		return {
			
			info: {
				id_card_img: '/static/img/idcard.png', //身份证正面，base64的图片字符串，最大1M
				id_card_img_back: '/static/img/idcard2.png', //身份证反面，base64的图片字符串，最大1M
				id_card_num: '', //身份证号码
				realname: '', //姓名
				// telephone: '' //联系电话
			},
			allow:"" , //审核状态 0代表不允许登录,1代表允许登录，2：待提交审核；3：等待审核； 4：官方认证的医生半禁用（因他关联的医院医生子账户资源到期）
			status:"", //0待审核 1审核通过 -1驳回 或 未提交
			reason:"", //未通过审核的理由
			docid:"", //医生id
		};
	},
	onBackPress(options) {
		if (options.from === 'navigateBack') {
			return false;
		}
		uni.switchTab({
			url: '../index/index'
		});
		return true;
	},
	created(){
		try {
		    const value = uni.getStorageSync('auth');
		    if (value) {
		       this.info = Object.assign({},that.info,JSON.parse(value))
		    }
		} catch (e) {
		    // error
		}
		let that = this
		this.$api
			.get('api/v2/patient/user_review/review')
			.then(res=>{
				if(res.data==null){
					that.status = -1
				}else if(res.data.status==1||res.data.status==0||res.data.status==-1){
					that.status = res.data.status;
					console.log(res.data);
					that.reason = res.data.reason;
					console.log(res.data.reason);
					uni.$emit('updateAuthStatus',res.data.status);
				}
				if(!that.info.id_card_num&&res.data!=null){
					that.info = res.data
					uni.setStorageSync('auth',that.info)
				}
			})
	},
	onShow() {
		// 保存内容在本地硬盘
		// this.status = this.$store.state.userInfo.status
		
	},
	methods: {
		updateimg(val) {
			getApp().requestAndroidPermission('android.permission.WRITE_EXTERNAL_STORAGE', '存储权限')
			if(this.status==0){
				return
			}
			let that = this;
			uni.chooseImage({
				count: 1, //默认9
				sizeType: ['compressed'], //可以指定是原图还是压缩图，默认二者都有
				// sourceType: ['camera '], //从相册选择
				success: function(res) {
					uni.compressImage({
						src: res.tempFilePaths[0],
						quality: 20,
						success: res => {
							pathToBase64(res.tempFilePath)
								.then(base64 => {
									that.info[val] = base64;
								})
								.catch(error => {
									console.error(error);
								});
						}
					});
				}
			});
		},
		
		checkAuth() {
			if (!/^([\u4e00-\u9fa5·]{2,16})$/.test(this.info.realname)) {
				uni.showModal({
					title: '温馨提示',
					content: '姓名的格式是汉字，长度必须在2和16',
					showCancel:false
				});
				return;
			}
				if (!/^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$/.test(this.info.id_card_num)) {
							uni.showModal({
								title: '温馨提示',
								content: '身份证号码有误',
								showCancel:false
						 	});
							return;
						}
			if (!/^data:image\/(jpeg|png|gif);base64,/.test(this.info.id_card_img)) {
				uni.showModal({
					title: '温馨提示',
					content: '身份证头像面没有上传',
					showCancel:false
				});
				return;				
			}
			// console.log(this.info.idCard,2222)
			if (!/^data:image\/(jpeg|png|gif);base64,/.test(this.info.id_card_img_back)) {
				uni.showModal({
					title: '温馨提示',
					content: '身份证国徽面没有上传',
					showCancel:false
				});
				return;
			}
			uni.setStorageSync("auth",JSON.stringify(this.info))
			this.$api
				.post('api/v2/patient/user_review/review', this.info)
				.then(res => {
					console.log(res,222222)
					let data = res;
					if (res.errno==0) {
						this.status =0;
						uni.$emit('updateAuthStatus',this.status);
					}else
						uni.$emit('updateAuthStatus',-1);
					uni.showModal({
						title: '温馨提示',
						content: res.msg,
						success: function(res) {
							if (res.confirm) {
								if (data.errno==0) {
									uni.switchTab({
										url: '../index/index'
									});
								}
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				})
				.catch(err => {
					console.log(err);
				});
		},
	}
};
</script>


<style lang="scss">
.desc {
}
.btn{
	width: 92vw;
	height: 40x;
	border-radius: 10px;
	background-color: green;
	margin: 0 auto;
}
.idCard {
	width: 100%;
	height: 100px;
}
.border {
	// display: flex;
	// justify-content: center;
	// align-items:center;
	width: 44%;
	margin-left: $uni-spacing-row-lg;
	margin-bottom: $uni-spacing-row-base;
	.tip {
		font-size: $uni-font-size-sm;
		color: $uni-text-color-grey;
		text-align: center;
	}
}
.btn {
	margin: $uni-spacing-col-lg $uni-spacing-row-lg;
	background-color: #02cfc9 !important;
	color: $uni-text-color-inverse !important;
}
.prompt {
	font-size: $uni-font-size-sm;
	color: $uni-text-color-grey;
	margin-left: $uni-spacing-row-lg;
}
.await{
	font-size: $uni-font-size-lg;
	color:#DD524D;
	margin-left: $uni-spacing-row-lg;
}
	
.getleft{
	width: 600rpx;
}
</style>
