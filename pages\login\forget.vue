<template>
	<view class="forget">
		
		<view class="content">
			<!-- 主体 -->
			<view class="main">
				<view class="tips">若你忘记了密码，可在此重置新密码。</view>
				<wInput
					v-model="phoneData"
					type="text"
					maxlength="11"
					placeholder="请输入手机号码"
				></wInput>
				<wInput
					v-model="passData"
					type="password"
					maxlength="16"
					placeholder="请输入新密码"
					isShowPass
				></wInput>
				
				<wInput
					v-model="verCode"
					type="number"
					maxlength="6"
					placeholder="验证码"
					
					isShowCode
					codeText="获取重置码"
					setTime="30"
					ref="runCode"
					@setCode="getVerCode()"
				></wInput>
			</view>
			
			<wButton 
				text="重置密码"
				:rotate="isRotate" 
				@click.native="startRePass()"
			></wButton>

		</view>
	</view>
</template>

<script>
	var _this;
	import wInput from '../../components/watch-login/watch-input.vue' //input
	import wButton from '../../components/watch-login/watch-button.vue' //button
	export default {
		data() {
			return {
				phoneData: "", //电话
				passData: "", //密码
				verCode:"", //验证码
				isRotate: false, //是否加载旋转
			}
		},
		components:{
			wInput,
			wButton
		},
		mounted() {
			_this= this;
		},
		methods: {
			getVerCode(){
				//获取验证码
				if (!(/^1[3|4|5|7|8|9][0-9]{9}$/.test(this.phoneData))) {
				     uni.showToast({
				        icon: 'none',
						position: 'bottom',
				        title: '手机号码格式不正确或为空'
				    });
				    return false;
				}
				this.$refs.runCode.$emit('runCode'); 
				setTimeout(function(){
					_this.$refs.runCode.$emit('runCode',0); 
				},60000)
				this.$api
					.get('api/patient/getsmscode', { params: { telephone: this.phoneData } })
					.then(res => {})
					.catch(err => {});
			},
			startRePass() {
				//重置密码
				if(this.isRotate){
					//判断是否加载中，避免重复点击请求
					return false;
				}
				if (!(/^1[3|4|5|7|8|9][0-9]{9}$/.test(this.phoneData))) {
				    uni.showToast({
				        icon: 'none',
						position: 'bottom',
				        title: '手机号码格式不正确或为空'
				    });
				    return false;
				}
			    if (!(/^.*(?=.{6,})(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*? ]).*$/.test(this.passData))) {
			        uni.showToast({
			            icon: 'none',
						position: 'bottom',
			            title: '密码最少6位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符'
			        });
			        return false;
			    }
				if (!(/^\d{6}$/.test(this.verCode))) {
				    uni.showToast({
				        icon: 'none',
						position: 'bottom',
				        title: '验证码6位，只包括数字'
				    });
				    return false;
				}
				_this.isRotate=true
				setTimeout(function(){
					_this.isRotate=false
				},3000)
				this.$api.post('api/patient/auth/resetpassword',{
					telephone:this.phoneData,
					smscode:this.verCode,
					password:this.passData
				}).then(res =>{
					uni.showModal({
						title: '温馨提示',
						content:res.success?"密码重置成功":res.msg,
						success: function(data) {
							if (data.confirm) {
								if(res.success){
									if(uni.getStorageSync('token')){
										uni.navigateBack()
									}else{
									uni.navigateTo({
										url:"./login"
									})
									}
									
								}
							} else if (data.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}).catch(err =>{
					
				})
			}
		}
	}
</script>

<style>
	@import url("../../components/watch-login/css/icon.css");
	@import url("./css/main.css");
</style>

