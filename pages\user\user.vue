<template>
	<view id="user">
		<!-- <image class="bg" src="/static/img/userbg.png" mode=""></image> -->
		<!--S 用户信息 -->
		<navigator hover-class="none"
			:url="'/pages/user/userDetails?userInfo=' + encodeURIComponent(JSON.stringify(userInfo))" id="gai"
			class="space-between">
			<view class="flex-start">
				<image class="avatar" :src="userInfo.face" mode=""></image>
				<view>
					<view class="flex-start item1">
						<text>{{ userInfo.patname?userInfo.patname:'无' }}</text>
					</view>
					<view class="point">
						<!-- <image :src="'https://shop.xxjk99.com/upload/shop/'+userInfo.credit_img" mode="widthFix"></image> -->
						<text>{{userInfo.credit_title}}</text>
					</view>
					<view class="flex-start item2">
						<text>{{ userInfo.telephone | getlast }}</text>
					</view>
				</view>
			</view>
			<image class="back" src="../../static/img/back.png" mode=""></image>
		</navigator>
		<!--E 用户信息 -->
		<!--S 订单 -->
		<view id="order" class="flex-start">
			<navigator class="orderitem" url="../order/index?tabIndex=1" hover-class="none">
				<image class="orderimg" src="../../static/img/order1.png" mode=""></image>
				<text id="navtext">待付款</text>
				<uni-badge class="badge" size="small" type="error" v-if="orderSum" :text="orderSum"></uni-badge>
			</navigator>
			<navigator class="orderitem" url="../order/index?tabIndex=2" hover-class="none">
				<image class="orderimg" src="../../static/img/order2.png" mode=""></image>
				<text id="navtext">待发货</text>
				<uni-badge class="badge" size="small" type="error" v-if="deliveredSum" :text="deliveredSum"></uni-badge>
			</navigator>
			<navigator class="orderitem" url="../order/index?tabIndex=3" hover-class="none">
				<image class="orderimg" src="../../static/img/order3.png" mode=""></image>
				<text id="navtext">待收货</text>
				<uni-badge class="badge" size="small" type="error" v-if="receivedSum" :text="receivedSum"></uni-badge>
			</navigator>
			<navigator class="orderitem" url="../order/index?tabIndex=4" hover-class="none">
				<image class="orderimg" src="../../static/img/order4.png" mode=""></image>
				<text id="navtext">待评价</text>
				<uni-badge class="badge" size="small" type="error" v-if="evaluationSum" :text="evaluationSum">
				</uni-badge>
			</navigator>
		</view>
		<!--E 订单 -->
		<uni-list class="tabs" id="tabs1">
			<navigator hover-class="none" :url="'./signin?flag='+isSignInToday">
				<uni-list-item title="今天还没签到" v-if="isSignInToday==0" thumb="/static/img/user-tab14.png">
				</uni-list-item>
				<uni-list-item title="今天已签到" v-if="isSignInToday==1" thumb="/static/img/user-tab14.png"></uni-list-item>
			</navigator>
			<navigator hover-class="none" url="../medrecord/medrecordList">
				<uni-list-item title="我的病历" thumb="/static/img/user-tab1.png"></uni-list-item>
			</navigator>
			<navigator hover-class="none" url="../medrecord/health/record_list">
				<uni-list-item title="健康档案" thumb="/static/img/user-tab18.png"></uni-list-item>
			</navigator>
			<!-- <navigator hover-class="none" url="../doctors/doctorsList"><uni-list-item title="我的二维码" thumb="/static/img/user-tab2.png"></uni-list-item></navigator> -->
			<navigator hover-class="none" url="/pages/user/stores/store_list">
				<uni-list-item title="我的会员门店" thumb="/static/img/user-tab16.png"></uni-list-item>
			</navigator>
			<navigator hover-class="none" url="../addressBook/index">
				<uni-list-item title="我的医生" thumb="/static/img/user-tab3.png"></uni-list-item>
			</navigator>
			<navigator url="./myAppointment">
				<uni-list-item title="我的预约" thumb="/static/img/user-tab15.png"></uni-list-item>
			</navigator>
			<navigator url="./integralList">
				<uni-list-item title="我的积分" thumb="/static/img/jfdh.png"></uni-list-item>
			</navigator>
			<navigator url="./myGiftList">
				<uni-list-item title="我的礼品" thumb="/static/img/gift.png"></uni-list-item>
			</navigator>
			<!-- <navigator url="../doctors/doctorsList"><uni-list-item title="我的评价" thumb="/static/img/user-tab6.png"></uni-list-item></navigator> -->
			<navigator url="../blacklist/index">
				<uni-list-item title="我的黑名单" thumb="/static/img/user-tab7.png"></uni-list-item>
			</navigator>
		</uni-list>

		<uni-list class="tabs" id="tabs2">
			<!-- <navigator hover-class="none" url="../doctors/doctorsList"><uni-list-item title="推荐有奖" thumb="/static/img/user-tab8.png"></uni-list-item></navigator> -->
			<view @tap="tochatView" class="serve">
				<uni-list-item title="在线客服" thumb="/static/img/user-tab11.png"></uni-list-item>
				<view class="unread" v-if="unreadnum">
					{{unreadnum}}
				</view>
			</view>
		</uni-list>

	</view>
</template>

<script>
	import uniList from '@/components/uni-list/uni-list.vue';
	import uniListItem from '@/components/uni-list-item/uni-list-item.vue';
	import uniBadge from "@/components/uni-badge/uni-badge.vue"
	export default {
		components: {
			uniList,
			uniListItem,
			uniBadge
		},
		data() {
			return {
				userInfo: null,
				orderSum: 0, //待付款订单的数量，大于或等于0的整数
				evaluationSum: 0, //待评价记录,的数量，大于或等于0的整数
				receivedSum: 0, //待收货数量，大于或等于0的整数
				deliveredSum: 0, //待发货，大于或等于0的整数
				isSignInToday: 0, //
				unreadnum: 0
			};
		},
		filters: {
			getlast(num) {
				if (num) {
					let str = '*******' + num.substr(-4)
					return str
				} else {
					return ''
				}

			}
		},
		onShow() {
			this.userInfo = this.$store.state.userInfo;
			this.getPendingTaskSum()
			this.getSignInStatus()
		},
		created() {
			this.getrecvunread()
			this.$IMSDK.subscribe(
				this.$IMSDK.IMEvents.OnConversationChanged,
				({
					data
				}) => {
					// data 会话信息
					if (data.userID == 'xxjk_serv') {
						this.getrecvunread()
					}
				}
			);
		},

		onNavigationBarButtonTap(e) {
			if (e.index == 0) {
				uni.navigateTo({
					url: './set'
				});
			}
		},
		methods: {
			tochatView() {
				let that = this
				let inq = {
					name: '小欣客服',
					face: 'img/kfAvatar.png',
					username: "xxjk_serv",
					isdel: false
				}
				//选择用户聊天
				that.$store.commit('createConversationActive', inq.username);
				that.$IMSDK.asyncApi('getOneConversation', Date.now().toString(), {
						sourceID: 'xxjk_serv',
						sessionType: 1
					})
					.then((
						data
					) => {
						uni.navigateTo({
							url: '../tim/room?conversationID=' + data.data.conversationID + '&toUserInfo=' +
								encodeURIComponent(JSON.stringify(inq))
						});
						// 调用成功
					})
					.catch((err) => {
						// 调用失败
						console.log(err)
						if(err.errCode==1004){
							uni.showToast({
								icon: 'none',
								title: '对方账号异常，无法开启会话！'
							});
						}else{
							uni.showModal({
								title:'服务异常',
								content:'检测到APP通讯状态异常，可能影响正常使用，请重启应用',
								showCancel:false,
							});
						}
					});

			},
			getPendingTaskSum() {
				this.$api.get('api/patient/user/getPendingTaskSum', {
					params: {
						type: 0
					}
				}).then(res => {
					// console.log(res)
					this.orderSum = res.orderSum
					this.evaluationSum = res.evaluationSum
					this.receivedSum = res.receivedSum
					this.deliveredSum = res.deliveredSum
				}).catch(err => {

				})
			},
			getSignInStatus() {
				this.$api.get("api/patient/user_sign/status")
					.then(res => {
						if (!res.errno) {
							this.isSignInToday = res.data.status;
							console.log(this.isSignInToday, 'signintoday')
						}
					})
					.catch(err => {

					})
			},
			getrecvunread() {
				this.$IMSDK.asyncApi('getOneConversation', Date.now().toString(), {
						sourceID: 'xxjk_serv',
						sessionType: 1
					})
					.then(({
						data
					}) => {
						// 调用成功
						console.log(data)
						this.unreadnum = data.data.unreadCount
					})
					.catch(({
						errCode,
						errMsg
					}) => {
						// 调用失败
					});
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;
		background-image: url(../../static/img/userbg.png);
		background-size: 100% 130px;
		background-repeat: no-repeat;
		min-height: 100%;
	}

	.serve {
		position: relative;

		.unread {
			position: absolute;
			width: 36rpx;
			height: 36rpx;
			top: 50%;
			right: 100rpx;
			transform: translateY(-50%);
			background-color: #FF0000;
			line-height: 36rpx;
			text-align: center;
			border-radius: 18rpx;
			color: #fff;
		}
	}

	#user {
		position: relative;
		top: 0px;
		left: 0px;

		.bg {
			width: 100%;
			height: $jian-list-height-base;
		}

		.flex-start {
			display: flex;
			justify-content: flex-start;
			align-items: center;
		}

		.space-between {
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		#gai {
			color: $uni-bg-color;
			margin: 0px $uni-spacing-row-sm;
			padding: $uni-spacing-col-lg 0px;

			.item1 {
				font-size: $uni-font-size-lg;
				margin-bottom: $uni-spacing-col-base;


			}

			.point {
				font-size: $uni-font-size-base;
				display: flex;
				align-items: center;

				image {
					width: 40rpx !important;
				}

			}

			.item2 {
				font-size: $uni-font-size-base;
			}

			.avatar {
				width: $uni-img-size-lg;
				height: $uni-img-size-lg;
				border-radius: $uni-border-radius-circle;
				margin-right: $uni-spacing-row-base;
			}

			.back {
				width: $uni-img-size-sm;
				height: $uni-img-size-sm;
			}

			.gender {
				width: $uni-img-size-sm;
				height: $uni-img-size-sm;
				// margin-left: $uni-spacing-row-sm;
			}
		}

		#order {
			background-color: $uni-text-color-inverse;
			margin: 0px $uni-spacing-row-lg;
			border-radius: $uni-border-radius-lg !important;

			navigator {
				text-align: center;
				width: 25%;
				padding: $uni-spacing-col-base 0px;

				.orderimg {
					width: $uni-img-size-lg;
					height: $uni-img-size-lg;
					margin: auto;
					display: block;
				}

				#navtext {
					color: $uni-text-color;
					font-size: $uni-font-size-base;
				}
			}
		}

		.tabs {
			margin: 0px $uni-spacing-row-lg;
			border-radius: $uni-border-radius-lg !important;

			.uni-list-item {
				padding: 0px $uni-spacing-row-sm;
			}
		}

		#tabs1 {
			margin-top: $uni-spacing-col-lg;
		}

		#tabs2 {
			margin: $uni-spacing-col-lg;
		}

		.orderitem {
			position: relative;

			.badge {
				position: absolute;
				top: 8px;
				right: 8px;
				// background-color: #FF0000;
			}
		}
	}
</style>