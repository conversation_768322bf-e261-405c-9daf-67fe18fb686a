
// Objective-C API for talking to the following Go packages
//
//	github.com/openimsdk/openim-sdk-core/v3/open_im_sdk
//	github.com/openimsdk/openim-sdk-core/v3/open_im_sdk_callback
//
// File is generated by gomobile bind. Do not edit.
#ifndef __OpenIMCore_FRAMEWORK_H__
#define __OpenIMCore_FRAMEWORK_H__

#include "Open_im_sdk.objc.h"
#include "Open_im_sdk_callback.objc.h"
#include "Universe.objc.h"

#endif
