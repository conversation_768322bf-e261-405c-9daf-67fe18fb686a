<template>
	<view class="register">
		<view class="content">
			<!-- 头部logo -->
			<view class="header">
				<image :src="logoImage"></image>
			</view>
			<!-- 主体 -->
			<view class="main">
				<wInput v-model="userName" type="text" placeholder="用户名"></wInput>
				<wInput v-model="phoneData" type="text" maxlength="11" placeholder="手机号"></wInput>
				<wInput v-model="passData" type="password" placeholder="登录密码" isShowPass></wInput>
				<wInput v-model="verCode" type="number" maxlength="6" placeholder="验证码" isShowCode ref="runCode"
					@setCode="getVerCode()"></wInput>
			</view>
			<wButton text="注 册" :rotate="isRotate" @click.native="startReg()"></wButton>

			<!-- 底部信息 -->
			<view class="agreement">
				<text @tap="isShowAgree" class="cuIcon"
					:class="showAgree ? 'cuIcon-radiobox' : 'cuIcon-round'">同意</text>
				<!-- 协议地址 -->
				<navigator class='clink' url="./protocol" open-type="navigate">《用户协议》</navigator>、
				<navigator class='clink' url="./privacy" open-type="navigate">《隐私政策》</navigator>
			</view>
		</view>
	</view>
</template>

<script>
	var _this;
	import wInput from '../../components/watch-login/watch-input.vue'; //input
	import wButton from '../../components/watch-login/watch-button.vue'; //button

	export default {
		data() {
			return {
				//logo图片 base64
				logoImage: '/static/img/logo.png',
				phoneData: '', // 用户/电话
				passData: '', //密码
				verCode: '', //验证码
				userName: '', //用户名
				showAgree: false, //协议是否选择
				isRotate: false //是否加载旋转
			};
		},
		components: {
			wInput,
			wButton
		},
		mounted() {
			_this = this;
		},
		methods: {
			isShowAgree() {
				//是否选择协议
				_this.showAgree = !_this.showAgree;
			},
			getVerCode() {
				//获取验证码
				if (!(/^1[3|4|5|7|8|9][0-9]{9}$/.test(this.phoneData))) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '手机号码格式不正确或为空!'
					});
					return false;
				}
				console.log('获取验证码');
				this.$refs.runCode.$emit('runCode'); //触发倒计时（一般用于请求成功验证码后调用）
				setTimeout(() => {
					_this.$refs.runCode.$emit('runCode', 0);
				}, 60000);
				this.$api
					.get('api/patient/getsmscode', {
						params: {
							telephone: this.phoneData
						}
					})
					.then(res => {})
					.catch(err => {});
			},
			startReg() {
				//注册
				if (this.isRotate) {
					//判断是否加载中，避免重复点击请求
					return false;
				}
				if (!(/^[a-zA-Z][a-zA-Z0-9_@]{4,15}$/.test(this.userName))) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '用户名字母开头，允许5-16字节，允许字母数字下划线组合!'
					});
					return false;
				}
				if (this.showAgree == false) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '请先同意《协议》'
					});
					return false;
				}
				if (!(/^1[3|4|5|7|8|9][0-9]{9}$/.test(this.phoneData))) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '手机号码格式不正确或为空!'
					});
					return false;
				}
				if (!(/^.*(?=.{6,})(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!@#$%^&*? ]).*$/.test(this.passData))) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '密码最少6位，包括至少1个大写字母，1个小写字母，1个数字，1个特殊字符!'
					});
					return false;
				}
				if (!(/^\d{6}$/.test(this.verCode))) {
					uni.showToast({
						icon: 'none',
						position: 'bottom',
						title: '验证码6位，只包括数字'
					});
					return false;
				}
				_this.isRotate = true;
				setTimeout(function() {
					_this.isRotate = false;
				}, 3000);
				let params = {
					username: this.userName,
					telephone: this.phoneData,
					smscode: this.verCode,
					password: this.passData
				}
				if (plus.runtime.arguments) {
					let plusinfo = getApp().getUrlParams(plus.runtime.arguments)
					if (plusinfo.type == '1') {
						params.invite_id = plusinfo.drId
						params.invite_type = 1
					} else if (plusinfo.type == '2' || plusinfo.type == '3') {
						params.invite_id = plusinfo.userid
						params.invite_type = 2
					}
					if (plusinfo.drId) {
						params.drId = plusinfo.drId
					}
					if (plusinfo.hospitalId) {
						params.hospitalId = plusinfo.hospitalId
					}
				}
				this.$api.post('api/patient/auth/register', params).then(res => {
					console.log(res)
					uni.showModal({
						title: '温馨提示',
						content: res.msg,
						showCancel: false,
						success: function(data) {
							if (data.confirm) {
								if (res.success) {
									uni.navigateBack();
								}
							} else if (data.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}).catch(err => {
					_this.isRotate = false;
					console.log(err)
				})
			}
		}
	};
</script>

<style>
	@import url('../../components/watch-login/css/icon.css');
	@import url('./css/main.css');
</style>