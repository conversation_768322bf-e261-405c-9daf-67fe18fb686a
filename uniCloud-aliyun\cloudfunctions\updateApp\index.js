'use strict';
/**
 * 比较版本大小，如果新版本nv大于旧版本ov则返回true，否则返回false
 * @param {String} ov
 * @param {String} nv
 * @return {Boolean} 
 */
const compareVersion =function ( ov, nv ){
	if ( !ov || !nv || ov=="" || nv=="" ){
		return false;
	}
	var b=false,
	ova = ov.split(".",4),
	nva = nv.split(".",4);
	for ( var i=0; i<ova.length&&i<nva.length; i++ ) {
		var so=ova[i],no=parseInt(so),sn=nva[i],nn=parseInt(sn);
		if ( nn>no || sn.length>so.length  ) {
			return true;
		} else if ( nn<no ) {
			return false;
		}
	}
	if ( nva.length>ova.length && 0==nv.indexOf(ov) ) {
		return true;
	}
}
exports.main = async (event, context) => {
	let iosUrl = "https://vkceyugu.cdn.bspapp.com/VKCEYUGU-alixjk31415/9aacc2a0-e43e-11ea-b997-9918a5dda011.wgt"
	let anUrl ="https://vkceyugu.cdn.bspapp.com/VKCEYUGU-alixjk31415/9aacc2a0-e43e-11ea-b997-9918a5dda011.wgt"
	let res = {
		versionCode:4,
		versionName:"1.0.3",
		versionInfo:"版本更新",
		forceUpdate:false,
		downloadUrl:event.type==1101? anUrl:iosUrl
	}
	if(compareVersion(event.versionName,res.versionName)){
		return res
	}else{
		return null
	}
};
