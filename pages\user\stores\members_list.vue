<template>
	<view v-show="index==0">
		<view class="base">
			<mescroll-uni class="friend" ref="mescrollRef" height="87%" @init="mescrollInit" @down="downCallback"
				@up="upCallback" :down="downOption" :up="upOption">
				<view class="" v-for="(value, key, index) in addressBook.book" >
					<view class="pytitle" :id="key">
						<text>{{ key }}</text>
					</view>
					<view class="store_item" v-for="n in value" @tap="godetail(n)">
						<view class="avatar">
							<image v-if="n.clindFace" :src="$hisBaseUrl+n.clindFace" mode=""></image>
							<image v-else src="../../../static/img/hospital.jpg" mode=""></image>
						</view>
						<view class="clindinfo">
							<view class="title">
								<view class="name">
									{{n.clindName}}
								</view>
								<view class="score">
									评分：{{n.clindScore}}
								</view>
							</view>
							<view class="address">
								{{n.clindAddress}}
							</view>
						</view>
					</view>
				</view>
			</mescroll-uni>
			<view class="letter">
				<view v-for="n in addressBook.letter" @tap="scrollTo(n)">
					<text>{{ n }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	import MescrollUni from '@/components/mescroll-uni/mescroll-uni.vue';
	import p from 'wl-pinyin';
	export default {
		mixins: [MescrollMixin], // 使用mixin
		components: {
			MescrollUni,
			
		},
		props: {
			index: {
				// 当前tab的下标
				type: Number,
				default () {
					return 0;
				}
			}
		},
		data() {
			return {
				// 下拉刷新的配置(可选)
				downOption: {
					auto: false
				},
				// 上拉加载的配置(可选)
				upOption: {
					auto: true,
					noMoreSize: 2, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
					empty: {
						tip: '~ 空空如也 ~' // 提示
					}
				},
				addressBook: {}
			};
		},
		methods: {
			downCallback() {
				this.getlist()
			},
			upCallback() {
				this.getlist()
			},
			godetail(data) {
				uni.navigateTo({
					url: `/pages/user/stores/store_detail?data=`+encodeURIComponent(JSON.stringify(data))
				})
			},
			getlist() {
				this.list = [];
				this.book = {};
				this.$api
					.get('api/patient/vip/getlist')
					.then(res => {
						let book = {};
						let letter = [];
						this.mescroll.endSuccess(res.success);
						res.data.forEach(function(n) {
							let tmp = p.getPinyin(n.clindName).replace(/\s/g, '');
							n.pinyin = tmp;
							n.sort = tmp.substr(0, 1).toUpperCase();
							if (book[n.sort] == undefined) {
								book[n.sort] = [];
								letter.push(n.sort);
							}
							book[n.sort].push(n);
							return n;
						});
						let newData = {};
						Object.keys(book)
							.sort()
							.map(key => {
								newData[key] = book[key];
							});
						let addressBook = {
							book: newData,
							list: res,
							letter: letter.sort()
						};
						this.addressBook = addressBook
					})
					.catch(err => {
						this.mescroll.endErr();
					});
			},
			scrollTo(val) {
				this.mescroll.scrollTo(val);
			},
		}
	}
</script>

<style lang="scss">
	.store_item {
		width: 90%;
		padding: 24rpx;
		box-sizing: border-box;
		border-radius: 5px;
		margin: 15rpx auto 0;
		background-color: #fff;
		display: flex;
		justify-content: space-between;
		.avatar{
			width:100rpx;
			height:100rpx;
			border-radius: 50%;
			overflow: hidden;
			image{
				width:100rpx;
				height:100rpx;
			}
		}
		.clindinfo{
			width: calc(100% - 120rpx);
			.title {
				.name {
					font-size: 30rpx;
					font-weight: bold;
				}
			
				.score {
					font-size: 24rpx;
				}
			}
		}
		

		.address {
			font-size: 24rpx;
			color: $uni-text-color-grey;
		}
	}
	.pytitle{
		background-color: $uni-bg-color-grey;
		& > text {
			margin: 0px $uni-spacing-row-sm;
			font-size: $uni-font-size-base;
		}
	}
	.base {
		width: 100%;
		display: flex;
		flex-direction: row;
		.friend {
			width: 100%;
		}
		.letter {
			text-align: center;
			width: 30px;
		}
	}
</style>