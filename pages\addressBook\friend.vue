<template>
	<view v-show="i === index">
		<uni-search-bar @input="search" cancelButton="none"></uni-search-bar>
		<view class="base">
			<mescroll-uni class="friend" ref="mescrollRef" height="87%" @init="mescrollInit" @down="downCallback" @up="upCallback" :down="downOption" :up="upOption">
				<view v-for="(value, key, index) in data.book">
					<view class="title" :id="key">
						<text>{{ key }}</text>
					</view>
					<navigator :url="'/pages/doctors/doctorsDetails?id=' + n.doctor_id" class="list flex-start" v-for="n in value">
						<view class="avatar">
							<image v-if="n.face" :src="$baseUrl+n.face" mode="widthFix"></image>
							<image v-if="!n.face && n.gender == 1" src="../../static/img/defaultman.png" mode="widthFix"></image>
							<image v-if="!n.face && n.gender == 0" src="../../static/img/defaultgirl.png" mode="widthFix"></image>
						</view>
						<view class="right">
							<view>
								<text class="name">{{ n.realname }}</text>
								<text class="gender">{{ n.gender ? '男' : '女' }}</text>
							</view>
							<view class="item">
								<text>{{ n.brief }}</text>
							</view>
						</view>
					</navigator>
				</view>
			</mescroll-uni>
			<view class="letter">
				<view v-for="n in data.letter" @tap="scrollTo(n)">
					<text>{{ n }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
import MescrollUni from '@/components/mescroll-uni/mescroll-uni.vue';
import { mapState } from 'vuex';
import uniSearchBar from '@/components/uni-search-bar/uni-search-bar.vue';
import p from 'wl-pinyin';
export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MescrollUni,
		uniSearchBar
	},
	props: {
		i: Number, // 每个tab页的专属下标
		index: {
			// 当前tab的下标
			type: Number,
			default() {
				return 0;
			}
		}
	},
	data() {
		return {
			// 下拉刷新的配置(可选)
			downOption: {
				auto: false
			},
			// 上拉加载的配置(可选)
			upOption: {
				auto: true,
				noMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					tip: '~ 空空如也 ~' // 提示
				}
			},
			data: {}
		};
	},
	computed: {
		...mapState({
			addressBook: state => state.addressBook
		})
	},
	watch: {
		addressBook(val) {
			// console.log(val);
			this.data = JSON.parse(JSON.stringify(val));
		}
	},
	methods: {
		/*下拉刷新的回调*/
		downCallback() {
			this.resetList();
		},
		/*上拉加载的回调*/
		upCallback(page) {
			// 与 mescroll-body 的处理方式一致 >
			this.resetList();
		},
		resetList() {
			this.list = [];
			this.book = {};
			this.$api
				.get('api/patient/doctor/getmydoctors')
				.then(res => {
					let book = {};
					let letter = [];
					this.mescroll.endSuccess(res.length);
					res.forEach(function(n) {
						let tmp = p.getPinyin(n.realname).replace(/\s/g, '');
						n.pinyin = tmp;
						n.sort = tmp.substr(0, 1).toUpperCase();
						if (book[n.sort] == undefined) {
							book[n.sort] = [];
							letter.push(n.sort);
						}
						book[n.sort].push(n);
						return n;
					});
					let newData = {};
					Object.keys(book)
						.sort()
						.map(key => {
							newData[key] = book[key];
						});
					let addressBook = {
						book: newData,
						list: res,
						letter: letter.sort()
					};
					console.log(addressBook);
					this.$store.commit('updateAddressBook', addressBook);
				})
				.catch(err => {
					this.mescroll.endErr();
				});
		},
		scrollTo(val) {
			this.mescroll.scrollTo(val);
		},
		search(val) {
			//根据字母或汉字搜索
			if (val.value) {
				let list;
				if (/^[a-zA-Z]+$/.test(val.value)) {
					list = this.data.list.filter(function(n) {
						if (n.pinyin.startsWith(val.value.toLowerCase())) {
							return n;
						}
					});
				} else {
					list = this.data.list.filter(function(n) {
						if (n.realname.includes(val.value)) {
							return n;
						}
					});
				}
				var book = {};
				let letter = [];
				list.forEach(function(n) {
					if (book[n.sort] == undefined) {
						book[n.sort] = [];
						letter.push(n.sort);
					}
					book[n.sort].push(n);
					return n;
				});
				this.data.book = book;
				this.data.letter = letter;
			} else {
				this.data.book = this.$store.state.addressBook.book;
				this.data.letter = this.$store.state.addressBook.letter;
			}
		}
	}
};
</script>

<style lang="scss">
.base {
	width: 100%;
	display: flex;
	flex-direction: row;
	.friend {
		width: 100%;
	}
	.letter {
		text-align: center;
		width: 30px;
	}
}
.title {
	background-color: $uni-bg-color-grey;
	& > text {
		margin: 0px $uni-spacing-row-sm;
		font-size: $uni-font-size-base;
	}
}
.list {
	border-bottom: 1px solid $uni-border-color;
	padding: $uni-spacing-col-sm $uni-spacing-row-sm;
	.avatar {
		width: $uni-img-size-lg;
		height: $uni-img-size-lg;
		border-radius: $uni-border-radius-circle;
		margin-right: $uni-spacing-row-base;
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;
		image{
			width: 100%;
		}
	}
	.right{
		width: calc(100% - 100rpx);
	}
	.name {
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		margin-right: $uni-spacing-row-sm;
	}
	.gender {
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
	}
	.item {
		color: $uni-text-color-grey;
		font-size: $uni-font-size-sm;
	}
}
</style>
