<template>
	<view class="queue">
		<mescroll-body ref="mescrollRef" @init="mescrollInit" :down="downOption" :up="upOption" @down="downCallback"
			@up="upCallback">
			<!-- <view class="headpic">
				<image v-if="face" :src="$hisBaseUrl+face" mode="widthFix"></image>
			</view> -->
			<view class="queueNumber">
				<view class="detail">
					{{queueData.number}}
				</view>
				<view class="desc">
					您的排队号
				</view>
			</view>
			<view class="container">
				<!-- 	<view class="title">
					{{queueData.number}}
				</view> -->
				<view class="desc">
					<view class="label">
						姓名：
					</view>
					<view class="detail">
						{{queueData.patname}}
					</view>
				</view>
				<view class="desc">
					<view class="label">
						科室：
					</view>
					<view class="detail">
						{{queueData.department}}
					</view>
				</view>
				<view class="desc">
					<view class="label">
						医生：
					</view>
					<view class="detail">
						{{queueData.doctor}}
					</view>
				</view>
				<view class="desc">
					<view class="label">
						状态：
					</view>
					<view class="detail">
						{{queueData.status | statusFilter}}
					</view>
				</view>
				<view class="desc">
					<view class="label">
						等待人数：
					</view>
					<view class="detail">
						{{queueData.waitnum || '0（请尽快就诊）' }}
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	import moment from 'moment';
	export default {
		mixins: [MescrollMixin],
		data() {
			return {
				downOption: {
					auto: true //是否在初始化后,自动执行downCallback; 默认true
				},
				upOption: {
					empty: {
						use: true, // 是否显示空布局
						icon: "", // 图标路径 (建议放入static目录, 如 /static/img/mescroll-empty.png )
						tip: '~ 下拉刷新获取最新排队信息 ~' // 提示
					},
				},
				queueData: {},
				// #ifdef MP-WEIXIN
				$hisBaseUrl: '',
				face: '',
				isCropping: this.$cropping.isCropping,
				id: '',
				// #endif
			};
		},
		onLoad: function(option) {
			this.id = option.id;
			this.face = this.$store.state.hospitalData.face
		},
		methods: {
			/*下拉刷新的回调 */
			downCallback() {
				this.medList = []
				this.mescroll.resetUpScroll()
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				// console.log(page)
				/**
				 * todo :获取排队信息
				 */
				this.$api.get("api/patient/register/getdetail", {
						params: {
							id: this.id
						}
					})
					.then(res => {
						this.mescroll.endSuccess(0);
						this.queueData = res.data
						uni.setNavigationBarTitle({
							title: this.queueData.department + '排队'
						})
						return
						if (res.data.status != 0) {
							uni.showModal({
								title: '提示',
								content: '当前排队已结束',
								showCancel: false,
								success: function(res) {
									uni.navigateTo({
										url: '/pagesA/user/myAppointment'
									});
								}
							});
						}
					})
					.catch(err => {
						this.mescroll.endErr();
					})
			},

		},
		filters: {
			statusFilter(status) {
				switch (status) {
					case 0:
						return '待诊'
					case 1:
						return '已诊'
					case 2:
						return '诊中'
					case 3:
						return '已过期'
					case 4:
						return '未签到'
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	page {
		width: 100%;
		background-color: $uni-bg-color-grey;
	}



	.headpic {
		width: 160rpx;
		height: 160rpx;
		border-radius: 50%;
		overflow: hidden;
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 30rpx auto;
		background-image: url(/static/img/hospital.jpg);
		background-size: cover;

		image {
			width: 100%;
		}
	}

	.queueNumber {
		width: 160rpx;
		height: 160rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		margin: 30rpx auto 10rpx;

		.detail {
			color: $jian-bg-color;
			font-size: 60rpx;
			font-weight: bold;
		}

		.desc {
			color: gray;
			font-size: 30rpx;
		}
	}

	.container {
		box-sizing: border-box;
		padding: 0 15%;

		.title {
			text-align: center;
			height: 120rpx;
			line-height: 120rpx;
			font-size: 28rpx;
		}

		.desc {
			height: 120rpx;
			line-height: 120rpx;
			font-size: 30rpx;
			font-weight: bold;
			display: flex;
			align-items: center;

			.label {
				width: 160rpx;
				// text-align-last: justify;
				margin-right: 30rpx;
			}

			.detail {
				font-weight: normal;
			}
		}
	}
</style>