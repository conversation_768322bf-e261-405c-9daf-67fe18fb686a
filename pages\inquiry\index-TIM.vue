<template>
	<view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" :down="downOption" @down="downCallback" @up="upCallback">
			<view url="" id="inquiry" class="flex-start" v-for="n in inqList" @click="tochatView(n)">
				<view class="left">
					<image class="avatar" v-if="n.face" :src="$baseUrl + n.face" mode=""></image>
					<image class="avatar" v-if="!n.face&&n.gender==0" src="../../static/img/defaultgirl.png" mode="">
					</image>
					<image class="avatar" v-if="!n.face&&n.gender==1" src="../../static/img/defaultman.png" mode="">
					</image>
					<text class="unreadCount" v-show="n.unreadCount">{{ n.unreadCount }}</text>
				</view>
				<view class="rigth">
					<view class="space-between item1">
						<view class="flex-start">
							<text class="name">{{ n.doctname }}</text>
							<text v-if="n.department">{{ n.department }}</text>
						</view>
						<text v-if="n.status != 2 && n.ischarge == 1">{{ n.adddate }}</text>
						<text v-if="n.status == 2 && n.ischarge == 1">{{ n.enddate }}</text>
					</view>
					<view class="item2 space-between">
						<text class="cliname">{{ n.cliname }}</text>
						<text v-if="n.status == 0 && n.ischarge == 1">未接诊</text>
						<text v-if="n.status == 1 && n.ischarge == 1">接诊中</text>
						<text v-if="n.status == 2 && n.ischarge == 1">接诊结束</text>
						<text v-if="n.ischarge == 2">退款中</text>
						<text v-if="n.ischarge == 3">已退款</text>
					</view>
					<view class="item3" v-if="n.status == 0 && n.isBefore"><text class="refund"
							@click.stop="refund(n.ordernum)" type="default">退款</text></view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	import {
		mapState
	} from 'vuex';
	import moment from 'moment';
	let that;
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
				inqList: [],
				// #ifdef MP-WEIXIN
				$baseUrl: '',
				// #endif
				operationID: ''
			};
		},
		computed: {
			...mapState({
				isTimLogin: state => state.isTimLogin,
				isSDKReady: state => state.isSDKReady,
				conversationList: state => state.conversationList
			})
		},
		watch: {
			conversationList(val) {
				if (val.length > 0) {
					this.mergelist(val);
				}
			}
		},
		created() {
			this.operationID = this.$store.state.operationID
			this.$globalEvent.addEventListener("onConversationChanged", (params) => {
					this.getConversationList()
					this.$openSdk.getTotalUnreadMsgCount(this.operationID, data => {
						let num = parseInt(data.data)
						this.$openSdk.getOneConversation(this.operationID, 1, 'xxjk_serv', data => {
							let kfnum = JSON.parse(data.data).unreadCount
							num = num - kfnum
							num = num.toString()
							if (num != '0') {
								uni.setTabBarBadge({
									index: 2,
									text: num,
								})
							}
						})
					})
				}),
				this.$globalEvent.addEventListener('OnNewConversation', data => {
					console.log('新会话创建：', data)
				})
		},
		onLoad() {
			that = this;
			uni.$on('updateInquiry', function() {
				that.inqList = [];
				that.mescroll.resetUpScroll();
			});
			// #ifdef MP-WEIXIN
			this.$baseUrl = getApp().globalData.$baseUrl
			// #endif
		},
		methods: {
			/*下拉刷新的回调 */
			downCallback() {
				this.inqList = [];
				this.mescroll.resetUpScroll();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				this.$api
					.get('api/patient/inquiry/getlist', {
						params: {
							pager: page.num
						}
					})
					.then(res => {
						this.mescroll.endSuccess(res.length);
						let arr = res.map(n => {
							n.enddate = moment(n.enddate).format('MM/DD HH:mm');
							n.isBefore = (n.payDate != null) ? moment().isAfter(moment(n.payDate).add(12,
								'h')) : false;
							n.adddate = moment(n.adddate).format('MM/DD HH:mm');
							return n;
						});
						console.log(arr);
						this.inqList = this.inqList.concat(arr);
						this.getConversationList();
					})
					.catch(err => {
						this.mescroll.endErr();
					});
			},
			//获取消息列表
			getConversationList() {
				// 拉取会话列表
				let promise = this.tim.getConversationList();
				promise &&
					promise
					.then(res => {
						let conversationList = res.data.conversationList; // 会话列表，用该列表覆盖原有的会话列表
						if (conversationList.length) {
							//将返回的会话列表拼接上 用户的基本资料
							//说明：如果已经将用户信息 提交到tim服务端了 就不需要再次拼接
							this.$store.commit('updateConversationList', conversationList);
						}
					})
					.catch(err => {
						console.warn('getConversationList error:', err); // 获取会话列表失败的相关信息
					});
			},
			//获取未读消息数
			getUserInfo(conversationList) {
				let unreadtotal = 0;
				this.inqList = this.inqList.map(item => {
					conversationList.forEach(item1 => {
						let username = 'doctor_' + item.username;
						if (item1.toAccount == username) {
							// console.log(item1.unreadCount);
							item.unreadCount = item1.unreadCount;
							unreadtotal += item1.unreadCount;
						}
					});
					return item;
				});
				if (unreadtotal) {
					uni.setTabBarBadge({
						index: 2,
						text: unreadtotal.toString()
					});
				} else {
					uni.removeTabBarBadge({
						index: 2
					});
				}
			},
			tochatView(inq) {
				if (inq.status == 0) {
					uni.showToast({
						icon: 'none',
						title: '等待医生接诊!'
					});
					return;
				}
				//选择用户聊天
				// console.log(inq)
				this.$store.commit('createConversationActive', 'doctor_' + inq.username);
				// console.log(JSON.stringify(inq));
				uni.navigateTo({
					url: '../tim/room?toUserInfo=' + encodeURIComponent(JSON.stringify(inq))
				});
			},
			refund(val) {
				//退费
				console.log(val);
				uni.showModal({
					title: '温馨提示',
					content: '该医生长时间未接诊，是否退费',
					success(result) {
						if (result.confirm) {
							that.$api
								.post('api/patient/order/refund', {
									ordernumber: val.trim()
								})
								.then(res => {
									console.log(res);
									uni.showToast({
										icon: 'none',
										title: res.msg
									});
									that.inqList = [];
									that.mescroll.resetUpScroll();
								});
						}
					}
				});
			}
		},
		onShow() {
			if (this.isSDKReady) {
				this.getConversationList();
			} else {}
		}
	};
</script>

<style lang="scss">
	page {
		width: 100% !important;
		background-color: $uni-bg-color-grey;
		padding: $uni-spacing-col-lg 0px;

		#inquiry {
			// min-height:$jian-list-height-sm ;
			background-color: $uni-bg-color;
			padding: $uni-spacing-col-base;
			margin: $uni-spacing-col-sm $uni-spacing-row-lg;
			margin-bottom: $uni-spacing-col-lg;
			border-radius: $uni-border-radius-base;

			.left {
				margin-top: $uni-spacing-col-sm;
				position: relative;
				width: 120rpx;
				height: 100rpx;

				.unreadCount {
					position: absolute;
					right: -10px;
					top: -8px;
					width: 40rpx;
					height: 40rpx;
					border-radius: 50%;
					background: #f06c7a;
					color: #fff;
					line-height: 40rpx;
					text-align: center;
					font-size: 24rpx;
				}

				.avatar {
					position: absolute;
					rigth: 0px;
					top: 0px;
					width: 100%;
					height: 100%;
					border-radius: $uni-border-radius-lg;
				}
			}

			.rigth {
				width: 100%;
				margin-top: 10rpx;

				.item1 {
					width: 100%;
					margin-bottom: $uni-spacing-col-sm;
					color: $uni-text-color-grey;
					font-size: $uni-font-size-base;

					text {
						margin: 0px $uni-spacing-row-sm;
					}

					.name {
						color: $uni-text-color;
						font-size: $uni-font-size-lg;
						font-weight: 700;
					}
				}

				.item2 {
					margin: 0px $uni-spacing-row-sm;
					margin-bottom: $uni-spacing-col-sm;
					color: #ec4040;
					font-size: $uni-font-size-base;

					.cliname {
						color: $uni-text-color-grey;
					}
				}

				.item3 {
					margin: 0px $uni-spacing-row-sm;
					margin-bottom: $uni-spacing-col-sm;

					.refund {
						height: 20px;
						width: 20px;
						border-radius: $uni-border-radius-lg;
						padding: $uni-spacing-col-sm $uni-spacing-row-sm;
						background-color: #16cc9f;
						color: $uni-text-color-inverse;
						font-size: $uni-font-size-sm;
					}
				}
			}
		}
	}
</style>
