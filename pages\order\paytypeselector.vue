<template>
	<view class="a_mask" @tap="cancel">
		<view class="a_box">
			<view class="paytype">
				<text>请选择一个付款方式：</text>
			</view>
			<view class="paytype" @tap.stop="setPayType(2)">
				<text>微信</text>
			</view>
			<view class="paytype" @tap.stop="setPayType(0)">
				<text>支付宝</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			title:{
				type:String,
				default:'提示'
			},
			placeholder:{
				type:String,
				default:'请点击输入'
			},
			name:{
				type:String,
				default:'text'
			},
			type:{
				type:String,
				default:'text'
			},
			value:{
				type:String,
				default:''
			},
			cancelColor:{
				type:String,
				default:'#999999'
			},
			confirmColor:{
				type:String,
				default:'#333333'
			},
			cancelText:{
				type:String,
				default:'取消'
			},
			confirmText:{
				type:String,
				default:'确定'
			},
		},
		data() {
			return {

			};
		},
		methods: {
			setPayType: function(val) {
				console.log(val)
				this.$emit('payTypeConfirm',val)
			},
			cancel: function() {
				this.$emit('payTypeCancel')
			}
		}
	}
</script>

<style lang="scss">
	.a_mask{
		position: fixed;
		z-index: 99999;
		background-color: rgba(0,0,0,0.5);
		top: 0;
		left: 0;
		bottom: 0;
		right: 0;
		.a_box{
			width: 600upx;
			overflow: hidden;
			
			background-color: #fff;
			border-radius: 5upx;
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%,-50%);
			.paytype{
				margin: $uni-spacing-col-lg $uni-spacing-row-lg;
			}
		}
	}
</style>
