
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/cover/cover","pages/login/login","pages/index/index","pages/mall/index","pages/login/forget","pages/login/register","pages/login/verifyphone","pages/msg/msg","pages/user/user","pages/user/myGiftList","pages/user/integralList","pages/medrecord/medrecordList","pages/medrecord/medrecordDeatils","pages/user/signin","pages/doctors/doctorsList","pages/hospitals/hospitalsList","pages/blacklist/index","pages/user/userDetails","pages/inquiry/index","pages/tim/room","pages/doctors/doctorsDetails","pages/addressBook/index","pages/hospitals/myHospitals","pages/doctors/inquiryNewOrder","pages/doctors/reservationNewOrder","pages/doctors/confirmPresent","pages/inquiry/health","pages/order/index","pages/order/details","pages/recipientAddress/authentication","pages/recipientAddress/verifyexample","pages/recipientAddress/index","pages/recipientAddress/editAddress","pages/login/protocol","pages/user/set","pages/user/AboutUs","pages/order/logistics","pages/user/edit-phone","pages/guide/index","pages/login/privacy","pages/user/myAppointment","pages/user/queue","pages/hospitals/hospitalDetail/hospitalDetail","pages/hospitals/hospitalDetail/recommendedList","pages/hospitals/hospitalSearch","pages/hospitals/myCollect","pages/hospitals/History","pages/hospitals/hospitalDetail/ServiceHistory","pages/user/account","pages/user/cancellation/cancellation","pages/user/cancellation/verification","pages/user/cancellation/result","pages/user/cancellation/Cancellation_Agreement","pages/webview/webview","pages/doctors/homeDoctor","pages/doctors/99eyao","pages/user/stores/store_list","pages/user/stores/members_list","pages/user/stores/store_detail","pages/user/stores/accountBook","pages/user/stores/rechargeBook","pages/user/stores/tickets","pages/user/stores/coupon_detail","pages/medrecord/health/record_list","pages/user/stores/unuse_tickets","pages/doctors/videoCall","pages/order/videoCall","pages/user/permission"],"window":{"navigationBarTextStyle":"black","navigationBarBackgroundColor":"#FFFFFFFF","backgroundColor":"#393939","scrollIndicator":"none","bounce":"none"},"tabBar":{"color":"#B2B2B2","selectedColor":"#16CC9F","backgroundColor":"#ffffff","list":[{"pagePath":"pages/index/index","text":"首页","iconPath":"static/img/home.png","selectedIconPath":"static/img/homeHL.png"},{"pagePath":"pages/msg/msg","text":"消息","iconPath":"static/img/msg.png","selectedIconPath":"static/img/msgHL.png"},{"pagePath":"pages/inquiry/index","text":"咨询","iconPath":"static/img/inquiry.png","selectedIconPath":"static/img/inquiryHL.png"},{"pagePath":"pages/mall/index","text":"商城","iconPath":"static/img/mall.png","selectedIconPath":"static/img/mallHL.png"},{"pagePath":"pages/user/user","text":"我的","iconPath":"static/img/user.png","selectedIconPath":"static/img/userHL.png"}]},"networkTimeout":{"request":15000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"weex","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":false},"appname":"欣九康健康版","compilerVersion":"4.45","entryPagePath":"pages/cover/cover"};
var __uniRoutes = [{"path":"/pages/cover/cover","meta":{"isQuit":true},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false,"titleNView":false}},{"path":"/pages/login/login","meta":{},"window":{"navigationBarTitleText":"登录"}},{"path":"/pages/index/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"欣九康","titleColor":"#393939","scrollIndicator":"none"}},{"path":"/pages/mall/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"","navigationStyle":"custom","titleNView":false,"animationType":"slide-in-bottom"}},{"path":"/pages/login/forget","meta":{},"window":{"navigationBarTitleText":"重置密码"}},{"path":"/pages/login/register","meta":{},"window":{"navigationBarTitleText":"注册"}},{"path":"/pages/login/verifyphone","meta":{},"window":{"navigationBarTitleText":"绑定手机注册"}},{"path":"/pages/msg/msg","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarTitleText":"消息","scrollIndicator":"none"}},{"path":"/pages/user/user","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationBarBackgroundColor":"#5BDCD4","navigationBarTextStyle":"white","navigationBarTitleText":"我的","titleNView":{"buttons":[{"text":"","fontSrc":"/static/fonts/mui.ttf","fontSize":"26px","color":"#ffffff"}]},"scrollIndicator":"none"}},{"path":"/pages/user/myGiftList","meta":{},"window":{"navigationBarTitleText":"我的礼品","enablePullDownRefresh":false}},{"path":"/pages/user/integralList","meta":{},"window":{"navigationBarTitleText":"积分明细","enablePullDownRefresh":false}},{"path":"/pages/medrecord/medrecordList","meta":{},"window":{"navigationBarTitleText":"我的病历"}},{"path":"/pages/medrecord/medrecordDeatils","meta":{},"window":{"navigationBarTitleText":"病历详情"}},{"path":"/pages/user/signin","meta":{},"window":{"navigationBarTitleText":"签到"}},{"path":"/pages/doctors/doctorsList","meta":{},"window":{"titleNView":{"searchInput":{"borderRadius":"15px","placeholder":"疾病,症状,医生名","backgroundColor":"#F7F7F7"},"buttons":[{"text":"搜索","fontSize":"16px","width":"48px"}]},"scrollIndicator":"none"}},{"path":"/pages/hospitals/hospitalsList","meta":{},"window":{"titleNView":{"searchInput":{"borderRadius":"15px","placeholder":"医院名称","backgroundColor":"#F7F7F7"},"buttons":[{"text":"搜索","fontSize":"16px","width":"48px"}]},"scrollIndicator":"none"}},{"path":"/pages/blacklist/index","meta":{},"window":{"navigationBarTitleText":"我的黑名单"}},{"path":"/pages/user/userDetails","meta":{},"window":{"navigationBarBackgroundColor":"#5BDCD4","navigationBarTextStyle":"white","navigationBarTitleText":"我的资料"}},{"path":"/pages/inquiry/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/tim/room","meta":{},"window":{"navigationBarTitleText":"聊天室","scrollIndicator":"none"}},{"path":"/pages/doctors/doctorsDetails","meta":{},"window":{"navigationBarTitleText":"医生详情","titleNView":{"type":"transparent"}}},{"path":"/pages/addressBook/index","meta":{},"window":{"navigationBarTitleText":"我的医生","disableScroll":true,"scrollIndicator":"none","bounce":"none"}},{"path":"/pages/hospitals/myHospitals","meta":{},"window":{"navigationBarTitleText":"收藏医院","disableScroll":true,"scrollIndicator":"none","bounce":"none"}},{"path":"/pages/doctors/inquiryNewOrder","meta":{},"window":{"titleNView":{"type":"transparent"}}},{"path":"/pages/doctors/reservationNewOrder","meta":{},"window":{"titleNView":{"type":"transparent"}}},{"path":"/pages/doctors/confirmPresent","meta":{},"window":{"navigationBarTitleText":"选择礼品","enablePullDownRefresh":false}},{"path":"/pages/inquiry/health","meta":{},"window":{"titleNView":{"buttons":[{"text":"编辑健康档案","fontSize":"16px","width":"106px"}]}}},{"path":"/pages/order/index","meta":{},"window":{"navigationBarTitleText":"我的订单"}},{"path":"/pages/order/details","meta":{},"window":{"navigationBarTitleText":"订单详情","titleNView":{"type":"transparent"}}},{"path":"/pages/recipientAddress/authentication","meta":{},"window":{"navigationBarTitleText":"实名认证"}},{"path":"/pages/recipientAddress/verifyexample","meta":{},"window":{"navigationBarTitleText":"图片示例"}},{"path":"/pages/recipientAddress/index","meta":{},"window":{"navigationBarTitleText":"收货地址管理"}},{"path":"/pages/recipientAddress/editAddress","meta":{},"window":{"navigationBarTitleText":"编辑收货地址"}},{"path":"/pages/login/protocol","meta":{},"window":{"navigationBarTitleText":"欣九康用户协议及使用须知"}},{"path":"/pages/user/set","meta":{},"window":{"navigationBarTitleText":"设置"}},{"path":"/pages/user/AboutUs","meta":{},"window":{"navigationBarTitleText":"关于我们"}},{"path":"/pages/order/logistics","meta":{},"window":{}},{"path":"/pages/user/edit-phone","meta":{},"window":{"navigationBarTitleText":"手机号换绑"}},{"path":"/pages/guide/index","meta":{},"window":{"navigationBarTitleText":"用户指南"}},{"path":"/pages/login/privacy","meta":{},"window":{"navigationBarTitleText":"欣九康隐私保护政策"}},{"path":"/pages/user/myAppointment","meta":{},"window":{"navigationBarTitleText":"我的预约"}},{"path":"/pages/user/queue","meta":{},"window":{"navigationBarTitleText":"查看排队","enablePullDownRefresh":false}},{"path":"/pages/hospitals/hospitalDetail/hospitalDetail","meta":{},"window":{"navigationBarBackgroundColor":"#5BDCD4","navigationBarTextStyle":"white","navigationBarTitleText":"医院详情","titleNView":{"buttons":[{"text":"","fontSrc":"/static/fonts/mui.ttf","fontSize":"26px","color":"#ffffff"}]}}},{"path":"/pages/hospitals/hospitalDetail/recommendedList","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/hospitals/hospitalSearch","meta":{},"window":{"titleNView":{"searchInput":{"borderRadius":"15px","placeholder":"医院名称","backgroundColor":"#F7F7F7"},"buttons":[{"text":"搜索","fontSize":"16px","width":"48px"}]},"scrollIndicator":"none"}},{"path":"/pages/hospitals/myCollect","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/hospitals/History","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/hospitals/hospitalDetail/ServiceHistory","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/user/account","meta":{},"window":{"navigationBarTitleText":"账户与安全","enablePullDownRefresh":false}},{"path":"/pages/user/cancellation/cancellation","meta":{},"window":{"navigationBarTitleText":"申请注销账号","enablePullDownRefresh":false}},{"path":"/pages/user/cancellation/verification","meta":{},"window":{"navigationBarTitleText":"注销账号","enablePullDownRefresh":false}},{"path":"/pages/user/cancellation/result","meta":{},"window":{"navigationBarTitleText":"注销账号","enablePullDownRefresh":false}},{"path":"/pages/user/cancellation/Cancellation_Agreement","meta":{},"window":{"navigationBarTitleText":"欣九康注销须知","enablePullDownRefresh":false}},{"path":"/pages/webview/webview","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/doctors/homeDoctor","meta":{},"window":{"navigationBarTitleText":"私人医生","titleNView":{"type":"transparent"}}},{"path":"/pages/doctors/99eyao","meta":{},"window":{"navigationBarTitleText":"","navigationStyle":"custom","titleNView":false,"animationType":"slide-in-bottom"}},{"path":"/pages/user/stores/store_list","meta":{},"window":{"navigationBarTitleText":"门店会员","enablePullDownRefresh":false}},{"path":"/pages/user/stores/members_list","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/user/stores/store_detail","meta":{},"window":{"titleNView":{"buttons":[{"text":"充值记录","fontSize":"16px","width":"72px"}]}}},{"path":"/pages/user/stores/accountBook","meta":{},"window":{"navigationBarTitleText":"我的账单","enablePullDownRefresh":false}},{"path":"/pages/user/stores/rechargeBook","meta":{},"window":{"navigationBarTitleText":"充值记录","enablePullDownRefresh":false}},{"path":"/pages/user/stores/tickets","meta":{},"window":{"navigationBarTitleText":"我的券包","enablePullDownRefresh":false,"titleNView":{"buttons":[{"text":"无效券","fontSize":"16px","width":"72px"}]}}},{"path":"/pages/user/stores/coupon_detail","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/medrecord/health/record_list","meta":{},"window":{"navigationBarTitleText":"健康档案","enablePullDownRefresh":false,"titleNView":{"buttons":[{"text":"保存","fontSize":"16px","width":"72px"}]}}},{"path":"/pages/user/stores/unuse_tickets","meta":{},"window":{"navigationBarTitleText":"无效券","enablePullDownRefresh":false}},{"path":"/pages/doctors/videoCall","meta":{},"window":{"navigationBarTitleText":"视频通话","titleNView":{"type":"transparent"}}},{"path":"/pages/order/videoCall","meta":{},"window":{"navigationBarTitleText":"","enablePullDownRefresh":false}},{"path":"/pages/user/permission","meta":{},"window":{"navigationBarTitleText":"权限设置","enablePullDownRefresh":false}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
