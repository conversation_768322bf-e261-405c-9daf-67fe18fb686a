<template>
	<view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" :down="downOption" @down="downCallback" @up="upCallback">
			<navigator class="item" :url="'/pages/medrecord/medrecordDeatils?medid=' + n.id" v-for="n in medList">
					<view class="rightctx">
						<view class="space-between">
						<text class="name">医生：{{n.realname}}</text>
					</view>
					<view class="space-between" v-if="n.cliname">
						<text class="gender">医院：{{n.cliname}}</text>
						<text class="gender">科室：{{n.departmentname}}</text>
					</view>
				</view>
				<view class="flex-start">
					<view class="diagnose">诊断：</view>
					<view class="diagnosectx">{{n.diagnose}}</view>
				</view>
				<view class="time">
					<text class="time">{{n.addDate}}</text>
				</view>
			</navigator>
		</mescroll-body>
	</view>
</template>

<script>
import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
import moment from 'moment';
export default {
	mixins: [MescrollMixin],
	data() {
		return {
			downOption: {
				auto: true //是否在初始化后,自动执行downCallback; 默认true
			},
			medList: [] //病历列表
		};
	},
	methods: {
		/*下拉刷新的回调 */
		downCallback() {
			this.medList=[]
			this.mescroll.resetUpScroll()
		},
		/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
		upCallback(page) {
			// console.log(page)
			this.$api.get("api/patient/medrecord/getlist",{params:{pager: page.num}})
			.then(res=>{
				// this.mescroll.endBySize(res.length);
				this.mescroll.endSuccess(res.length);
				//设置列表数据
				let arr = res.map(n =>{
					n.addDate=moment(n.addDate).format('MM/DD HH:mm:ss')
					return n
				})
				this.medList = this.medList.concat(arr);
				// console.log(this.medList)
			})
			.catch(err => {
				this.mescroll.endErr();
			})
		}
	}
};
</script>

<style lang="scss">
page {
	width: 100%;
	background-color: $uni-bg-color-grey;
	.item{
		margin: 0px $uni-spacing-row-lg;
		margin-top: $uni-spacing-col-lg;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;
	}
	.rightctx{
		width: 100%;
		.space-between{
			.name{
				width: 50%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}
		&>view{
			margin-bottom: $uni-spacing-col-base;
		}
		.name{
			color: $uni-text-color;
			font-size: $uni-font-size-base;
			font-weight: bold;
		}
		.gender{
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
		}
	}
	.time{
		text-align: right;
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
	}
	.diagnose{
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
	}
	.diagnosectx{
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		font-weight: 500;
		width: calc(100% - 100rpx);
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}
.avatar{
	width: 50px;
	height: 50px;
	margin-right: $uni-spacing-row-lg;
	border-radius: $uni-border-radius-circle;
}
</style>
