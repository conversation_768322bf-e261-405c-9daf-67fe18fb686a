<template>
<view>
	<mescroll-body ref="mescrollRef" @init="mescrollInit" :down="downOption" @down="downCallback" @up="upCallback">
		<logistics :wlInfo="wlInfo" v-if="wlInfo !=null"></logistics>
	</mescroll-body>
</view>
</template>

<script>
	import MescrollMixin from "@/components/mescroll-uni/mescroll-mixins.js";
	import logistics from '@/components/xinyu-logistics/xinyu-logistics.vue'
	export default {
		components: { logistics },
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		data() {
			return {
				downOption: {
					auto: true //是否在初始化后,自动执行downCallback; 默认true
				},
				wlInfo: null,
				id:"",
			}
		},
		onLoad(options) {
			console.log(options.id)
			this.id =options.id
		},
		methods: {
			/*下拉刷新的回调 */
			downCallback() {
				let that = this
				uni.showLoading({
					title: '加载中',
					mask: true
				});
				that.$api.get('api/patient/logistics/getdetail', {
						params: {
							id: that.id
						}
					})
					.then(res => {
					uni.hideLoading();
						if (res.success) {
							that.wlInfo = res.data.data
							console.log(that.wlInfo)
							that.wlInfo.traces = that.wlInfo.traces.reverse()
							that.mescroll.endErr();
						} else {
							if (res.code == 1) {
								uni.showModal({
									title: '查询异常',
									content: '订单物流信息异常，您可以稍后再来查询',
									showCancel: false,
									success(res) {
										if (res.confirm) {
											uni.navigateBack({

											})
										}
									}

								})
							} else if (res.code == 2) {
								uni.showModal({
									title: '查询失败',
									content: '暂无此订单的物流信息，您的订单可能尚未发货',
									showCancel: false,
									success(res) {
										if (res.confirm) {
											uni.navigateBack()
										}
									}
								})
							}
						}
						}).catch(err => {
					uni.hideLoading();
				})
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				this.mescroll.endErr();
			}
		}
	}
</script>

<style lang="scss">
	page{
		background-color:$uni-bg-color-grey ;
		padding: $uni-spacing-col-base 0px;
	}
</style>
