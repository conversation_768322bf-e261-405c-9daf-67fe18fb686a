<template>
	<view class="content">
		<view class="row b-b">
			<text class="tit">联系人</text>
			<input class="input" type="text" v-model="addressData.receiver" placeholder="收货人姓名" placeholder-class="placeholder" />
		</view>
		<view class="row b-b">
			<text class="tit">手机号</text>
			<input class="input" type="number" v-model="addressData.telephone" placeholder="收货人手机号码" placeholder-class="placeholder" />
		</view>
		<view class="row b-b">
			<text class="tit">地址</text>
			<!-- <text @click="chooseLocation" class="input">
				{{addressData.address?addressData.address:"在地图选择"}}
			</text> -->
			<textarea auto-height="true" value="" v-model="addressData.address" placeholder="收货人地址" />
			<text class="yticon icon-shouhuodizhi"></text>
		</view>
	<!-- 	<view class="row b-b"> 
			<text class="tit">门牌号</text>
			<input class="input" type="text" v-model="addressData.area" placeholder="楼号、门牌" placeholder-class="placeholder" />
		</view> -->
		
		<view class="row default-row">
			<text class="tit">设为默认</text>
			<switch :checked="addressData.status" color="#fa436a" @change="switchChange" />
		</view>
		<button class="add-btn" @click="confirm">提交</button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				addressData: {
					id:"" , //id
					receiver:"" , //收货人名字，长度2-20字符串
					telephone:"" , //收货电话
					address:"" , //收货地址，长度2-255字符串
					status:false,//是否默认地址，0：非默认地址；1：是默认地址
				},
				isdefault:''
			}
		},
		onLoad(option){
			let title = '新增收货地址';
			if(option.type==='edit'){
				title = '编辑收货地址'
				
				this.addressData = JSON.parse(option.data)
				this.isdefault = this.addressData.status
				this.addressData.status =this.addressData.status?true:false
				// console.log(JSON.stringify(this.addressData) )
			}
			this.manageType = option.type;
			uni.setNavigationBarTitle({
				title
			})
		},
		methods: {
			switchChange(e){
				this.addressData.status = e.detail.value;
			},
			
			//地图选择地址
			chooseLocation(){
				uni.chooseLocation({
					success: (data)=> {
						console.log(data.address)
						this.addressData.addressName = data.name;
						this.addressData.address = data.address;
					}
				})
			},
			
			//提交
			confirm(){
				let data = JSON.parse(JSON.stringify(this.addressData));
				if(!(/^(?:[\u4e00-\u9fa5·]{2,16})$/.test(data.receiver)) ){
					uni.showToast({
						title:'收货人的格式是汉字，长度必须在2和16之间',
						icon:'none'
					})
					return;
				}
				if(!(/^(1[0-9])\d{9}$/.test(data.telephone))){
					uni.showToast({
						title:'请输入正确的手机号码',
						icon:'none'
					})
					return;
				}
				if(data.address.length> 50||data.address.length<= 0){
					uni.showToast({
						title:'收货地址长度在0-50之间',
						icon:'none'
					})
					return;
				}
				// if(!data.area){
				// 	this.$api.msg('请填写门牌号信息');
				// 	return;
				// }
				let url;
				// console.log(this.manageType=='edit')
				if(this.manageType=='edit'){
					url='api/patient/user/editAddresslist'
				}else{
					url='api/patient/user/addAddress'
				}
				data.status = data.status?1:0
				console.log(this.isdefault)
				if(this.manageType=='edit'&&data.status==false&&this.isdefault==1){
					uni.showToast({
						title:'您必须设置一个默认地址！！！',
						icon:'none'
					})
				}else{
					this.$api.post(url,data)
					.then(res =>{
						if(res.success){
							uni.showToast({
								title:`地址${this.manageType=='edit' ? '修改': '添加'}成功`,
								icon:'none',
								success:function(){
									uni.$emit('updateAddress')
									uni.navigateBack()
								}
							})
						}else{
							uni.showToast({
								title:res.msg,
								icon:'none'
							})
						}
					})
				}
				
			},
		}
	}
</script>

<style lang="scss">
	page{
		background: #f8f8f8;
		padding-top: 16upx;
	}

	.row{
		display: flex;
		align-items: center;
		position: relative;
		padding:0 30upx;
		height: 110upx;
		background: #fff;
		
		.tit{
			flex-shrink: 0;
			width: 120upx;
			font-size: 30upx;
			color: #303133;
		}
		.input{
			flex: 1;
			font-size: 30upx;
			color: #303133;
		}
		.icon-shouhuodizhi{
			width: 109upx;
			height: 100%;
			padding: 50upx 0 50upx 58upx;
			box-sizing: border-box;
			background: url('/static/img/user-tab10.png') no-repeat 43upx center;
			background-size: 35upx auto;
		}
	}
	.default-row{
		margin-top: 16upx;
		.tit{
			flex: 1;
		}
		switch{
			transform: translateX(16upx) scale(.9);
		}
	}
	.add-btn{
		display: flex;
		align-items: center;
		justify-content: center;
		width: 690upx;
		height: 80upx;
		margin: 60upx auto;
		font-size: 32upx;
		color: #fff;
		background-color: #fa436a;
		border-radius: 10upx;
		box-shadow: 1px 2px 5px rgba(219, 63, 96, 0.4);
	}
	.address{
		min-height: 20px;
	}
</style>
