<template>
	<view class="guide">
		<view class="header space-between">
			<view class="space-between">
				<text class="rectangle"></text>
				<text style="color: #333333;font-size: 16px;">用户指南</text>
			</view>
		</view>
		<swiper class="swiper" :next-margin="nextMargin" :indicator-dots="indicatorDots" :autoplay="autoplay" :interval="interval" :duration="duration">
			<swiper-item> <navigator hover-class="none" url="../guide/index?link=1"><image src="../../static/img/guide1.png" mode=""></image></navigator> </swiper-item>
			<swiper-item> <navigator hover-class="none" url="../guide/index?link=2"> <image src="../../static/img/guide2.png" mode=""></image></navigator></swiper-item>
		</swiper>
	</view>
</template>

<script>
export default {
	data() {
		return {
			indicatorDots: false,
			autoplay: false,
			interval: 2000,
			duration: 500,
			nextMargin:"40px"
		};
	}
};
</script>

<style lang="scss">
.guide {
	padding: 0px 15px;
	padding-bottom: 25px;
	.header{
		height: 60px;
	}
	.space-between {
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: #333333;
	}
	.rectangle {
		display: inline-block;
		width: 4px;
		height: 18px;
		background-color: #16cc9f;
		margin-right: 20rpx;
	}
	.swiper{
		height: 85px;
	}
	swiper-item{
		height: 85px;
		width: 162px !important;
		image{
			height: 85px;
			width: 155px;
		}
	}
}
</style>
