<template>
	<view class="content">
	</view>
</template>

<script>
	export default {
		data() {
			return {
				app_token: '',
				H5_token: '',
				webVIewShow: false,
				user_id: '',
				discount:''
			}
		},
		onLoad() {},
		created() {
			// 进入页面显示加载中
			uni.showLoading({
					title: '加载中',
					mask: false
				}),
				this.app_token = uni.getStorageSync('token')
			// 通过接口获取商城token和user_id
			this.$api.get('api/patient/auth/gettmptoken').then(res => {
				console.log(res)
				if (res.success) {
					this.H5_token = res.data.token,
						this.user_id = res.data.user_id,
						this.discount = res.data.discount
						this.webVIewShow = true
					//创建页面
					this.createweb()
				}
			})
		},
		methods: {
			ReceiveMessage(event) {
				console.log(event)
			},
			// 创建页面
			createweb() {
				//创建子页面并添加到当前页面 
				var wv = plus.webview.create('https://shop.xxjk99.com/shop/index.html?token='+this.H5_token+'&user_id='+this.user_id+'&discount='+this.discount) 
				var currentWebview = this.$scope.$getAppWebview();
				currentWebview.append(wv);

				//重点: 监听子页面uni.postMessage返回的值  
				plus.globalEvent.addEventListener('plusMessage', function(msg) {
					if (msg.data.args.data.name == 'postMessage') {
						let success = msg.data.args.data.arg.success
						console.log(success)
						// 登陆成功显示页面
						if (success) {
							uni.hideLoading()
						} else { //登录失败退出页面
							uni.hideLoading()
							uni.showModal({
								title: '提示',
								content: '商城登录异常，请重新登陆后重试',
								showCancel: false,
								success(res) {
									if (res.confirm) {
										uni.reLaunch({
											url: '../index/index',
										});
									}
								},
							});
						}
					}
				});
			},
		}
	}
</script>

<style lang="scss">
	.content {
		background: linear-gradient(-8deg, rgba(95, 231, 194, 1), rgba(88, 201, 238, 1));
	}
</style>
