<template>
	<view>
		<view class="main">
			<wInput v-model="verCode" type="number" maxlength="6" placeholder="验证码" isShowCode ref="runCode" @setCode="getVerCode()"></wInput>
			<view class="tip">为了您的帐号安全，请输入发送给该账号绑定的手机号的验证码。</view>
		</view>
		<wButton class="button" text="注销账号" @click.native="startReg()"></wButton>
	</view>
</template>

<script>
import wInput from '../../../components/watch-login/watch-input.vue'; //input
import wButton from '../../../components/watch-login/watch-button.vue'; //button
export default {
	components: {
		wInput,
		wButton
	},
	data() {
		return {
			verCode: '' //验证码
		};
	},
	methods: {
		getVerCode() {
			let that = this;
			//获取验证码
			let phonedata = that.$store.state.userInfo.telephone;
			console.log(that.$store.state.userInfo)
			that.$refs.runCode.$emit('runCode'); //触发倒计时（一般用于请求成功验证码后调用）
			setTimeout(() => {
				that.$refs.runCode.$emit('runCode', 0);
			}, 60000);
			that.$api
				.get('api/patient/getsmscode', { params: { telephone: phonedata } })
				.then(res => {
					console.log(res);
				})
				.catch(err => {});
		},
		startReg() {
			this.$api.post('api/patient/auth/unregister', { smscode: this.verCode }).then(res=>{
				if(res.cancellation){
					uni.redirectTo({
						url: './result',
					});
				}else{
					let tip = res.msg
					if(res.cancellation!=undefined){
						tip = tip +'，请先处理后再进行注销'
					}
					uni.showToast({
						title:tip,
						icon:"none"
					})
				}
			});
			
		}
	}
};
</script>

<style lang="scss">
@import url('../../../components/watch-login/css/icon.css');
@import url('../../login/css/main.css');
.tip {
	font-size: 26rpx;
	color: #555555;
}
.button {
	margin-top: 20rpx;
}
</style>
