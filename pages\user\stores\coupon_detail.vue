<template>
	<view>
		<view class="ticket" >
			<view class="item_left">
				<view class="title">
					{{couponInfo.title}}
				</view>
				<view class="times">
					<view>{{couponInfo.endTime}}到期</view>
				</view>
				<view class="use">
					满{{couponInfo.limit}}可用
				</view>
			</view>
			<view class="item_right">
				<view class="discounts">
					{{couponInfo.type==1?couponInfo.discount+'元':(couponInfo.discount/10)+'折'}}
				</view>
				<view class="type">
					{{couponInfo.type==1?'满减券':'折扣券'}}
				</view>
			</view>
		</view>
		<view class="couponcode">
			<view class="title">
				优惠券码
			</view>
			<view class="code">
				{{couponInfo.code}}
			</view>
		</view>
		<view class="userRule">
			<view class="title">
				使用规则
			</view>
			<view class="rule">
				{{couponInfo.describe}}
			</view>
		</view>
		<view class="userRule">
			<view class="title">
				补充说明
			</view>
			<view class="rule">
				{{couponInfo.statement}}
			</view>
		</view>
		</view>
</template>

<script>
	export default {
		data() {
			return {
				couponInfo:{}
			};
		},
		onLoad(option) {
			this.couponInfo = JSON.parse(option.data)
		}
	}
</script>

<style lang="scss">
.ticket{
	width: 90%;
	margin: 10rpx auto;
	border: solid 1rpx #000;
	border-radius: 24rpx;
	display: flex;
	justify-content: space-between;
	padding: 24rpx;
	box-sizing: border-box;
	font-size: 28rpx;
	.item_left{
		.title{
			font-size: 36rpx;
			font-weight: bold;
		}
		.times{
			display: flex;
			margin-top: 10rpx;
		}
		.use{
			margin-top: 10rpx;
		}
	}
	.item_right{
		text-align: center;
		.discounts{
			font-size: 40rpx;
		}
		.type{
			margin-top: 10rpx;
		}
		.btn{
			width: 160rpx;
			line-height: 50rpx;
			background-color: $jian-bg-color;
			color: #fff;
			border-radius: 25rpx;
			margin-top: 20rpx;
		}
	}
}
.userRule{
	padding: 0 36rpx;
	margin-top: 20rpx;
	.title{
		font-size: 36rpx;
		font-weight: bold;
	}
	.rule{
		font-size: 28rpx;
		margin-top: 20rpx;
	}
}
.couponcode{
	margin-top: 20rpx;
	padding: 0 36rpx;
	.title{
		font-size: 36rpx;
		font-weight: bold;
	}
	.code{
		width: 100%;
		font-size: 100rpx;
		font-weight: bold;
		color: $jian-bg-color;
		text-align: justify;
	}
}
</style>
