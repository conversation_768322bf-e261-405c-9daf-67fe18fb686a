<template>
	<view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" :down="downOption" @down="downCallback" @up="upCallback">
			<view class="item" v-for="n in medList">
				<view class="flex-start">
					<view>
						<image class="avatar" v-if="n.face" :src="$baseUrl+n.face" mode=""></image>
						<image class="avatar" v-if="!n.face" src="../../static/img/defaultman.png" mode=""></image>
					</view>
					<view class="rightctx">
						<view class="space-between">
							<text class="name">{{n.doctor}}医生</text>
							<text class="gender">科室：{{n.department}}</text>
							<text class="state" v-if="n.status==0">待诊</text>
							<text class="state" v-if="n.status==1">已诊</text>
							<text class="state" v-if="n.status==2">接诊中</text>
							<text class="state" v-if="n.status==3">已过期</text>
							<text class="state" v-if="n.status==4">已预约</text>
						</view>
						<view class="space-between">
							<text class="time">预约时间：{{n.arriveDate}}</text>
						</view>
						<view class="space-between">
							<text class="gender">诊所：{{n.cliname}}</text>
						</view>
						<view class="space-between">
							<text class="gender">地址：{{n.cliaddress}}</text>
						</view>
						<navigator class="flex-end" v-if="n.status==1||n.status==3" :url="'/pages/medrecord/medrecordDeatils?medid=' + n.medrecord_id">
							<button class="gotoMedrecord" size="mini">查看病历</button>
						</navigator>
						<navigator class="flex-end" v-if="n.status==0" :url="'/pages/user/queue?id=' + n.id">
							<button class="gotoMedrecord" size="mini">查看排队</button>
						</navigator>
					</view>
				</view>
			</view>
		</mescroll-body>
	</view>
</template>

<script>
import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
import moment from 'moment';
export default {
	mixins: [MescrollMixin],
	data() {
		return {
			downOption: {
				auto: true //是否在初始化后,自动执行downCallback; 默认true
			},
			medList: [] //病历列表
		};
	},
	methods: {
		/*下拉刷新的回调 */
		downCallback() {
			this.medList=[]
			this.mescroll.resetUpScroll()
		},
		/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
		upCallback(page) {
			// console.log(page)
			this.$api.get("api/patient/register/getlist",{params:{isreservation: 1,pager:page.num}})
			.then(res=>{
				this.mescroll.endSuccess(res.length);
				this.medList = this.medList.concat(res);
				console.log(this.medList)
			})
			.catch(err => {
				this.mescroll.endErr();
			})
		}
	}
};
</script>

<style lang="scss">
page {
	width: 100%;
	background-color: $uni-bg-color-grey;
	.item{
		margin: 0px $uni-spacing-row-lg;
		margin-top: $uni-spacing-col-lg;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;
	}
	.rightctx{
		width: 100%;
		&>view{
			margin-bottom: $uni-spacing-col-base;
		}
		.name{
			color: $uni-text-color;
			font-size: $uni-font-size-base;
			font-weight: bold;
		}
		.time,.gender{
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
		}
	}
	.diagnose{
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
		width:80px;
		margin-right: $uni-spacing-row-lg;
	}
	.diagnosectx{
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		font-weight: 500;
	}
}
.avatar{
	width: 50px;
	height: 50px;
	margin-right: $uni-spacing-row-lg;
	border-radius: $uni-border-radius-circle;
}
.state{
	color: $jian-bg-color;
}

.gotoMedrecord{
	display: block;
	background-color: $jian-bg-color;
	color: #fff;
	width: 100px;
}
</style>
