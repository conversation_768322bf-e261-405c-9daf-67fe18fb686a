<template>
	<view>
		<view class="header">
			<view class="status_bar"></view>
			<view class="space-between" v-if="data != null">
				<text class="status" v-if="data.status == 3 && data.evaluation == null">待评价</text>
				<text class="status" v-if="data.status == 3 && data.evaluation != null">已评价</text>
				<text class="status" v-if="data.status == 0">待付款</text>
				<text class="status" v-if="data.status == 1">待发货</text>
				<text class="status" v-if="data.status == 2">待收货</text>
				<image class="tab" v-if="data.status == 3 && data.evaluation == null" src="/static/img/order_img/3.png"
					mode=""></image>
				<image class="tab" v-if="data.status == 3 && data.evaluation != null" src="/static/img/order_img/3.png"
					mode=""></image>
				<image class="tab" v-if="data.status == 2" src="/static/img/order_img/2.png" mode=""></image>
				<image class="tab" v-if="data.status == 1" src="/static/img/order_img/1.png" mode=""></image>
				<image class="tab" v-if="data.status == 0" src="/static/img/order_img/0.png" mode=""></image>
			</view>
		</view>
		<mescroll-body ref="mescrollRef" @init="mescrollInit" :down="downOption" :up="upOption" @down="downCallback"
			@up="upCallback">
			<view class="details" v-if="data != null">
				<view class="block" v-if="data.status > 1&&!openlist">
					<view class="logistics flex-start" v-if="data.logistics.length">
						<image class="addressimg" src="/static/img/order_img/address.png" mode=""></image>
						<view class="">
							<view class="top">
								<text>{{ data.logistics[0].receiver }}</text>
								<text>{{ data.logistics[0].telephone }}</text>
							</view>
							<text class="bottom">{{ data.logistics[0].address }}</text>
						</view>
					</view>
					<view class="flex-end-center logisticsbtn" @tap="currlogisticsid = data.logistics[0].id"
						v-if="data.logistics.length">
						<picker mode="date"
							v-if="data.status == 2 && data.logistics.length > 0&&data.logistics[0].traffic_status==6 "
							:start="startDate" :end="endDate" @change="bindDateChange">
							<text class="button">延长收货时间</text>
						</picker>
						<text @tap="hasreceive(data.logistics[0].id)"
							v-if="data.status == 2 && data.logistics.length > 0&&data.logistics[0].traffic_status==6"
							class="button">确认收货</text>
						<navigator :url="'./logistics?id='+data.logistics[0].id">
							<text class="button" v-if="data.status != 0 && data.logistics.length > 0">查看物流</text>
						</navigator>
					</view>
					<view class="seemore" v-if="data.logistics.length > 0&&!openlist" @tap="openlist=true">
						查看更多物流>
					</view>
				</view>
				<view class="block" v-if="data.status > 1&&openlist" v-for="n in data.logistics">
					<view class="logistics flex-start">
						<image class="addressimg" src="/static/img/order_img/address.png" mode=""></image>
						<view class="">
							<view class="top">
								<text>{{ n.receiver }}</text>
								<text>{{ n.telephone }}</text>
							</view>
							<text class="bottom">{{ n.address }}</text>
						</view>
					</view>
					<view class="flex-end-center logisticsbtn" @tap="currlogisticsid = n.id">
						<picker mode="date" v-if="data.status == 2 && data.logistics.length > 0&&n.traffic_status==6 "
							:start="startDate" :end="endDate" @change="bindDateChange">
							<text class="button">延长收货时间</text>
						</picker>
						<text @tap="hasreceive(n.id)"
							v-if="data.status == 2 && data.logistics.length > 0&&n.traffic_status==6"
							class="button">确认收货</text>
						<navigator :url="'./logistics?id='+n.id">
							<text class="button" v-if="data.status != 0 && data.logistics.length > 0">查看物流</text>
						</navigator>
					</view>
				</view>
				<view class="seemore closemore" v-if="openlist" @tap="openlist=false">
					收起物流列表
				</view>
				<view class="block">
					<view class="prescription">
						<view class="gai">
							<view class="flex-start-center title">
								<image class="titleimg" src="/static/img/order_img/prescription2.png" mode=""></image>
								<text>开方订单</text>
							</view>
							<view>
								<text class="label">医生姓名:</text>
								<text class="ctx">{{ data.prescription.doctname }}</text>
							</view>
							<view v-if="data.prescription.department != null">
								<text class="label">科室:</text>
								<text class="ctx">{{ data.prescription.department }}</text>
							</view>
							<view>
								<text class="label">诊所名:</text>
								<text class="ctx">{{ data.prescription.cliname }}</text>
							</view>
							<view>
								<text class="label">诊所地址:</text>
								<text class="ctx">{{ data.prescription.cliaddress }}</text>
							</view>
							<view>
								<text class="label">诊断:</text>
								<text class="ctx">{{ data.prescription.diagnosis }}</text>
							</view>
							<view>
								<text class="label">创建订单时间:</text>
								<text class="ctx">{{ data.addDate }}</text>
							</view>
						</view>
						<view class="space-around">
							<navigator :url="'../medrecord/medrecordDeatils?medid=' + data.prescription.medid">查看处方详情>
							</navigator>
						</view>
						<view class="evaluation" v-if="data.evaluation != null">
							<view>
								<text>评价</text>
								<view class="grey" style="margin-bottom: 10px;">
									<text>{{ data.evaluation.evaluation }}</text>
								</view>
								<view class="rate">
									<uni-rate size="16" :value="data.evaluation.score" disabled="true"></uni-rate>
								</view>
								<view class="flex-end-center">
									<text class="grey">{{ data.evaluation.time }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="block">
					<view class="amount">
						<view class="flex-start-center title">
							<image class="amountimg" src="/static/img/order_img/amountimg.png" mode=""></image>
							<text>订单金额</text>
						</view>
						<view class="space-between">
							<text class="label">药费</text>
							<text class="ctx" v-if="data.status == 0">￥{{ data.unpaidMedicineFee }}</text>
							<text class="ctx" v-if="data.status != 0">￥{{ data.medicineFee }}</text>
						</view>
						<view class="space-between">
							<text class="label">加工费</text>
							<text class="ctx" v-if="data.status == 0">￥{{ data.unpaidProcessingFee }}</text>
							<text class="ctx" v-if="data.status != 0">￥{{ data.processingFee }}</text>
						</view>

						<view class="space-between" v-for="n in data.check">
							<text class="label">{{ n.checkName }}</text>
							<text v-if="data.status == 0" class="ctx">￥{{ n.unpaidCheckAmount }}</text>
							<text v-if="data.status != 0" class="ctx">￥{{ n.checkAmount }}</text>
						</view>
						<view class="space-between" v-for="n in data.extra">
							<text class="label">{{ n.extraName }}</text>
							<text v-if="data.status == 0" class="ctx">￥{{ n.unpaidExtraAmount }}</text>
							<text v-if="data.status != 0" class="ctx">￥{{ n.extraAmount }}</text>
						</view>
						<view class="space-between">
							<text class="label">运费</text>
							<text v-if="data.status == 0" class="ctx">￥{{ data.unpaidFreight }}</text>
							<text v-if="data.status != 0" class="ctx">￥{{ data.freight }}</text>
						</view>
						<view class="space-between">
							<text class="label">实付款</text>
							<text class="ctx">￥{{ data.paidAmount }}元</text>
						</view>
						<view class="space-between">
							<text class="label">待付款</text>
							<text class="ctx">￥{{ data.nopaying }}元</text>
						</view>
						<view class="space-between" v-if="parseFloat(data.refund)">
							<text class="label">已退费</text>
							<text class="ctx">￥{{ data.refund }}元</text>
						</view>
					</view>
					<view class="space-between pay">
						<text class="discount">*优惠金额￥{{ data.discount }}</text>
						<text class="payed">总价：￥{{ data.totalprice }}</text>
					</view>
				</view>
				<view class="block">
					<view class="payinfo">
						<view class="flex-start-center">
							<text class="label">订单编号</text>
							<text class="ctx">{{ data.orderNumber }}</text>
						</view>
						<view class="flex-start-center">
							<text class="label">支付方式</text>
							<!-- 支付类型0支付宝1现金2微信3银行卡4其他 -->
							<text class="ctx" v-if="data.paymentMethod == 0">支付宝</text>
							<text class="ctx" v-if="data.paymentMethod == 1">现金</text>
							<text class="ctx" v-if="data.paymentMethod == 2">微信</text>
							<text class="ctx" v-if="data.paymentMethod == 3">银行卡</text>
							<text class="ctx" v-if="data.paymentMethod == 4">其他</text>
							<text class="ctx" v-if="data.paymentMethod == 5">余额</text>
						</view>
						<view class="flex-start-center" v-if="data.status != 0">
							<text class="label">交易时间</text>
							<text class="ctx">{{ data.time }}</text>
						</view>
					</view>
				</view>
				<view class="block flex-end-center"
					v-if="data.status == 0 || (data.status == 3 && data.evaluation == null)">
					<view v-if="data.status == 0" class="nopaying">
						<text>合计</text>
						<text>￥{{ data.nopaying }}</text>
						<text class="button" v-if="data.status == 0" @tap="pay(data.orderNumber)">付款</text>
					</view>
					<view class="flex-end-center" v-if="data.status == 3 && data.evaluation == null">
						<!-- <text class="button" v-if="data.status == 2 && data.logistics.length > 0">延长收货时间</text>
						<text class="button" v-if="data.status == 2 && data.logistics.length > 0">确认收货</text> -->
						<text class="button editevaluation" @tap="edit()">评价</text>
						<!-- <text class="button" v-if="data.status != 0 && data.logistics.length > 0">查看物流</text> -->
					</view>
				</view>
			</view>
		</mescroll-body>
		<paytypeselector v-if="payTypeShow" @payTypeConfirm="payTypeConfirm" @payTypeCancel="payTypeCancel">
		</paytypeselector>
		<ygc-comment ref="ygcComment" :placeholder="'发布评论'" @pubComment="evaluation"></ygc-comment>
	</view>
</template>

<script>
	let that = null;
	import moment from 'moment';
	import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins.js';
	import uniRate from '@/components/uni-rate/uni-rate.vue';
	import Paytypeselector from '@/pages/order/paytypeselector.vue';
	export default {
		mixins: [MescrollMixin], // 使用mixin (在main.js注册全局组件)
		components: {
			uniRate,
			Paytypeselector
		},
		data() {
			return {
				downOption: {
					auto: true //是否在初始化后,自动执行downCallback; 默认true
				},
				upOption: {
					use: false
				},
				payTypeShow: false, //是否显示支付方式选择器
				data: null,
				orderid: null,
				startDate: moment().format('YYYY-MM-DD'),
				endDate: moment()
					.add(15, 'days')
					.format('YYYY-MM-DD'),
				currlogisticsid: '', //当前快递id
				openlist: false
			};
		},
		onLoad(options) {
			that = this;
			this.orderid = options.orderid;
		},
		methods: {
			/*下拉刷新的回调 */
			downCallback() {
				this.getdetail();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {},
			getdetail() {
				uni.showLoading({
					title: '加载中',
					mask: true
				});
				this.$api
					.get('api/patient/order/getdetail', {
						params: {
							ordernumber: this.orderid
						}
					})
					.then(res => {
						uni.hideLoading();
						// console.log(res);
						this.mescroll.endErr();
						this.data = res;
					})
					.catch(err => {
						uni.hideLoading();
						this.mescroll.endErr();
					});
			},
			payTypeCancel() {
				this.payTypeShow = false;
			},
			payTypeConfirm(val) {
				let that = this
				this.payTypeShow = false;
				this.$api
					.post('api/patient/order/payold', {
						ordernumber: this.orderid,
						pay_type: val
					})
					.then(res => {
						console.log('paytest', res)
						if (res.success) {
							let provider
							if (val == 0) {
								provider = 'alipay'
							} else if (val == 2) {
								provider = 'wxpay'
							}
							// var EnvUtils = plus.android.importClass("com.alipay.sdk.app.EnvUtils");
							// EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);
							uni.requestPayment({
								provider: provider,
								orderInfo: res.data.PayStr,
								success(res) {
									uni.showToast({
										title: '支付成功！',
										duration: 2000,
										success() {
											that.$emit('tabChange');
											uni.showModal({
												title: '温馨提示',
												content: '支付成功！',
												success(data) {
													that.mescroll.triggerDownScroll();
												}
											});
										}
									});
								},
								fail(err) {
									uni.showModal({
										title: '支付异常',
										content: "支付失败，请重新支付",
										showCancel: false,
										success() {}
									});
								}
							});
						} else {
							uni.showToast({
								title: res.msg,
								icon: "none"
							})
						}
					})
					.catch(err => {
						uni.showToast({
							title: '支付异常，请稍后重试',
							icon: "none"
						})
						console.log(err)
					});
			},
			pay() {
				//支付订单
				this.payTypeShow = true;

			},
			edit() {
				this.$refs.ygcComment.toggleMask('show');
			},
			evaluation(val) {
				//评价
				this.$refs.ygcComment.toggleMask();
				this.$api
					.post('api/patient/evaluation/evaluate', {
						orderid: this.data.id,
						evaluation: val.content,
						score: val.score
					})
					.then(res => {
						// console.log(res);
						this.$emit('tabChange');
						uni.showModal({
							title: '温馨提示',
							content: '已评价成功',
							success(data) {
								that.mescroll.triggerDownScroll();
							}
						});
					})
					.catch(err => {
						console.log(err);
					});
			},
			hasreceive(val) {
				uni.showModal({
					title: "温馨提示",
					content: "是否确认已收到货品？",
					success(b) {
						if (b.confirm) {
							//确认收货
							that.$api
								.post('api/patient/logistics/hasreceive', {
									id: val, //必填，正整数，快递id
									status: 5
								})
								.then(res => {
									console.log(res)
									if (res.success) {
										uni.showToast({
											title: "收货成功！",
											success() {
												setTimeout(() => {
													that.mescroll
														.triggerDownScroll();
												}, 1000)
											}
										})
									}
								})
								.catch(err => {});
						}
					}
				})
			},
			bindDateChange: function(e) {
				console.log(e);
				this.updatereceipttime(e.detail.value);
			},
			updatereceipttime(val) {
				//延长收货
				this.$api
					.post('api/patient/logistics/updatereceipttime', {
						id: this.currlogisticsid, //必填，正整数，快递id
						receiptTime: val
					})
					.then(res => {
						uni.showModal({
							title: '温馨提示',
							content: '已延长收货时间',
							showCancel: false
						});
					})
					.catch(err => {});
			},
			getLogisticsDetail(val) {
				this.$api
					.get('api/patient/logistics/getdetail', {
						params: {
							id: this.currlogisticsid, //必填，正整数，快递id
							status: 5
						}
					})
					.then(res => {
						this.mescroll.triggerDownScroll();
					})
					.catch(err => {});
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;
	}

	.header {
		padding: 40rpx $uni-spacing-row-lg;
		padding-top: 70rpx;
		background: linear-gradient(-8deg, rgba(95, 231, 194, 1), rgba(88, 201, 238, 1));

		.status {
			color: $uni-text-color-inverse;
		}

		.tab {
			width: 100rpx;
			height: 100rpx;
		}
	}

	.details {
		.block {
			background-color: $uni-bg-color;
			margin-bottom: $uni-spacing-col-lg;
			padding: $uni-spacing-col-lg 0px;
			box-shadow: 0px 4px 6px 0px rgba(0, 0, 0, 0.05);
		}

		.logistics {
			margin: 0px $uni-spacing-row-lg;

			.addressimg {
				margin-top: $uni-spacing-col-base;
				margin-right: $uni-spacing-row-base;
				width: 35px;
				height: 30px;
			}

			.top {
				color: $uni-text-color;
				font-size: $uni-font-size-base;
			}

			.bottom {
				color: $uni-text-color-grey;
				font-size: $uni-font-size-base;
			}
		}

		.label {
			display: inline-block;
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
			width: 100px;
			margin-bottom: $uni-spacing-col-base;
		}

		.ctx {
			color: $uni-text-color;
			font-size: $uni-font-size-base;
			margin-bottom: $uni-spacing-col-base;
		}

		.prescription {
			.gai {
				margin: 0px $uni-spacing-row-lg;
				border-bottom: 1px dashed rgba(225, 225, 225, 1);

				.titleimg {
					width: $uni-img-size-sm;
					height: $uni-img-size-sm;
					margin-right: $uni-spacing-row-sm;
				}
			}

			.evaluation {
				margin-bottom: $uni-spacing-col-lg;
				background-color: #e7fffa;
				color: $uni-text-color;
				font-size: $uni-font-size-base;

				&>view {
					margin: 0px $uni-spacing-row-base;
				}

				.grey {
					color: $uni-text-color-grey;
				}
			}

			navigator {
				color: $uni-text-color-grey;
				font-size: $uni-font-size-base;
				margin: $uni-spacing-col-base 0px;
			}
		}

		.amount {
			margin: 0px $uni-spacing-row-lg;

			.flex-start {
				padding-bottom: $uni-spacing-col-sm;
			}

			.title {
				margin-bottom: $uni-spacing-col-sm;
			}

			.amountimg {
				width: $uni-img-size-sm;
				height: $uni-img-size-sm;
				margin-right: $uni-spacing-row-sm;
			}

			.label {
				color: $uni-text-color-grey;
				font-size: $uni-font-size-base;
			}

			border-bottom: 1px dashed rgba(225, 225, 225, 1);
		}

		.discount {
			color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
		}

		.pay {
			margin: 0px $uni-spacing-row-lg;
			margin-top: $uni-spacing-row-sm;

			.payed {
				color: $uni-text-color;
				font-size: $uni-font-size-base;
				font-weight: bold;
			}
		}

		.payinfo {
			margin: 0px $uni-spacing-row-lg;

			&>view {
				margin-bottom: $uni-spacing-col-sm;
			}

			.label {
				display: inline-block;
				color: $uni-text-color-grey;
				font-size: $uni-font-size-base;
				width: 100px;
			}

			.ctx {
				color: $uni-text-color;
				font-size: $uni-font-size-base;
			}
		}

		.button {
			display: inline-block;
			color: #ec4040;
			border: 1px solid #ec4040;
			font-size: $uni-font-size-base;
			border-radius: $uni-border-radius-lg * 2;
			padding: $uni-spacing-col-sm/2 $uni-spacing-row-sm;
			margin-left: $uni-spacing-row-lg;
		}

		.nopaying {
			font-size: $uni-font-size-base;
			color: $uni-text-color;
			margin-right: $uni-spacing-row-lg;
		}

		.logisticsbtn {
			margin-right: $uni-spacing-row-lg;

			.button {
				margin-top: $uni-spacing-col-lg;
				display: inline-block;
				color: #ec4040;
				border: 1px solid #ec4040;
				font-size: $uni-font-size-base;
				border-radius: $uni-border-radius-lg * 2;
				padding: $uni-spacing-col-sm/2 $uni-spacing-row-sm;
				margin-left: $uni-spacing-row-lg;
			}
		}

		.editevaluation {
			margin-right: $uni-spacing-row-lg;
		}
	}

	.status_bar {
		height: var(--status-bar-height);
		width: 100%;
	}

	.rate {
		position: relative;
	}

	.seemore {
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
		margin: $uni-spacing-col-base 0px;
		padding-top: 15rpx;
		text-align: center;
		border-top: 1px dashed rgba(225, 225, 225, 1);
	}

	.closemore {
		padding: 15rpx 0;
		background-color: #fff;
		margin-top: -$uni-spacing-col-lg;
	}
</style>