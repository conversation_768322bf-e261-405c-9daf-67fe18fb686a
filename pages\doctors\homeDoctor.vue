<template>
	<view class="base">
		<view class="details ">
			<view class="status_bar"></view>
			<!-- 分享按钮 -->
			<view class="flex-start">
				<image class="left" v-if="details.face" :src="$baseUrl+details.face" mode=""></image>
				<image class="left" v-if="!details.face&&details.gender==1" src="../../static/img/defaultman.png"
					mode=""></image>
				<image class="left" v-if="!details.face&&details.gender==0" src="../../static/img/defaultgirl.png"
					mode=""></image>
				<view class="rigth">
					<view class="flex-start">
						<text class="name">{{details.realname}}</text>
					</view>
					<view class="flex-start">
						<text v-if="details.departmentname">{{details.departmentname}}</text>
						<text>{{details.professional}}</text>
					</view>
					<view class="space-between">
						<text>{{details.cliname}}</text>
					</view>
					<view class="flex-start">
						<text>服务人数:{{details.number}}</text>
						<text>评分:{{details.score}}分</text>
					</view>
					<view>
						<text class="label" v-for="n in details.postcard">{{n}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="space-padding">
			<view class="desc">
				<view class="item"><text>&bull;可通过文字、图片的形式和医生沟通</text></view>
				<view><text>&bull;在您购买的服务期内，可随时和医生沟通，不限次数</text></view>
			</view>
			<view class="pay">
				<view class="service" v-for="item in serviceslist" :class="{chosed:serviceid==item.id}"
					@tap="serviceid=item.id">
					<text>{{item.day}}天({{item.price}}元)</text>
				</view>
			</view>
			<!-- #ifdef APP-PLUS -->
			<view class="select">选择支付方式：</view>
			<view class="paytype flex-start">
				<image class="alipay" src="/static/img/doctors_img/zhifubao.png" mode=""></image>
				<view class="right space-between">
					<view>
						<view><text>支付宝支付</text></view>
						<view><text>推荐支付宝用户使用</text></view>
					</view>
					<image src="/static/img/doctors_img/dui.png" mode="" v-if="pay_type==0" @click="Alipay"></image>
					<image src="/static/img/doctors_img/uncheck.png" mode="" v-if="pay_type != 0" @click="Alipay">
					</image>
				</view>
			</view>

			<view class="paytype flex-start">
				<image class="alipay" src="/static/img/doctors_img/weixin.png" mode=""></image>
				<view class="right space-between">
					<view>
						<view><text>微信支付</text></view>
						<view><text>推荐微信用户使用</text></view>
					</view>
					<image src="/static/img/doctors_img/dui.png" mode="" v-if="pay_type==2" @click="WeChat"></image>
					<image src="/static/img/doctors_img/uncheck.png" mode="" v-if="pay_type != 2" @click="WeChat">
					</image>
				</view>
			</view>
			<view class="select">用户须知：</view>
			<!-- #endif -->
			<view class="desc">
				<view><text>&bull;医生将在12小时内接诊，逾期未回复您可选择继续等待或选择退款。</text></view>
				<view><text>&bull;医生将与您通过图片、文字进行交流。</text></view>
				<view><text>&bull;医生将根据患者的实际情况给出诊疗、调理建议。</text></view>
				<view><text>&bull;购买成功后请如实填写健康档案并发送给医生，医生将按照接诊先后顺序回复。</text></view>
				<view><text>&bull;医生每周有设置休息日和工作日，在工作日提问会更快得到回复。</text></view>
				<view><text>&bull;本服务有效期：在购买的服务套餐设置的有效期内有效。</text></view>
				<view><text>&bull;购买即同意<navigator hover-class="none" url="../login/protocol">《用户协议》（包含远程医疗服务风险告知及知情同意书）
						</navigator> 及 <navigator hover-class="none" url="../login/privacy">《隐私保护政策》</navigator></text>
				</view>
			</view>
			<view class="emptyBtn"></view>
			<button class="buyButton" @click="inquiryNewOrder()" id="newOrder" type="default">购买</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				pay_type: -1,
				// show:false, //支付宝支付默认为未勾选
				flag: true, //微信支付默认为未勾选
				downOption: {
					auto: false //是否在初始化后,自动执行downCallback; 默认true
				},
				details: '',
				serviceslist: [],
				serviceid: 0
			};
		},
		onLoad(option) {

			this.details = JSON.parse(decodeURIComponent(option.details));
			console.log(this.details)
			this.getdoctorprice()
		},
		methods: {
			getdoctorprice() {
				this.$api.get('api/patient/family_doctor/getdetail', {
					params: {
						id: this.details.id
					}
				}).then(res => {
					if (res.success) {
						this.serviceslist = res.data.services
						this.serviceid = res.data.services[0].id
					}
				})
			},
			// // 点击了支付宝支付之后 微信支付为未勾选状态
			Alipay() {
				this.pay_type = 0
			},
			// 点击了微信支付之后 支付宝支付为未勾选状态
			WeChat() {
				this.pay_type = 2
			},
			// #ifdef APP-PLUS
			pay(OrderNumber) {
				let that = this
				// 请求支付接口
				that.$api.post('api/patient/order/payold', {
					ordernumber: OrderNumber,
					pay_type: that.pay_type
				}).then(res => {
					if (res.success) {
						let provider
						if (that.pay_type == 0) {
							provider = 'alipay'
						} else if (that.pay_type == 2) {
							provider = 'wxpay'
						}
						// 测试服调用沙盒代码代码，生产服删除
						//						var EnvUtils = plus.android.importClass("com.alipay.sdk.app.EnvUtils");
						//						EnvUtils.setEnv(EnvUtils.EnvEnum.SANDBOX);
						// 调用支付
						uni.requestPayment({
							provider: provider,
							orderInfo: res.data.PayStr,
							success(res) {
								uni.showToast({
									title: '支付成功！',
									duration: 2000,
									success() {
										uni.$emit('updateInquiry');
										uni.switchTab({
											url: '../inquiry/index'
										});
									}
								});
							},
							fail(err) {
								uni.showModal({
									title: '支付异常',
									content: "支付失败，请前往订单页面完成支付",
									showCancel: false,
									success() {
										uni.navigateTo({
											url: "../order/index?tabIndex=1"
										})
									}
								})
							}
						})
					}else{
						uni.showToast({
							title:res.msg,
							icon:'none'
						})
					}
				})
			},
			// #endif
			// app支付
			inquiryNewOrder() {
				let that = this;
				if (that.pay_type == -1) {
					uni.showToast({
						title: '请选择支付方式',
						icon: 'error'
					})
					return
				}
				uni.showModal({
					title: '温馨提示',
					content: '是否购买私人医生服务？',
					success: function(result) {
						console.log("------")
						if (result.confirm) {
							console.log(result.confirm)
							that.$api
								.post('api/patient/family_doctor/generate', {
									doctor: that.details.id,
									pay_type: that.pay_type,
									service_id: that.serviceid,
									day: that.serviceslist.find(item => item.id == that.serviceid).day,
									price: that.serviceslist.find(item => item.id == that.serviceid).price
								})
								.then(res => {
									console.log(res)
									if (res.success) {
										that.pay(res.msg.OrderNumber);
									} else {
										uni.showModal({
											title: "温馨提示",
											content: res.msg,
											showCancel: false,
											success(data) {
												if (data.confirm) {
													if (res.code == 8) {
														uni.navigateTo({
															url: "../order/index?tabIndex=1"
														})
													} else if (res.code == 2) {
														uni.navigateTo({
															url: "../user/userDetails"
														})
													} else if (res.code == 9) {
														console.log('res.code == 9', res.code)
														uni.switchTab({
															url: "../inquiry/index"
														})
													}
													if (res.data && res.code == 6) {
														let index = that.serviceslist.findIndex((
															i) => i.id === that.serviceid)
														let updata = JSON.parse(JSON.stringify(that
															.serviceslist[index]))
														updata.price = res.data.price
														updata.day = res.data.day
														that.$set(that.serviceslist, index, updata)
													}
												}
											}
										})
									}
								})
								.catch(err => {
									console.log(err)
								});

						}
					}
				});

				return;

			},
		}
	};
</script>

<style lang="scss">
	page {
		background-color: $uni-bg-color-grey;

	}

	.status_bar {
		height: 70px;
		width: 100%;
	}

	.space-padding>view {
		margin-top: $uni-spacing-col-lg;
		margin-right: $uni-spacing-row-lg;
		margin-left: $uni-spacing-row-lg;
	}

	.details {
		background-color: $jian-bg-color;
		background: linear-gradient(-8deg, rgba(95, 231, 194, 1), rgba(88, 201, 238, 1));

		image {
			width: 80px;
			height: 60px;
			border-radius: $uni-border-radius-circle*2;
		}

		color: $uni-text-color-inverse;
		font-size: $uni-font-size-base;

		.left {
			margin: $uni-spacing-col-lg 0px;
			margin-left: $uni-spacing-row-lg;
			margin-top: 20px;
		}

		.rigth {
			width: 100%;
			margin: $uni-spacing-col-lg $uni-spacing-row-sm;

			.btn {
				position: absolute;
				right: $uni-spacing-row-sm;
				border: 1px solid #FFFFFF;
				padding: $uni-spacing-col-sm $uni-spacing-row-sm ;
				border-radius: $uni-border-radius-lg*2;
				font-weight: 500;
			}
		}

		text {
			margin: $uni-spacing-col-sm $uni-spacing-row-sm;
		}

		.name {
			font-size: $uni-font-size-base;
			font-weight: bold;
		}

		.label {
			display: inline-block;
			border-radius: $uni-border-radius-lg;
			background-color: $uni-bg-color;
			color: #5FE7C2;
			padding: 2px 6px;
			font-size: $uni-font-size-sm;
		}
	}

	.tu {
		color: $uni-text-color;
		font-weight: bold;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;

		.name {
			// color: $uni-text-color-grey;
			font-size: $uni-font-size-base;
			margin-left: $uni-spacing-col-base;
		}
	}

	.desc {
		// margin-top: $uni-spacing-col-sm;
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;

		.item {
			margin-bottom: $uni-spacing-col-sm;
		}

		navigator {
			color: #007AFF;
			display: inline;
		}
	}

	.pay {
		margin-top: $uni-spacing-col-sm;
		color: $uni-text-color;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-lg $uni-spacing-row-lg;
		display: flex;
		justify-content: space-between;
		flex-wrap: wrap;
		&::after {
			content: '';
			width: 30%;
		}

		.service {
			width: 30%;
			text-align: center;
			line-height: 50rpx;
			border: solid 1rpx #a1a1a1;
			color: #a1a1a1;
		}

		.chosed {
			color: $jian-bg-color;
			border-color: $jian-bg-color;
		}
	}

	.paytype {
		align-items: center;

		.alipay {
			width: $uni-img-size-lg;
			height: $uni-img-size-lg;
			margin-right: $uni-spacing-row-sm;
		}

		image {
			width: $uni-img-size-sm;
			height: $uni-img-size-sm;
		}

		.right {
			width: calc(100% - 30px);
		}

		// margin-top: $uni-spacing-col-sm;
		color: $uni-text-color-grey;
		font-size: $uni-font-size-base;
		background-color: $uni-bg-color;
		padding: $uni-spacing-col-base $uni-spacing-row-lg;
	}

	.select {
		padding: 0 $uni-spacing-row-lg;
		margin-top: $uni-spacing-col-sm;
		font-size: $uni-font-size-base;
	}

	button {
		background-color: #16CC9F !important;
		color: $uni-text-color-inverse !important;
		margin-top: 20px;
		margin-right: $uni-spacing-row-lg;
		margin-left: $uni-spacing-row-lg;
	}

	.buyButton {
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		text-align: center;
		height: 110upx;
		line-height: 60upx;
		position: fixed;
		bottom: 0upx;
		left: 0;
		right: 0;
		widht: 100%;
	}

	.emptyBtn {
		height: 90upx;
	}
</style>