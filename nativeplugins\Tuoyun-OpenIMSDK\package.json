{"name": "OpenIMSDK", "id": "Tuoyun-OpenIMSDK", "version": "*******", "description": "IM", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "Tuoyun-OpenIMSDK", "class": "com.example.openim.OpenIMSDK"}, {"type": "module", "name": "Tuoyun-OpenIMSDK-OUIFilePicker", "class": "io.openim.android.file_selector.FileSelector"}], "integrateType": "aar", "dependencies": [{"id": "com.google.android.material:material", "source": "implementation('com.google.android.material:material:1.4.0', {\r\nexclude group: 'androidx.lifecycle', module: 'lifecycle-viewmodel-ktx'\r\n})"}, {"id": "androidx.activity:activity", "source": "implementation('androidx.activity:activity:1.2.4', {\r\nexclude group: 'androidx.lifecycle', module: 'lifecycle-viewmodel-ktx'\r\n})"}, {"id": "androidx.activity:activity-ktx", "source": "implementation('androidx.activity:activity-ktx:1.3.1', {\r\nexclude group: 'androidx.lifecycle', module: 'lifecycle-viewmodel-ktx'\r\n})"}, {"id": "androidx.fragment:fragment-ktx", "source": "implementation('androidx.fragment:fragment-ktx:1.3.6', {\r\nexclude group: 'androidx.lifecycle', module: 'lifecycle-viewmodel-ktx'\r\n})"}, {"id": "com.github.bumptech.glide:glide", "source": "implementation('com.github.bumptech.glide:glide:4.13.0', {\r\nexclude group: 'androidx.lifecycle', module: 'lifecycle-viewmodel-ktx'\r\n})"}]}, "ios": {"plugins": [{"type": "module", "name": "Tuoyun-OpenIMSDK", "class": "OpenIMModule"}, {"type": "module", "name": "Tuoyun-OpenIMSDK-OUIFilePicker", "class": "OUIFilePicker"}], "integrateType": "framework", "validArchitectures": ["arm64"], "embedSwift": true}}}