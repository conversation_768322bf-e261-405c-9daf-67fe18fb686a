<template>
	<view>
		<map 
		class="map"
		:latitude="my_latitude" 
		:longitude="my_longitude"
		>
		</map>
		<view class="navto">
			<text>{{hospitalInfo.name}}</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				my_longitude:0, //当前经度
				my_latitude:0,  //当前纬度 
				maker:{
					id:1,
					latitude:0,
					longitude:0,
					iconPath:'../../static/img/loaction.png'
				},
				polyline:{
					points:[{latitude: 0, longitude: 0}],
					color:'#16CC9F'
				},
				hospitalInfo:{
					longitude:0,
					longitude:0,
					name:'武汉医院'
				}
			};
		},
		mounted(){
			let that = this
			uni.getLocation({
				type: 'wgs84',
				success: function (res) {
					console.log(res)
					that.my_longitude = res.longitude
					that.my_latitude = res.latitude
					that.maker.latitude = res.latitude
					that.maker.longitude = res.longitude
				},
				fail: function (res){
				}
			})
		}
	}
</script>

<style lang="scss">
	.map{
		width: 100%;
		position: absolute;
		height: 100%;
		z-index: -1;
	}
	.navto{
		position: absolute;
		width: 100%;
		bottom: 0;
		display: flex;
		background-color: rgba(0,0,0,.3);
		padding: 0 5%;
		box-sizing: border-box;
	}
</style>
