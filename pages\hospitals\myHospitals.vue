<template>
	<view>
		<view class="top-warp">
			<app-tabs v-model="tabIndex" :tabs="tabs"></app-tabs>
		</view>
		<Mycollect ref="mescrollItem0" :i="0" :index="tabIndex"></Mycollect>
		<!-- <Recently ref="mescrollItem1" :i="1" :index="tabIndex"></Recently> -->
		<History ref="mescrollItem2" :i="1" :index="tabIndex"></History>
	</view>
</template>

<script>
import AppTabs from "@/components/other/app-tabs.vue";
import Mycollect from "./myCollect.vue";
import Recently from "./Recently.vue"
import History from "./History.vue"
export default {

	components: {
		AppTabs,
		Mycollect,
		Recently,
		History
	},
	props: {
		
	},
	data() {
		return {
			tabs: ['收藏医院','最近看过'],
				tabIndex: 0, // 当前tab下标
		}
	}	
};
</script>

<style lang="scss">

</style>
